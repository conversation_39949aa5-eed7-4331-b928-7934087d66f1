# 🚀 Wit Mall 业务代码开发指南

## 📋 概述

本文档详细介绍如何在 Wit Mall 微服务框架基础上开发业务功能，包括标准的开发流程、代码规范和最佳实践。

## 🏗️ 开发流程

### 1. 选择服务模块
根据业务需求选择对应的服务模块进行开发：
- **用户相关** → `wit-user`
- **商品相关** → `wit-product`
- **订单相关** → `wit-order`
- **支付相关** → `wit-payment`
- 其他服务类推...

### 2. 标准开发步骤

#### 步骤1：数据库设计
```sql
-- 示例：用户表设计
CREATE TABLE `user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(100) NOT NULL COMMENT '密码',
    `email` VARCHAR(100) COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '手机号',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 步骤2：创建实体类
```java
// src/main/java/com/wit/user/entity/User.java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user")
@ApiModel("用户实体")
public class User extends BaseEntity {
    
    @ApiModelProperty("用户名")
    @TableField("username")
    private String username;
    
    @ApiModelProperty("密码")
    @TableField("password")
    private String password;
    
    @ApiModelProperty("邮箱")
    @TableField("email")
    private String email;
    
    @ApiModelProperty("手机号")
    @TableField("phone")
    private String phone;
    
    @ApiModelProperty("状态：1-正常，0-禁用")
    @TableField("status")
    private Integer status;
}
```

#### 步骤3：创建Mapper接口
```java
// src/main/java/com/wit/user/mapper/UserMapper.java
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM user WHERE username = #{username} AND deleted = 0")
    User findByUsername(@Param("username") String username);
    
    /**
     * 分页查询用户列表
     */
    IPage<User> selectUserPage(IPage<User> page, @Param("query") UserQueryDTO query);
}
```

#### 步骤4：创建Mapper XML（如需要）
```xml
<!-- src/main/resources/mapper/UserMapper.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wit.user.mapper.UserMapper">
    
    <select id="selectUserPage" resultType="com.wit.user.entity.User">
        SELECT * FROM user 
        WHERE deleted = 0
        <if test="query.username != null and query.username != ''">
            AND username LIKE CONCAT('%', #{query.username}, '%')
        </if>
        <if test="query.status != null">
            AND status = #{query.status}
        </if>
        ORDER BY create_time DESC
    </select>
    
</mapper>
```

#### 步骤5：创建DTO类
```java
// 查询DTO
@Data
@ApiModel("用户查询DTO")
public class UserQueryDTO {
    @ApiModelProperty("用户名")
    private String username;
    
    @ApiModelProperty("状态")
    private Integer status;
}

// 创建DTO
@Data
@ApiModel("用户创建DTO")
public class UserCreateDTO {
    @NotBlank(message = "用户名不能为空")
    @ApiModelProperty("用户名")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    @ApiModelProperty("密码")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    @ApiModelProperty("邮箱")
    private String email;
}

// 更新DTO
@Data
@ApiModel("用户更新DTO")
public class UserUpdateDTO {
    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty("用户ID")
    private Long id;
    
    @ApiModelProperty("邮箱")
    private String email;
    
    @ApiModelProperty("手机号")
    private String phone;
}
```

#### 步骤6：创建Service接口和实现
```java
// Service接口
public interface UserService extends IService<User> {
    
    /**
     * 创建用户
     */
    Result<Long> createUser(UserCreateDTO dto);
    
    /**
     * 更新用户
     */
    Result<Void> updateUser(UserUpdateDTO dto);
    
    /**
     * 分页查询用户
     */
    Result<IPage<User>> getUserPage(int current, int size, UserQueryDTO query);
    
    /**
     * 根据用户名查询用户
     */
    Result<User> getUserByUsername(String username);
}

// Service实现
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Long> createUser(UserCreateDTO dto) {
        // 1. 参数校验
        if (this.getUserByUsername(dto.getUsername()).getData() != null) {
            return Result.error("用户名已存在");
        }
        
        // 2. 密码加密
        String encodedPassword = passwordEncoder.encode(dto.getPassword());
        
        // 3. 创建用户
        User user = new User();
        BeanUtils.copyProperties(dto, user);
        user.setPassword(encodedPassword);
        user.setStatus(1);
        
        // 4. 保存到数据库
        boolean saved = this.save(user);
        if (!saved) {
            throw new BusinessException("用户创建失败");
        }
        
        log.info("用户创建成功，ID: {}", user.getId());
        return Result.success(user.getId());
    }
    
    @Override
    public Result<IPage<User>> getUserPage(int current, int size, UserQueryDTO query) {
        IPage<User> page = new Page<>(current, size);
        IPage<User> result = baseMapper.selectUserPage(page, query);
        return Result.success(result);
    }
    
    @Override
    public Result<User> getUserByUsername(String username) {
        User user = baseMapper.findByUsername(username);
        return Result.success(user);
    }
}
```

#### 步骤7：创建Controller
```java
@RestController
@RequestMapping("/api/v1/users")
@Api(tags = "用户管理")
@Slf4j
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @PostMapping
    @ApiOperation("创建用户")
    public Result<Long> createUser(@Valid @RequestBody UserCreateDTO dto) {
        return userService.createUser(dto);
    }
    
    @PutMapping
    @ApiOperation("更新用户")
    public Result<Void> updateUser(@Valid @RequestBody UserUpdateDTO dto) {
        return userService.updateUser(dto);
    }
    
    @GetMapping("/page")
    @ApiOperation("分页查询用户")
    public Result<IPage<User>> getUserPage(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            UserQueryDTO query) {
        return userService.getUserPage(current, size, query);
    }
    
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询用户")
    public Result<User> getUserById(@PathVariable Long id) {
        User user = userService.getById(id);
        return Result.success(user);
    }
    
    @DeleteMapping("/{id}")
    @ApiOperation("删除用户")
    public Result<Void> deleteUser(@PathVariable Long id) {
        boolean removed = userService.removeById(id);
        return removed ? Result.success() : Result.error("删除失败");
    }
}
```

## 🔧 开发规范

### 1. 命名规范
- **包名**：全小写，使用点分隔
- **类名**：大驼峰命名法（PascalCase）
- **方法名**：小驼峰命名法（camelCase）
- **常量**：全大写，下划线分隔

### 2. 注解使用
- **实体类**：`@Data`, `@TableName`, `@ApiModel`
- **控制器**：`@RestController`, `@RequestMapping`, `@Api`
- **服务类**：`@Service`, `@Transactional`
- **配置类**：`@Configuration`, `@EnableConfigurationProperties`

### 3. 异常处理
```java
// 业务异常
throw new BusinessException("用户不存在");
throw new BusinessException(ResultCode.USER_NOT_FOUND);

// 参数校验异常会被全局异常处理器自动处理
```

### 4. 日志规范
```java
@Slf4j
public class UserServiceImpl {
    
    public Result<User> createUser(UserCreateDTO dto) {
        log.info("开始创建用户，用户名: {}", dto.getUsername());
        
        try {
            // 业务逻辑
            log.info("用户创建成功，ID: {}", user.getId());
            return Result.success(user.getId());
        } catch (Exception e) {
            log.error("用户创建失败，用户名: {}, 错误: {}", dto.getUsername(), e.getMessage(), e);
            throw new BusinessException("用户创建失败");
        }
    }
}
```

## 🧪 测试开发

### 1. 单元测试
```java
@SpringBootTest
@Transactional
@Rollback
class UserServiceTest {
    
    @Autowired
    private UserService userService;
    
    @Test
    void testCreateUser() {
        UserCreateDTO dto = new UserCreateDTO();
        dto.setUsername("testuser");
        dto.setPassword("123456");
        dto.setEmail("<EMAIL>");
        
        Result<Long> result = userService.createUser(dto);
        
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNotNull();
    }
}
```

### 2. 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class UserControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testCreateUser() {
        UserCreateDTO dto = new UserCreateDTO();
        dto.setUsername("testuser");
        dto.setPassword("123456");
        
        ResponseEntity<Result> response = restTemplate.postForEntity(
            "/api/v1/users", dto, Result.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
    }
}
```

## 📝 API文档

使用Knife4j自动生成API文档，访问：`http://localhost:8082/doc.html`

## 🔄 服务间调用

### 1. Dubbo RPC调用
```java
// 定义接口
@DubboService
public interface UserRpcService {
    Result<User> getUserById(Long id);
}

// 实现接口
@DubboService
public class UserRpcServiceImpl implements UserRpcService {
    
    @Autowired
    private UserService userService;
    
    @Override
    public Result<User> getUserById(Long id) {
        return Result.success(userService.getById(id));
    }
}

// 调用服务
@DubboReference
private UserRpcService userRpcService;

public void someMethod() {
    Result<User> result = userRpcService.getUserById(1L);
}
```

### 2. HTTP调用（通过网关）
```java
@FeignClient(name = "wit-user", path = "/api/v1/users")
public interface UserFeignClient {
    
    @GetMapping("/{id}")
    Result<User> getUserById(@PathVariable Long id);
}
```

## 🎯 最佳实践

1. **数据库设计**：遵循三范式，合理使用索引
2. **事务管理**：在Service层使用@Transactional
3. **缓存使用**：合理使用Redis缓存热点数据
4. **异步处理**：使用RabbitMQ处理异步任务
5. **监控日志**：关键操作记录日志，便于排查问题
6. **安全考虑**：敏感数据加密，接口权限控制

## 🚀 下一步

1. 选择一个服务模块开始开发
2. 按照本指南创建完整的CRUD功能
3. 编写单元测试和集成测试
4. 完善API文档
5. 进行服务间调用测试

祝您开发顺利！🎉

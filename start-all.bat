@echo off
echo ========================================
echo    Wit Mall 微服务项目启动脚本
echo ========================================

echo.
echo 1. 启动基础设施服务...
echo 启动 Docker Compose 服务...
docker-compose up -d

echo.
echo 等待基础设施服务启动完成...
timeout /t 30 /nobreak

echo.
echo 2. 编译项目...
call mvn clean compile -DskipTests

echo.
echo 3. 启动微服务...

echo 启动网关服务...
start "wit-gateway" cmd /k "cd wit-gateway && mvn spring-boot:run"
timeout /t 10 /nobreak

echo 启动认证服务...
start "wit-auth" cmd /k "cd wit-auth && mvn spring-boot:run"
timeout /t 10 /nobreak

echo 启动用户服务...
start "wit-user" cmd /k "cd wit-user && mvn spring-boot:run"
timeout /t 5 /nobreak

echo 启动商品服务...
start "wit-product" cmd /k "cd wit-product && mvn spring-boot:run"
timeout /t 5 /nobreak

echo 启动文件服务...
start "wit-file" cmd /k "cd wit-file && mvn spring-boot:run"
timeout /t 5 /nobreak

echo 启动定时任务服务...
start "wit-schedule" cmd /k "cd wit-schedule && mvn spring-boot:run"

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo.
echo 访问地址：
echo 网关服务: http://localhost:8080
echo Nacos控制台: http://localhost:8848/nacos (nacos/nacos)
echo Sentinel控制台: http://localhost:8858 (sentinel/sentinel)
echo RabbitMQ控制台: http://localhost:15672 (admin/admin123)
echo MinIO控制台: http://localhost:9001 (minioadmin/minioadmin)
echo Kibana: http://localhost:5601
echo XXL-Job控制台: http://localhost:8080/xxl-job-admin (admin/123456)
echo.
echo 按任意键退出...
pause

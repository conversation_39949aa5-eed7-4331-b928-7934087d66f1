@echo off
echo ========================================
echo    启动网关服务 (本地开发模式)
echo ========================================

echo.
echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java环境未配置，请安装JDK 17+
    pause
    exit /b 1
)

echo.
echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo ❌ Maven环境未配置，请安装Maven
    pause
    exit /b 1
)

echo.
echo 检查端口8080是否被占用...
netstat -ano | findstr :8080
if %errorlevel% equ 0 (
    echo ⚠️  端口8080已被占用，请先关闭占用进程
    echo 可以使用以下命令查看占用进程：
    echo netstat -ano ^| findstr :8080
    echo 然后使用以下命令关闭进程：
    echo taskkill /PID ^<进程ID^> /F
    pause
    exit /b 1
)

echo.
echo 🚀 启动网关服务...
echo 使用本地配置文件：application-local.yml
echo 服务地址：http://localhost:8080

cd wit-gateway
mvn spring-boot:run -Dspring-boot.run.profiles=local

echo.
echo 网关服务已启动！
echo 健康检查：http://localhost:8080/gateway/health
echo 网关信息：http://localhost:8080/gateway/info
echo.
pause

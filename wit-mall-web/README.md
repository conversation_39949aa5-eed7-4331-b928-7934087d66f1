# 🛍️ Wit Mall Web - 前端项目

[![Vue](https://img.shields.io/badge/Vue-3.3+-brightgreen.svg)](https://vuejs.org/)
[![Vite](https://img.shields.io/badge/Vite-5.0+-blue.svg)](https://vitejs.dev/)
[![Element Plus](https://img.shields.io/badge/Element%20Plus-2.4+-blue.svg)](https://element-plus.org/)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)

## 📋 项目简介

Wit Mall Web 是 Wit Mall 微服务电商平台的前端项目，基于 Vue 3 + Vite + Element Plus 构建的现代化单页应用。

### ✨ 技术特色
- 🚀 **Vue 3** - 使用 Composition API，性能更优，开发体验更好
- ⚡ **Vite** - 极速的构建工具，热更新快如闪电
- 🎨 **Element Plus** - 基于 Vue 3 的组件库，界面美观易用
- 📱 **响应式设计** - 完美适配桌面端、平板和移动端
- 🔧 **TypeScript 友好** - 虽然使用 JavaScript，但支持渐进式 TypeScript 迁移
- 📦 **自动导入** - 组件和 API 自动导入，开发更高效

## 🛠️ 技术栈

### 核心框架
- **Vue 3.3+** - 渐进式 JavaScript 框架
- **Vite 5.0+** - 下一代前端构建工具
- **Vue Router 4.x** - Vue.js 官方路由管理器
- **Pinia** - Vue 的状态管理库

### UI 组件库
- **Element Plus 2.4+** - 基于 Vue 3 的桌面端组件库
- **@element-plus/icons-vue** - Element Plus 图标库

### 工具库
- **Axios** - HTTP 客户端
- **Day.js** - 轻量级日期处理库
- **Lodash-ES** - JavaScript 实用工具库
- **NProgress** - 页面加载进度条
- **js-cookie** - Cookie 操作库

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Sass** - CSS 预处理器
- **unplugin-auto-import** - 自动导入插件
- **unplugin-vue-components** - 组件自动导入

## 📁 项目结构

```
wit-mall-web/
├── public/                     # 静态资源
│   ├── favicon.ico            # 网站图标
│   └── logo.png               # 应用 Logo
├── src/                       # 源代码
│   ├── api/                   # API 接口
│   │   ├── auth.js           # 认证相关接口
│   │   ├── request.js        # Axios 配置
│   │   └── system.js         # 系统相关接口
│   ├── assets/               # 静态资源
│   │   ├── images/           # 图片资源
│   │   └── styles/           # 样式文件
│   │       ├── index.scss    # 全局样式
│   │       └── variables.scss # SCSS 变量
│   ├── components/           # 公共组件
│   ├── layouts/              # 布局组件
│   ├── router/               # 路由配置
│   │   └── index.js         # 路由主文件
│   ├── stores/               # 状态管理
│   │   ├── app.js           # 应用状态
│   │   └── user.js          # 用户状态
│   ├── utils/                # 工具函数
│   │   ├── auth.js          # 认证工具
│   │   ├── format.js        # 格式化工具
│   │   └── global.js        # 全局配置
│   ├── views/                # 页面组件
│   │   └── Home.vue         # 首页
│   ├── App.vue               # 根组件
│   └── main.js               # 入口文件
├── .env                      # 环境变量（默认）
├── .env.development          # 开发环境变量
├── .env.production           # 生产环境变量
├── .eslintrc.cjs            # ESLint 配置
├── .prettierrc              # Prettier 配置
├── .gitignore               # Git 忽略文件
├── index.html               # HTML 模板
├── package.json             # 项目配置
├── vite.config.js           # Vite 配置
└── README.md                # 项目说明
```

## 🚀 快速开始

### 环境要求
- **Node.js** >= 16.0.0
- **npm** >= 8.0.0 或 **yarn** >= 1.22.0

### 安装依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

### 启动开发服务器
```bash
# 启动开发服务器
npm run dev

# 或
yarn dev
```

访问 http://localhost:3000 查看应用

### 构建生产版本
```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

### 代码检查和格式化
```bash
# ESLint 检查
npm run lint

# 代码格式化
npm run format
```

## 🔧 开发指南

### 添加新页面
1. 在 `src/views/` 目录下创建 Vue 组件
2. 在 `src/router/index.js` 中添加路由配置
3. 如需要，在导航菜单中添加链接

### 添加新的 API 接口
1. 在 `src/api/` 目录下创建或编辑相应的 API 文件
2. 使用统一的 `request` 实例发送请求
3. 遵循项目的错误处理规范

### 状态管理
使用 Pinia 进行状态管理：
```javascript
// 定义 store
export const useExampleStore = defineStore('example', () => {
  const count = ref(0)
  const increment = () => count.value++
  
  return { count, increment }
})

// 在组件中使用
const store = useExampleStore()
```

### 样式开发
- 使用 SCSS 预处理器
- 遵循 BEM 命名规范
- 使用 CSS 变量支持主题切换
- 响应式设计优先

## 📱 浏览器支持

- Chrome >= 80
- Firefox >= 75
- Safari >= 13
- Edge >= 80

## 🔗 相关链接

- [Vue 3 文档](https://vuejs.org/)
- [Vite 文档](https://vitejs.dev/)
- [Element Plus 文档](https://element-plus.org/)
- [Vue Router 文档](https://router.vuejs.org/)
- [Pinia 文档](https://pinia.vuejs.org/)

## 📄 许可证

本项目基于 [Apache License 2.0](LICENSE) 许可证开源。

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 联系我们

- 项目地址：https://github.com/your-username/wit-mall-web
- 问题反馈：https://github.com/your-username/wit-mall-web/issues
- 邮箱：<EMAIL>

---

⭐ 如果这个项目对您有帮助，请给我们一个Star！

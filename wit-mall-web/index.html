<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <!-- 基础元信息 -->
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  
  <!-- 页面标题和描述 -->
  <title>Wit Mall - 微服务电商平台</title>
  <meta name="description" content="基于 Vue 3 + Spring Cloud Alibaba 的现代化微服务电商平台" />
  <meta name="keywords" content="电商平台,微服务,Vue3,Spring Cloud,在线购物" />
  <meta name="author" content="Wit" />
  
  <!-- Open Graph 元数据（社交媒体分享） -->
  <meta property="og:title" content="Wit Mall - 微服务电商平台" />
  <meta property="og:description" content="基于 Vue 3 + Spring Cloud Alibaba 的现代化微服务电商平台" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://witmall.com" />
  <meta property="og:image" content="/og-image.png" />
  <meta property="og:site_name" content="Wit Mall" />
  <meta property="og:locale" content="zh_CN" />
  
  <!-- Twitter Card 元数据 -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="Wit Mall - 微服务电商平台" />
  <meta name="twitter:description" content="基于 Vue 3 + Spring Cloud Alibaba 的现代化微服务电商平台" />
  <meta name="twitter:image" content="/twitter-image.png" />
  
  <!-- 网站图标 -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  <link rel="manifest" href="/manifest.json" />
  
  <!-- 主题颜色 -->
  <meta name="theme-color" content="#409eff" />
  <meta name="msapplication-TileColor" content="#409eff" />
  <meta name="msapplication-config" content="/browserconfig.xml" />
  
  <!-- 安全策略 -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:;" />
  
  <!-- DNS 预解析 -->
  <link rel="dns-prefetch" href="//api.witmall.com" />
  <link rel="dns-prefetch" href="//cdn.witmall.com" />
  
  <!-- 预加载关键资源 -->
  <link rel="preload" href="/fonts/main.woff2" as="font" type="font/woff2" crossorigin />
  
  <!-- 样式预加载 -->
  <style>
    /* 页面加载时的基础样式，避免闪烁 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html, body {
      height: 100%;
      font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background-color: #f5f7fa;
    }
    
    #app {
      height: 100%;
    }
    
    /* 加载动画 */
    .app-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    
    .loading-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
      background: url('/logo.png') no-repeat center;
      background-size: contain;
      animation: pulse 2s infinite;
    }
    
    .loading-text {
      font-size: 16px;
      color: #409eff;
      margin-bottom: 20px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #409eff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    @keyframes pulse {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.1); opacity: 0.8; }
      100% { transform: scale(1); opacity: 1; }
    }
    
    /* 隐藏加载动画的类 */
    .app-loading.hidden {
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease, visibility 0.3s ease;
    }
    
    /* 暗色主题支持 */
    @media (prefers-color-scheme: dark) {
      html, body {
        background-color: #1a1a1a;
        color: #ffffff;
      }
      
      .app-loading {
        background: #1a1a1a;
      }
      
      .loading-text {
        color: #79bbff;
      }
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .loading-logo {
        width: 60px;
        height: 60px;
      }
      
      .loading-text {
        font-size: 14px;
      }
      
      .loading-spinner {
        width: 30px;
        height: 30px;
        border-width: 2px;
      }
    }
  </style>
</head>

<body>
  <!-- 应用根节点 -->
  <div id="app">
    <!-- 应用加载动画 -->
    <div id="app-loading" class="app-loading">
      <div class="loading-logo"></div>
      <div class="loading-text">Wit Mall 正在加载中...</div>
      <div class="loading-spinner"></div>
    </div>
  </div>

  <!-- 应用脚本 -->
  <script type="module" src="/src/main.js"></script>
  
  <!-- 加载完成后隐藏加载动画 -->
  <script>
    // 监听应用加载完成事件
    window.addEventListener('load', function() {
      // 延迟隐藏加载动画，确保应用完全渲染
      setTimeout(function() {
        const loadingElement = document.getElementById('app-loading');
        if (loadingElement) {
          loadingElement.classList.add('hidden');
          // 动画结束后移除元素
          setTimeout(function() {
            loadingElement.remove();
          }, 300);
        }
      }, 500);
    });
    
    // 错误处理
    window.addEventListener('error', function(event) {
      console.error('应用加载错误:', event.error);
      
      // 显示错误信息
      const loadingElement = document.getElementById('app-loading');
      if (loadingElement) {
        const textElement = loadingElement.querySelector('.loading-text');
        const spinnerElement = loadingElement.querySelector('.loading-spinner');
        
        if (textElement) {
          textElement.textContent = '应用加载失败，请刷新页面重试';
          textElement.style.color = '#f56c6c';
        }
        
        if (spinnerElement) {
          spinnerElement.style.display = 'none';
        }
      }
    });
    
    // 检测浏览器兼容性
    (function() {
      const isModernBrowser = (
        'fetch' in window &&
        'Promise' in window &&
        'assign' in Object &&
        'keys' in Object &&
        'addEventListener' in window
      );
      
      if (!isModernBrowser) {
        alert('您的浏览器版本过低，请升级到最新版本以获得最佳体验。\n\n推荐使用：Chrome 80+、Firefox 75+、Safari 13+、Edge 80+');
      }
    })();
    
    // 性能监控
    if ('performance' in window && 'getEntriesByType' in performance) {
      window.addEventListener('load', function() {
        setTimeout(function() {
          const navigation = performance.getEntriesByType('navigation')[0];
          if (navigation) {
            console.log('页面加载性能:', {
              DNS解析: Math.round(navigation.domainLookupEnd - navigation.domainLookupStart) + 'ms',
              TCP连接: Math.round(navigation.connectEnd - navigation.connectStart) + 'ms',
              请求响应: Math.round(navigation.responseEnd - navigation.requestStart) + 'ms',
              DOM解析: Math.round(navigation.domContentLoadedEventEnd - navigation.responseEnd) + 'ms',
              总加载时间: Math.round(navigation.loadEventEnd - navigation.navigationStart) + 'ms'
            });
          }
        }, 0);
      });
    }
  </script>
  
  <!-- 百度统计（如果需要） -->
  <!-- 
  <script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?YOUR_BAIDU_ANALYTICS_ID";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
  </script>
  -->
  
  <!-- Google Analytics（如果需要） -->
  <!--
  <script async src="https://www.googletagmanager.com/gtag/js?id=YOUR_GA_ID"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'YOUR_GA_ID');
  </script>
  -->
</body>
</html>

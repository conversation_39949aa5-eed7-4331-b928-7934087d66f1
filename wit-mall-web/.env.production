# 生产环境配置
# 
# 说明：
# 1. 仅在生产环境（npm run build）时加载
# 2. 会覆盖 .env 中的同名变量
# 
# <AUTHOR>

# 环境标识
NODE_ENV=production
VITE_MODE=production

# API 配置（生产环境地址）
VITE_API_BASE_URL=https://api.witmall.com/api
VITE_UPLOAD_URL=https://api.witmall.com/upload

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_CONSOLE=false
VITE_ENABLE_PWA=true
VITE_ENABLE_ANALYTICS=true

# 调试配置
VITE_DEBUG=false
VITE_LOG_LEVEL=error

# CDN 配置
VITE_CDN_URL=https://cdn.witmall.com

# 分析工具
VITE_GOOGLE_ANALYTICS_ID=
VITE_BAIDU_ANALYTICS_ID=

{"name": "wit-mall-web", "version": "1.0.0", "description": "Wit Mall 微服务电商平台前端项目", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "keywords": ["vue3", "vite", "javascript", "element-plus", "pinia", "vue-router", "axios", "sass", "电商", "微服务"], "author": "Wit", "license": "Apache-2.0", "dependencies": {"vue": "^3.3.11", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "axios": "^1.6.2", "@element-plus/icons-vue": "^2.3.1", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "pinia-plugin-persistedstate": "^3.2.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "sass": "^1.69.5", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.1", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite-plugin-eslint": "^1.8.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}
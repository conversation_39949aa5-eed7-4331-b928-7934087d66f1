/**
 * ESLint 配置文件
 * 
 * 主要功能：
 * 1. 代码质量检查
 * 2. 代码风格统一
 * 3. Vue 3 语法支持
 * 4. 自动修复功能
 * 
 * <AUTHOR>
 */

module.exports = {
  // 环境配置
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  
  // 扩展配置
  extends: [
    'eslint:recommended',
    '@vue/eslint-config-prettier',
    'plugin:vue/vue3-essential',
    'plugin:vue/vue3-strongly-recommended',
    'plugin:vue/vue3-recommended'
  ],
  
  // 解析器配置
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  
  // 插件配置
  plugins: [
    'vue'
  ],
  
  // 全局变量
  globals: {
    // Element Plus 自动导入的组件
    ElMessage: 'readonly',
    ElMessageBox: 'readonly',
    ElNotification: 'readonly',
    ElLoading: 'readonly',
    
    // Vue 自动导入的 API
    ref: 'readonly',
    reactive: 'readonly',
    computed: 'readonly',
    watch: 'readonly',
    watchEffect: 'readonly',
    onMounted: 'readonly',
    onUnmounted: 'readonly',
    nextTick: 'readonly',
    
    // 路由相关
    useRouter: 'readonly',
    useRoute: 'readonly',
    
    // 状态管理
    defineStore: 'readonly',
    storeToRefs: 'readonly'
  },
  
  // 规则配置
  rules: {
    // Vue 相关规则
    'vue/multi-word-component-names': 'off', // 允许单词组件名
    'vue/no-unused-vars': 'error', // 禁止未使用的变量
    'vue/no-unused-components': 'warn', // 警告未使用的组件
    'vue/require-default-prop': 'off', // 不强制要求默认属性
    'vue/require-prop-types': 'warn', // 警告缺少属性类型
    'vue/prefer-import-from-vue': 'error', // 优先从 vue 导入
    'vue/no-v-html': 'warn', // 警告使用 v-html
    'vue/component-tags-order': ['error', {
      'order': ['script', 'template', 'style']
    }], // 组件标签顺序
    'vue/block-tag-newline': ['error', {
      'singleline': 'always',
      'multiline': 'always'
    }], // 块标签换行
    'vue/component-name-in-template-casing': ['error', 'PascalCase'], // 模板中组件名大小写
    'vue/custom-event-name-casing': ['error', 'camelCase'], // 自定义事件命名
    'vue/html-comment-content-spacing': ['error', 'always'], // HTML 注释间距
    'vue/no-potential-component-option-typo': 'warn', // 组件选项拼写检查
    'vue/no-reserved-component-names': 'error', // 禁止保留组件名
    'vue/no-static-inline-styles': 'warn', // 警告静态内联样式
    'vue/padding-line-between-blocks': ['error', 'always'], // 块之间的空行
    'vue/prefer-separate-static-class': 'error', // 优先分离静态类
    
    // JavaScript 基础规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off', // 生产环境警告 console
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off', // 生产环境警告 debugger
    'no-unused-vars': ['error', { 
      'argsIgnorePattern': '^_',
      'varsIgnorePattern': '^_'
    }], // 未使用变量（忽略下划线开头）
    'no-undef': 'error', // 禁止未定义变量
    'no-var': 'error', // 禁止使用 var
    'prefer-const': 'error', // 优先使用 const
    'prefer-arrow-callback': 'error', // 优先使用箭头函数
    'arrow-spacing': 'error', // 箭头函数间距
    'comma-dangle': ['error', 'never'], // 禁止尾随逗号
    'comma-spacing': 'error', // 逗号间距
    'comma-style': 'error', // 逗号风格
    'key-spacing': 'error', // 键值间距
    'keyword-spacing': 'error', // 关键字间距
    'object-curly-spacing': ['error', 'always'], // 对象花括号间距
    'array-bracket-spacing': ['error', 'never'], // 数组方括号间距
    'space-before-blocks': 'error', // 块前空格
    'space-before-function-paren': ['error', {
      'anonymous': 'always',
      'named': 'never',
      'asyncArrow': 'always'
    }], // 函数括号前空格
    'space-in-parens': ['error', 'never'], // 括号内空格
    'space-infix-ops': 'error', // 操作符间距
    'space-unary-ops': 'error', // 一元操作符间距
    'spaced-comment': ['error', 'always'], // 注释间距
    'template-curly-spacing': 'error', // 模板字符串间距
    'quotes': ['error', 'single'], // 使用单引号
    'semi': ['error', 'never'], // 不使用分号
    'indent': ['error', 2], // 2 空格缩进
    'linebreak-style': 'off', // 关闭换行符检查
    'max-len': ['warn', { 
      'code': 120,
      'ignoreUrls': true,
      'ignoreStrings': true,
      'ignoreTemplateLiterals': true
    }], // 最大行长度
    'no-multiple-empty-lines': ['error', { 
      'max': 2,
      'maxEOF': 1
    }], // 最大空行数
    'no-trailing-spaces': 'error', // 禁止尾随空格
    'eol-last': 'error', // 文件末尾换行
    
    // ES6+ 规则
    'prefer-template': 'error', // 优先使用模板字符串
    'template-curly-spacing': ['error', 'never'], // 模板字符串花括号间距
    'object-shorthand': 'error', // 对象简写
    'prefer-destructuring': ['error', {
      'array': false,
      'object': true
    }], // 优先使用解构
    'no-useless-rename': 'error', // 禁止无用重命名
    'rest-spread-spacing': 'error', // 剩余/扩展操作符间距
    
    // 错误预防
    'no-cond-assign': 'error', // 禁止条件赋值
    'no-constant-condition': 'error', // 禁止常量条件
    'no-dupe-args': 'error', // 禁止重复参数
    'no-dupe-keys': 'error', // 禁止重复键
    'no-duplicate-case': 'error', // 禁止重复 case
    'no-empty': 'error', // 禁止空块
    'no-ex-assign': 'error', // 禁止异常赋值
    'no-extra-boolean-cast': 'error', // 禁止多余布尔转换
    'no-extra-semi': 'error', // 禁止多余分号
    'no-func-assign': 'error', // 禁止函数赋值
    'no-inner-declarations': 'error', // 禁止内部声明
    'no-invalid-regexp': 'error', // 禁止无效正则
    'no-irregular-whitespace': 'error', // 禁止不规则空白
    'no-obj-calls': 'error', // 禁止对象调用
    'no-sparse-arrays': 'error', // 禁止稀疏数组
    'no-unreachable': 'error', // 禁止不可达代码
    'use-isnan': 'error', // 使用 isNaN
    'valid-typeof': 'error' // 有效的 typeof
  },
  
  // 忽略文件
  ignorePatterns: [
    'dist',
    'node_modules',
    '*.min.js',
    'public'
  ]
}

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import eslint from 'vite-plugin-eslint'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

/**
 * Vite 配置文件
 * 
 * 主要功能：
 * 1. Vue3 支持
 * 2. 路径别名配置
 * 3. 代理配置（连接后端API）
 * 4. Element Plus 自动导入
 * 5. ESLint 集成
 * 6. 开发服务器配置
 * 
 * <AUTHOR>
 */
export default defineConfig({
  // 插件配置
  plugins: [
    // Vue3 支持
    vue(),
    
    // ESLint 集成，开发时进行代码检查
    eslint({
      include: ['src/**/*.js', 'src/**/*.vue', 'src/*.js', 'src/*.vue']
    }),
    
    // 自动导入 Vue3 Composition API 和 Element Plus API
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: [
        'vue',
        'vue-router',
        'pinia',
        {
          'axios': [
            'default', // import { default as axios } from 'axios'
          ]
        }
      ],
      dts: true, // 生成类型声明文件
      eslintrc: {
        enabled: true, // 生成 ESLint 配置
      }
    }),
    
    // 自动导入 Element Plus 组件
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true // 生成类型声明文件
    })
  ],

  // 路径别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'), // @ 指向 src 目录
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@assets': resolve(__dirname, 'src/assets'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@api': resolve(__dirname, 'src/api'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@layouts': resolve(__dirname, 'src/layouts')
    }
  },

  // CSS 预处理器配置
  css: {
    preprocessorOptions: {
      scss: {
        // 全局 SCSS 变量和混入
        additionalData: `@import "@/assets/styles/variables.scss";`
      }
    }
  },

  // 开发服务器配置
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 3000, // 开发服务器端口
    open: true, // 自动打开浏览器
    cors: true, // 允许跨域
    
    // 代理配置 - 将前端请求代理到后端服务
    proxy: {
      // 代理所有 /api 请求到后端网关
      '/api': {
        target: 'http://localhost:8080', // 后端网关地址
        changeOrigin: true, // 改变请求头中的 origin
        secure: false, // 如果是 https 接口，需要配置这个参数
        // rewrite: (path) => path.replace(/^\/api/, '') // 重写路径，去掉 /api 前缀
      },
      
      // 代理文件上传请求
      '/upload': {
        target: 'http://localhost:8093', // 文件服务地址
        changeOrigin: true,
        secure: false
      }
    }
  },

  // 构建配置
  build: {
    // 输出目录
    outDir: 'dist',
    
    // 静态资源目录
    assetsDir: 'assets',
    
    // 小于此阈值的导入或引用资源将内联为 base64 编码
    assetsInlineLimit: 4096,
    
    // 启用/禁用 CSS 代码拆分
    cssCodeSplit: true,
    
    // 构建后是否生成 source map 文件
    sourcemap: false,
    
    // 打包大小警告的限制（单位：KB）
    chunkSizeWarningLimit: 2000,
    
    // Rollup 打包配置
    rollupOptions: {
      output: {
        // 分包策略
        manualChunks: {
          // 将 Vue 相关库打包到 vue-vendor
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          // 将 Element Plus 相关库打包到 element-vendor
          'element-vendor': ['element-plus', '@element-plus/icons-vue'],
          // 将工具库打包到 utils-vendor
          'utils-vendor': ['axios', 'dayjs', 'lodash-es', 'js-cookie']
        }
      }
    }
  },

  // 预构建配置
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'element-plus',
      'axios',
      'dayjs',
      'lodash-es',
      'js-cookie'
    ]
  }
})

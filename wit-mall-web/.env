# 环境配置文件
# 
# 说明：
# 1. 这是默认环境配置，所有环境都会加载
# 2. 变量名必须以 VITE_ 开头才能在客户端代码中访问
# 3. 不要在这里存储敏感信息
# 
# <AUTHOR>

# 应用基础信息
VITE_APP_TITLE=Wit Mall
VITE_APP_DESCRIPTION=微服务电商平台
VITE_APP_VERSION=1.0.0

# API 配置
VITE_API_BASE_URL=/api
VITE_UPLOAD_URL=/api/system/upload

# 应用配置
VITE_APP_LOGO=/logo.png
VITE_APP_FAVICON=/favicon.ico

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_PWA=false
VITE_ENABLE_ANALYTICS=false

# 其他配置
VITE_DEFAULT_LANGUAGE=zh-CN
VITE_DEFAULT_THEME=light

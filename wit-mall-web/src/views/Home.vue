<!--
  首页组件
  
  主要功能：
  1. 展示平台介绍
  2. 显示主要功能模块
  3. 快速导航入口
  4. 系统状态展示
  
  <AUTHOR>
-->

<template>
  <div class="home-page">
    <!-- 头部横幅 -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          <el-icon class="hero-icon"><ShoppingBag /></el-icon>
          欢迎使用 Wit Mall
        </h1>
        <p class="hero-subtitle">
          基于 Vue 3 + Spring Cloud Alibaba 的现代化微服务电商平台
        </p>
        <div class="hero-actions">
          <el-button 
            type="primary" 
            size="large" 
            @click="navigateTo('/products')"
          >
            <el-icon><Shop /></el-icon>
            浏览商品
          </el-button>
          <el-button 
            size="large" 
            @click="navigateTo('/dashboard')"
            v-if="userStore.isLoggedIn"
          >
            <el-icon><User /></el-icon>
            个人中心
          </el-button>
          <el-button 
            size="large" 
            @click="navigateTo('/login')"
            v-else
          >
            <el-icon><User /></el-icon>
            立即登录
          </el-button>
        </div>
      </div>
      <div class="hero-image">
        <el-image 
          src="/hero-image.png" 
          alt="Wit Mall"
          fit="contain"
          :preview-src-list="['/hero-image.png']"
        />
      </div>
    </section>

    <!-- 功能特性 -->
    <section class="features-section">
      <div class="section-header">
        <h2 class="section-title">平台特性</h2>
        <p class="section-subtitle">现代化的技术架构，完善的功能体系</p>
      </div>
      
      <div class="features-grid">
        <div 
          class="feature-card" 
          v-for="feature in features" 
          :key="feature.id"
        >
          <div class="feature-icon">
            <el-icon :size="32">
              <component :is="feature.icon" />
            </el-icon>
          </div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
        </div>
      </div>
    </section>

    <!-- 快速导航 -->
    <section class="navigation-section">
      <div class="section-header">
        <h2 class="section-title">快速导航</h2>
        <p class="section-subtitle">快速访问主要功能模块</p>
      </div>
      
      <div class="navigation-grid">
        <el-card 
          class="navigation-card"
          v-for="nav in navigations"
          :key="nav.id"
          shadow="hover"
          @click="navigateTo(nav.path)"
        >
          <div class="nav-content">
            <div class="nav-icon">
              <el-icon :size="24">
                <component :is="nav.icon" />
              </el-icon>
            </div>
            <div class="nav-info">
              <h4 class="nav-title">{{ nav.title }}</h4>
              <p class="nav-description">{{ nav.description }}</p>
            </div>
            <div class="nav-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>
      </div>
    </section>

    <!-- 系统状态 -->
    <section class="status-section" v-if="userStore.isAdmin">
      <div class="section-header">
        <h2 class="section-title">系统状态</h2>
        <p class="section-subtitle">实时监控系统运行状态</p>
      </div>
      
      <div class="status-grid">
        <el-card class="status-card" v-for="status in systemStatus" :key="status.id">
          <div class="status-content">
            <div class="status-icon" :class="status.status">
              <el-icon :size="20">
                <component :is="status.icon" />
              </el-icon>
            </div>
            <div class="status-info">
              <h4 class="status-title">{{ status.title }}</h4>
              <p class="status-value">{{ status.value }}</p>
            </div>
          </div>
        </el-card>
      </div>
    </section>

    <!-- 页脚信息 -->
    <section class="footer-section">
      <div class="footer-content">
        <div class="footer-info">
          <h3>Wit Mall</h3>
          <p>现代化微服务电商平台</p>
          <p>技术栈：Vue 3 + Spring Cloud Alibaba</p>
        </div>
        <div class="footer-links">
          <h4>相关链接</h4>
          <ul>
            <li><a href="#" @click.prevent>项目文档</a></li>
            <li><a href="#" @click.prevent>API 文档</a></li>
            <li><a href="#" @click.prevent>技术支持</a></li>
            <li><a href="#" @click.prevent>关于我们</a></li>
          </ul>
        </div>
        <div class="footer-contact">
          <h4>联系方式</h4>
          <p>邮箱：<EMAIL></p>
          <p>电话：400-123-4567</p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 Wit Mall. All rights reserved.</p>
      </div>
    </section>
  </div>
</template>

<script>
/**
 * 首页组件逻辑
 */
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { 
  ShoppingBag, 
  Shop, 
  User, 
  ArrowRight,
  Box,
  CreditCard,
  ShoppingCart,
  Star,
  Bell,
  Setting,
  DataAnalysis,
  Monitor,
  Connection,
  Cpu
} from '@element-plus/icons-vue'

export default {
  name: 'Home',
  
  components: {
    ShoppingBag,
    Shop,
    User,
    ArrowRight,
    Box,
    CreditCard,
    ShoppingCart,
    Star,
    Bell,
    Setting,
    DataAnalysis,
    Monitor,
    Connection,
    Cpu
  },
  
  setup() {
    // 路由和状态管理
    const router = useRouter()
    const userStore = useUserStore()
    const appStore = useAppStore()
    
    // 响应式数据
    const loading = ref(false)
    
    /**
     * 平台特性数据
     */
    const features = ref([
      {
        id: 1,
        icon: 'Box',
        title: '微服务架构',
        description: '基于 Spring Cloud Alibaba 构建的分布式系统，高可用、高并发'
      },
      {
        id: 2,
        icon: 'Monitor',
        title: '现代化前端',
        description: '采用 Vue 3 + Vite + Element Plus，提供优秀的用户体验'
      },
      {
        id: 3,
        icon: 'Connection',
        title: '完整生态',
        description: '集成 Nacos、Dubbo、Sentinel 等组件，构建完整的微服务生态'
      },
      {
        id: 4,
        icon: 'DataAnalysis',
        title: '数据驱动',
        description: '完善的监控体系和数据分析能力，助力业务决策'
      }
    ])
    
    /**
     * 快速导航数据
     */
    const navigations = ref([
      {
        id: 1,
        icon: 'Shop',
        title: '商品管理',
        description: '浏览和管理商品信息',
        path: '/products'
      },
      {
        id: 2,
        icon: 'ShoppingCart',
        title: '购物车',
        description: '查看购物车中的商品',
        path: '/cart'
      },
      {
        id: 3,
        icon: 'CreditCard',
        title: '我的订单',
        description: '查看订单状态和历史',
        path: '/orders'
      },
      {
        id: 4,
        icon: 'User',
        title: '个人中心',
        description: '管理个人信息和设置',
        path: '/dashboard'
      },
      {
        id: 5,
        icon: 'Star',
        title: '我的评价',
        description: '查看和管理商品评价',
        path: '/reviews'
      },
      {
        id: 6,
        icon: 'Bell',
        title: '消息通知',
        description: '查看系统消息和通知',
        path: '/notifications'
      }
    ])
    
    /**
     * 系统状态数据
     */
    const systemStatus = ref([
      {
        id: 1,
        icon: 'Monitor',
        title: '系统状态',
        value: '正常运行',
        status: 'success'
      },
      {
        id: 2,
        icon: 'Connection',
        title: '服务连接',
        value: '18/18',
        status: 'success'
      },
      {
        id: 3,
        icon: 'Cpu',
        title: 'CPU 使用率',
        value: '45%',
        status: 'normal'
      },
      {
        id: 4,
        icon: 'DataAnalysis',
        title: '内存使用',
        value: '2.1GB/8GB',
        status: 'normal'
      }
    ])
    
    /**
     * 导航到指定路径
     * @param {string} path - 路径
     */
    const navigateTo = (path) => {
      router.push(path)
    }
    
    /**
     * 初始化页面数据
     */
    const initPageData = async () => {
      try {
        loading.value = true
        
        // 这里可以加载首页需要的数据
        // 比如：轮播图、推荐商品、公告等
        
        console.log('首页数据加载完成')
      } catch (error) {
        console.error('首页数据加载失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    /**
     * 组件挂载后执行
     */
    onMounted(() => {
      initPageData()
    })
    
    return {
      // 状态管理
      userStore,
      appStore,
      
      // 响应式数据
      loading,
      features,
      navigations,
      systemStatus,
      
      // 方法
      navigateTo
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

// 头部横幅
.hero-section {
  display: flex;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
  color: white;
  
  .hero-content {
    flex: 1;
    max-width: 600px;
    
    .hero-title {
      font-size: 3rem;
      font-weight: bold;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      
      .hero-icon {
        font-size: 3rem;
      }
    }
    
    .hero-subtitle {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }
    
    .hero-actions {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }
  }
  
  .hero-image {
    flex: 1;
    text-align: center;
    
    .el-image {
      max-width: 500px;
      max-height: 400px;
    }
  }
}

// 通用区块样式
.features-section,
.navigation-section,
.status-section {
  padding: 4rem 2rem;
  background: white;
  
  .section-header {
    text-align: center;
    margin-bottom: 3rem;
    
    .section-title {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: #333;
    }
    
    .section-subtitle {
      font-size: 1rem;
      color: #666;
    }
  }
}

// 特性网格
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  
  .feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 8px;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .feature-icon {
      color: #409eff;
      margin-bottom: 1rem;
    }
    
    .feature-title {
      font-size: 1.2rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: #333;
    }
    
    .feature-description {
      color: #666;
      line-height: 1.6;
    }
  }
}

// 导航网格
.navigation-section {
  background: #f8f9fa;
}

.navigation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  
  .navigation-card {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .nav-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      
      .nav-icon {
        color: #409eff;
        flex-shrink: 0;
      }
      
      .nav-info {
        flex: 1;
        
        .nav-title {
          font-size: 1rem;
          font-weight: bold;
          margin-bottom: 0.25rem;
          color: #333;
        }
        
        .nav-description {
          font-size: 0.875rem;
          color: #666;
        }
      }
      
      .nav-arrow {
        color: #ccc;
        flex-shrink: 0;
      }
    }
  }
}

// 状态网格
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  
  .status-card {
    .status-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      
      .status-icon {
        padding: 0.5rem;
        border-radius: 50%;
        
        &.success {
          background: #f0f9ff;
          color: #67c23a;
        }
        
        &.normal {
          background: #fef7e0;
          color: #e6a23c;
        }
        
        &.error {
          background: #fef0f0;
          color: #f56c6c;
        }
      }
      
      .status-info {
        .status-title {
          font-size: 0.875rem;
          color: #666;
          margin-bottom: 0.25rem;
        }
        
        .status-value {
          font-size: 1.1rem;
          font-weight: bold;
          color: #333;
        }
      }
    }
  }
}

// 页脚
.footer-section {
  background: #2c3e50;
  color: white;
  padding: 3rem 2rem 1rem;
  
  .footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    
    .footer-info h3 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }
    
    .footer-links,
    .footer-contact {
      h4 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
      }
      
      ul {
        list-style: none;
        
        li {
          margin-bottom: 0.5rem;
          
          a {
            color: #bdc3c7;
            text-decoration: none;
            
            &:hover {
              color: white;
            }
          }
        }
      }
      
      p {
        margin-bottom: 0.5rem;
        color: #bdc3c7;
      }
    }
  }
  
  .footer-bottom {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hero-section {
    flex-direction: column;
    text-align: center;
    padding: 1rem;
    
    .hero-content .hero-title {
      font-size: 2rem;
    }
  }
  
  .features-section,
  .navigation-section,
  .status-section {
    padding: 2rem 1rem;
  }
  
  .navigation-grid {
    grid-template-columns: 1fr;
  }
}
</style>

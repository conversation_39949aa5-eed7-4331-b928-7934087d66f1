/**
 * Vue Router 路由配置
 * 
 * 主要功能：
 * 1. 定义应用路由规则
 * 2. 配置路由守卫
 * 3. 处理路由权限验证
 * 4. 管理页面访问控制
 * 
 * <AUTHOR>
 */

import { createRouter, createWebHistory } from 'vue-router'
import NProgress from 'nprogress'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'

/**
 * 路由配置
 */
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '首页',
      requiresAuth: false, // 是否需要登录
      keepAlive: true, // 是否缓存页面
      transition: 'fade' // 页面切换动画
    }
  },
  
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideInMenu: true, // 在菜单中隐藏
      transition: 'slide-right'
    }
  },
  
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '用户注册',
      requiresAuth: false,
      hideInMenu: true,
      transition: 'slide-left'
    }
  },
  
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layouts/DashboardLayout.vue'),
    meta: {
      title: '控制台',
      requiresAuth: true,
      roles: ['admin', 'user'] // 允许访问的角色
    },
    children: [
      {
        path: '',
        name: 'DashboardHome',
        component: () => import('@/views/dashboard/Home.vue'),
        meta: {
          title: '控制台首页',
          requiresAuth: true
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/dashboard/Profile.vue'),
        meta: {
          title: '个人资料',
          requiresAuth: true
        }
      }
    ]
  },
  
  {
    path: '/products',
    name: 'Products',
    component: () => import('@/views/product/ProductList.vue'),
    meta: {
      title: '商品列表',
      requiresAuth: false,
      keepAlive: true
    }
  },
  
  {
    path: '/products/:id',
    name: 'ProductDetail',
    component: () => import('@/views/product/ProductDetail.vue'),
    meta: {
      title: '商品详情',
      requiresAuth: false
    }
  },
  
  {
    path: '/cart',
    name: 'Cart',
    component: () => import('@/views/cart/Cart.vue'),
    meta: {
      title: '购物车',
      requiresAuth: true
    }
  },
  
  {
    path: '/orders',
    name: 'Orders',
    component: () => import('@/views/order/OrderList.vue'),
    meta: {
      title: '我的订单',
      requiresAuth: true
    }
  },
  
  {
    path: '/orders/:id',
    name: 'OrderDetail',
    component: () => import('@/views/order/OrderDetail.vue'),
    meta: {
      title: '订单详情',
      requiresAuth: true
    }
  },
  
  // 管理后台路由
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/layouts/AdminLayout.vue'),
    meta: {
      title: '管理后台',
      requiresAuth: true,
      roles: ['admin'] // 仅管理员可访问
    },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/Dashboard.vue'),
        meta: {
          title: '管理首页',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/UserManagement.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'products',
        name: 'AdminProducts',
        component: () => import('@/views/admin/ProductManagement.vue'),
        meta: {
          title: '商品管理',
          requiresAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'orders',
        name: 'AdminOrders',
        component: () => import('@/views/admin/OrderManagement.vue'),
        meta: {
          title: '订单管理',
          requiresAuth: true,
          roles: ['admin']
        }
      }
    ]
  },
  
  // 404 页面
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  },
  
  // 403 页面
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: '访问被拒绝',
      hideInMenu: true
    }
  },
  
  // 500 页面
  {
    path: '/500',
    name: 'ServerError',
    component: () => import('@/views/error/500.vue'),
    meta: {
      title: '服务器错误',
      hideInMenu: true
    }
  },
  
  // 重定向所有未匹配的路由到 404
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

/**
 * 创建路由实例
 */
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  
  // 滚动行为配置
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（浏览器前进/后退）
    if (savedPosition) {
      return savedPosition
    }
    
    // 如果有锚点
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }
    
    // 默认滚动到顶部
    return { top: 0 }
  }
})

/**
 * 全局前置守卫
 * 在每次路由跳转前执行
 */
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  // 获取状态管理实例
  const userStore = useUserStore()
  const appStore = useAppStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - Wit Mall`
  }
  
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    // 检查用户是否已登录
    if (!userStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath } // 保存原始路径，登录后跳转
      })
      return
    }
    
    // 检查用户角色权限
    if (to.meta.roles && to.meta.roles.length > 0) {
      const userRoles = userStore.user?.roles || []
      const hasPermission = to.meta.roles.some(role => userRoles.includes(role))
      
      if (!hasPermission) {
        // 无权限，跳转到 403 页面
        next('/403')
        return
      }
    }
  }
  
  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && userStore.isLoggedIn) {
    next('/')
    return
  }
  
  // 继续路由跳转
  next()
})

/**
 * 全局后置守卫
 * 在每次路由跳转后执行
 */
router.afterEach((to, from) => {
  // 结束进度条
  NProgress.done()
  
  // 记录路由访问日志（开发环境）
  if (import.meta.env.DEV) {
    console.log(`路由跳转: ${from.path} -> ${to.path}`)
  }
  
  // 这里可以添加页面访问统计
  // analytics.trackPageView(to.path)
})

/**
 * 路由错误处理
 */
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
  
  // 这里可以上报错误到监控系统
  // errorReporter.report(error)
})

export default router

/**
 * 应用全局状态管理
 * 
 * 主要功能：
 * 1. 管理应用主题设置
 * 2. 管理全局加载状态
 * 3. 管理应用配置信息
 * 4. 管理侧边栏状态
 * 5. 管理设备信息
 * 
 * <AUTHOR>
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getAppConfig } from '@/api/system'

export const useAppStore = defineStore('app', () => {
  // ==================== 状态定义 ====================
  
  /**
   * 主题设置
   */
  const theme = ref(localStorage.getItem('theme') || 'light')
  
  /**
   * 侧边栏状态
   */
  const sidebarCollapsed = ref(localStorage.getItem('sidebarCollapsed') === 'true')
  
  /**
   * 全局加载状态
   */
  const globalLoading = ref(false)
  
  /**
   * 设备类型
   */
  const device = ref('desktop') // desktop, tablet, mobile
  
  /**
   * 应用配置信息
   */
  const appConfig = ref({
    name: 'Wit Mall',
    version: '1.0.0',
    description: '微服务电商平台',
    logo: '/logo.png',
    copyright: '© 2024 Wit Mall. All rights reserved.',
    icp: '',
    contactEmail: '<EMAIL>',
    contactPhone: '************'
  })
  
  /**
   * 页面标签页列表（多标签页功能）
   */
  const visitedViews = ref([])
  
  /**
   * 缓存的页面列表
   */
  const cachedViews = ref([])
  
  // ==================== 计算属性 ====================
  
  /**
   * 是否为暗色主题
   */
  const isDark = computed(() => theme.value === 'dark')
  
  /**
   * 是否为移动设备
   */
  const isMobile = computed(() => device.value === 'mobile')
  
  /**
   * 是否为平板设备
   */
  const isTablet = computed(() => device.value === 'tablet')
  
  /**
   * 是否为桌面设备
   */
  const isDesktop = computed(() => device.value === 'desktop')
  
  // ==================== 方法定义 ====================
  
  /**
   * 设置主题
   * @param {string} newTheme - 主题名称 ('light' | 'dark')
   */
  const setTheme = (newTheme) => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    
    // 更新 HTML 根元素的 class
    document.documentElement.className = newTheme === 'dark' ? 'dark' : ''
    
    console.log(`主题已切换为: ${newTheme}`)
  }
  
  /**
   * 切换主题
   */
  const toggleTheme = () => {
    const newTheme = theme.value === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }
  
  /**
   * 设置侧边栏状态
   * @param {boolean} collapsed - 是否折叠
   */
  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', collapsed.toString())
  }
  
  /**
   * 切换侧边栏状态
   */
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed.value)
  }
  
  /**
   * 设置全局加载状态
   * @param {boolean} loading - 加载状态
   */
  const setGlobalLoading = (loading) => {
    globalLoading.value = loading
  }
  
  /**
   * 设置设备类型
   * @param {string} deviceType - 设备类型
   */
  const setDevice = (deviceType) => {
    device.value = deviceType
  }
  
  /**
   * 检测设备类型
   */
  const detectDevice = () => {
    const width = window.innerWidth
    
    if (width < 768) {
      setDevice('mobile')
    } else if (width < 1024) {
      setDevice('tablet')
    } else {
      setDevice('desktop')
    }
  }
  
  /**
   * 获取应用配置
   */
  const getAppConfigData = async () => {
    try {
      setGlobalLoading(true)
      const response = await getAppConfig()
      
      if (response.success) {
        appConfig.value = { ...appConfig.value, ...response.data }
      }
    } catch (error) {
      console.error('获取应用配置失败:', error)
    } finally {
      setGlobalLoading(false)
    }
  }
  
  /**
   * 添加访问的页面标签
   * @param {Object} view - 页面视图对象
   */
  const addVisitedView = (view) => {
    // 检查是否已存在
    const existingIndex = visitedViews.value.findIndex(v => v.path === view.path)
    
    if (existingIndex === -1) {
      visitedViews.value.push({
        name: view.name,
        path: view.path,
        title: view.meta?.title || view.name,
        meta: view.meta
      })
    } else {
      // 更新已存在的标签
      visitedViews.value[existingIndex] = {
        ...visitedViews.value[existingIndex],
        title: view.meta?.title || view.name,
        meta: view.meta
      }
    }
  }
  
  /**
   * 删除访问的页面标签
   * @param {Object} view - 页面视图对象
   */
  const delVisitedView = (view) => {
    const index = visitedViews.value.findIndex(v => v.path === view.path)
    if (index !== -1) {
      visitedViews.value.splice(index, 1)
    }
  }
  
  /**
   * 删除其他页面标签
   * @param {Object} activeView - 当前激活的页面视图
   */
  const delOthersVisitedViews = (activeView) => {
    visitedViews.value = visitedViews.value.filter(v => v.path === activeView.path)
  }
  
  /**
   * 删除所有页面标签
   */
  const delAllVisitedViews = () => {
    visitedViews.value = []
  }
  
  /**
   * 添加缓存页面
   * @param {string} name - 页面组件名称
   */
  const addCachedView = (name) => {
    if (!cachedViews.value.includes(name)) {
      cachedViews.value.push(name)
    }
  }
  
  /**
   * 删除缓存页面
   * @param {string} name - 页面组件名称
   */
  const delCachedView = (name) => {
    const index = cachedViews.value.indexOf(name)
    if (index !== -1) {
      cachedViews.value.splice(index, 1)
    }
  }
  
  /**
   * 清空所有缓存页面
   */
  const clearCachedViews = () => {
    cachedViews.value = []
  }
  
  /**
   * 初始化应用设置
   */
  const initApp = () => {
    // 检测设备类型
    detectDevice()
    
    // 监听窗口大小变化
    window.addEventListener('resize', detectDevice)
    
    // 获取应用配置
    getAppConfigData()
  }
  
  /**
   * 重置应用状态
   */
  const resetApp = () => {
    // 重置主题
    setTheme('light')
    
    // 重置侧边栏
    setSidebarCollapsed(false)
    
    // 清空标签页
    delAllVisitedViews()
    
    // 清空缓存
    clearCachedViews()
    
    // 重置加载状态
    setGlobalLoading(false)
  }
  
  // ==================== 返回状态和方法 ====================
  
  return {
    // 状态
    theme,
    sidebarCollapsed,
    globalLoading,
    device,
    appConfig,
    visitedViews,
    cachedViews,
    
    // 计算属性
    isDark,
    isMobile,
    isTablet,
    isDesktop,
    
    // 方法
    setTheme,
    toggleTheme,
    setSidebarCollapsed,
    toggleSidebar,
    setGlobalLoading,
    setDevice,
    detectDevice,
    getAppConfig: getAppConfigData,
    addVisitedView,
    delVisitedView,
    delOthersVisitedViews,
    delAllVisitedViews,
    addCachedView,
    delCachedView,
    clearCachedViews,
    initApp,
    resetApp
  }
})

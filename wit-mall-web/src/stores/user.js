/**
 * 用户状态管理
 * 
 * 主要功能：
 * 1. 管理用户登录状态
 * 2. 存储用户信息
 * 3. 处理用户认证
 * 4. 管理用户权限
 * 5. 处理登录登出逻辑
 * 
 * <AUTHOR>
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, logout, getUserInfo, refreshToken } from '@/api/auth'
import { removeToken, setToken, getToken } from '@/utils/auth'
import router from '@/router'

export const useUserStore = defineStore('user', () => {
  // ==================== 状态定义 ====================
  
  /**
   * 用户信息
   */
  const user = ref(null)
  
  /**
   * 访问令牌
   */
  const token = ref(getToken())
  
  /**
   * 刷新令牌
   */
  const refreshTokenValue = ref(localStorage.getItem('refreshToken'))
  
  /**
   * 用户权限列表
   */
  const permissions = ref([])
  
  /**
   * 用户角色列表
   */
  const roles = ref([])
  
  /**
   * 登录加载状态
   */
  const loginLoading = ref(false)
  
  // ==================== 计算属性 ====================
  
  /**
   * 是否已登录
   */
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  
  /**
   * 用户头像
   */
  const avatar = computed(() => user.value?.avatar || '/default-avatar.png')
  
  /**
   * 用户昵称
   */
  const nickname = computed(() => user.value?.nickname || user.value?.username || '未知用户')
  
  /**
   * 是否为管理员
   */
  const isAdmin = computed(() => roles.value.includes('admin'))
  
  /**
   * 是否为VIP用户
   */
  const isVip = computed(() => user.value?.vipLevel > 0)
  
  // ==================== 方法定义 ====================
  
  /**
   * 用户登录
   * @param {Object} loginForm - 登录表单数据
   * @param {string} loginForm.username - 用户名
   * @param {string} loginForm.password - 密码
   * @param {string} loginForm.captcha - 验证码
   * @returns {Promise<boolean>} 登录是否成功
   */
  const userLogin = async (loginForm) => {
    try {
      loginLoading.value = true
      
      const response = await login(loginForm)
      
      if (response.success) {
        // 保存令牌
        token.value = response.data.accessToken
        refreshTokenValue.value = response.data.refreshToken
        
        setToken(response.data.accessToken)
        localStorage.setItem('refreshToken', response.data.refreshToken)
        
        // 获取用户信息
        await getUserInfoData()
        
        console.log('用户登录成功:', user.value.username)
        return true
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loginLoading.value = false
    }
  }
  
  /**
   * 获取用户信息
   */
  const getUserInfoData = async () => {
    try {
      if (!token.value) {
        throw new Error('未找到访问令牌')
      }
      
      const response = await getUserInfo()
      
      if (response.success) {
        user.value = response.data.user
        permissions.value = response.data.permissions || []
        roles.value = response.data.roles || []
        
        console.log('获取用户信息成功:', user.value.username)
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是 token 过期，尝试刷新
      await tryRefreshToken()
    }
  }
  
  /**
   * 刷新访问令牌
   */
  const tryRefreshToken = async () => {
    try {
      if (!refreshTokenValue.value) {
        throw new Error('未找到刷新令牌')
      }
      
      const response = await refreshToken(refreshTokenValue.value)
      
      if (response.success) {
        // 更新令牌
        token.value = response.data.accessToken
        refreshTokenValue.value = response.data.refreshToken
        
        setToken(response.data.accessToken)
        localStorage.setItem('refreshToken', response.data.refreshToken)
        
        console.log('令牌刷新成功')
        
        // 重新获取用户信息
        await getUserInfoData()
      } else {
        throw new Error(response.message || '刷新令牌失败')
      }
    } catch (error) {
      console.error('刷新令牌失败:', error)
      // 刷新失败，清除所有认证信息
      await userLogout()
    }
  }
  
  /**
   * 用户登出
   * @param {boolean} callApi - 是否调用登出API
   */
  const userLogout = async (callApi = true) => {
    try {
      // 调用登出API
      if (callApi && token.value) {
        await logout()
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      // 清除本地状态
      clearUserData()
      
      // 跳转到登录页
      router.push('/login')
      
      console.log('用户已登出')
    }
  }
  
  /**
   * 清除用户数据
   */
  const clearUserData = () => {
    user.value = null
    token.value = null
    refreshTokenValue.value = null
    permissions.value = []
    roles.value = []
    
    // 清除本地存储
    removeToken()
    localStorage.removeItem('refreshToken')
  }
  
  /**
   * 更新用户信息
   * @param {Object} userData - 用户数据
   */
  const updateUserInfo = (userData) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
    }
  }
  
  /**
   * 检查用户权限
   * @param {string|Array} permission - 权限代码
   * @returns {boolean} 是否有权限
   */
  const hasPermission = (permission) => {
    if (!permission) return true
    
    if (Array.isArray(permission)) {
      return permission.some(p => permissions.value.includes(p))
    }
    
    return permissions.value.includes(permission)
  }
  
  /**
   * 检查用户角色
   * @param {string|Array} role - 角色代码
   * @returns {boolean} 是否有角色
   */
  const hasRole = (role) => {
    if (!role) return true
    
    if (Array.isArray(role)) {
      return role.some(r => roles.value.includes(r))
    }
    
    return roles.value.includes(role)
  }
  
  /**
   * 检查是否可以访问路由
   * @param {Object} route - 路由对象
   * @returns {boolean} 是否可以访问
   */
  const canAccessRoute = (route) => {
    // 检查是否需要登录
    if (route.meta?.requiresAuth && !isLoggedIn.value) {
      return false
    }
    
    // 检查角色权限
    if (route.meta?.roles && route.meta.roles.length > 0) {
      return hasRole(route.meta.roles)
    }
    
    // 检查权限
    if (route.meta?.permissions && route.meta.permissions.length > 0) {
      return hasPermission(route.meta.permissions)
    }
    
    return true
  }
  
  /**
   * 设置用户令牌（用于外部设置）
   * @param {string} newToken - 新的访问令牌
   * @param {string} newRefreshToken - 新的刷新令牌
   */
  const setUserToken = (newToken, newRefreshToken) => {
    token.value = newToken
    refreshTokenValue.value = newRefreshToken
    
    setToken(newToken)
    if (newRefreshToken) {
      localStorage.setItem('refreshToken', newRefreshToken)
    }
  }
  
  /**
   * 初始化用户状态（应用启动时调用）
   */
  const initUser = async () => {
    if (token.value) {
      try {
        await getUserInfoData()
      } catch (error) {
        console.error('初始化用户状态失败:', error)
        clearUserData()
      }
    }
  }
  
  // ==================== 返回状态和方法 ====================
  
  return {
    // 状态
    user,
    token,
    refreshTokenValue,
    permissions,
    roles,
    loginLoading,
    
    // 计算属性
    isLoggedIn,
    avatar,
    nickname,
    isAdmin,
    isVip,
    
    // 方法
    login: userLogin,
    logout: userLogout,
    getUserInfo: getUserInfoData,
    refreshToken: tryRefreshToken,
    updateUserInfo,
    hasPermission,
    hasRole,
    canAccessRoute,
    setUserToken,
    clearUserData,
    initUser
  }
})

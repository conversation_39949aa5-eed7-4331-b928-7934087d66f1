/**
 * 全局属性和方法配置
 * 
 * 主要功能：
 * 1. 配置全局属性
 * 2. 注册全局方法
 * 3. 设置全局常量
 * 4. 配置全局过滤器
 * 
 * <AUTHOR>
 */

import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import { formatCurrency, formatNumber, formatFileSize } from './format'
import { debounce, throttle } from 'lodash-es'

// 配置 dayjs
dayjs.locale('zh-cn')
dayjs.extend(relativeTime)

/**
 * 设置全局属性和方法
 * @param {Object} app - Vue 应用实例
 */
export const setupGlobalProperties = (app) => {
  // 注册 Element Plus 图标
  registerElementPlusIcons(app)
  
  // 注册全局属性
  registerGlobalProperties(app)
  
  // 注册全局方法
  registerGlobalMethods(app)
  
  // 注册全局常量
  registerGlobalConstants(app)
  
  // 注册全局过滤器（Vue 3 中使用全局属性替代）
  registerGlobalFilters(app)
}

/**
 * 注册 Element Plus 图标
 * @param {Object} app - Vue 应用实例
 */
const registerElementPlusIcons = (app) => {
  // 注册所有 Element Plus 图标为全局组件
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  
  console.log('✅ Element Plus 图标注册完成')
}

/**
 * 注册全局属性
 * @param {Object} app - Vue 应用实例
 */
const registerGlobalProperties = (app) => {
  // Element Plus 组件
  app.config.globalProperties.$message = ElMessage
  app.config.globalProperties.$messageBox = ElMessageBox
  app.config.globalProperties.$notification = ElNotification
  app.config.globalProperties.$loading = ElLoading
  
  // 日期时间处理
  app.config.globalProperties.$dayjs = dayjs
  
  // 工具函数
  app.config.globalProperties.$debounce = debounce
  app.config.globalProperties.$throttle = throttle
  
  console.log('✅ 全局属性注册完成')
}

/**
 * 注册全局方法
 * @param {Object} app - Vue 应用实例
 */
const registerGlobalMethods = (app) => {
  /**
   * 成功消息提示
   * @param {string} message - 消息内容
   * @param {Object} options - 选项
   */
  app.config.globalProperties.$success = (message, options = {}) => {
    ElMessage.success({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }
  
  /**
   * 错误消息提示
   * @param {string} message - 消息内容
   * @param {Object} options - 选项
   */
  app.config.globalProperties.$error = (message, options = {}) => {
    ElMessage.error({
      message,
      duration: 5000,
      showClose: true,
      ...options
    })
  }
  
  /**
   * 警告消息提示
   * @param {string} message - 消息内容
   * @param {Object} options - 选项
   */
  app.config.globalProperties.$warning = (message, options = {}) => {
    ElMessage.warning({
      message,
      duration: 4000,
      showClose: true,
      ...options
    })
  }
  
  /**
   * 信息消息提示
   * @param {string} message - 消息内容
   * @param {Object} options - 选项
   */
  app.config.globalProperties.$info = (message, options = {}) => {
    ElMessage.info({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }
  
  /**
   * 确认对话框
   * @param {string} message - 消息内容
   * @param {string} title - 标题
   * @param {Object} options - 选项
   * @returns {Promise} 确认结果
   */
  app.config.globalProperties.$confirm = (message, title = '提示', options = {}) => {
    return ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      ...options
    })
  }
  
  /**
   * 删除确认对话框
   * @param {string} message - 消息内容
   * @param {string} title - 标题
   * @returns {Promise} 确认结果
   */
  app.config.globalProperties.$confirmDelete = (message = '此操作将永久删除该数据，是否继续？', title = '删除确认') => {
    return ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error',
      confirmButtonClass: 'el-button--danger'
    })
  }
  
  /**
   * 输入对话框
   * @param {string} message - 消息内容
   * @param {string} title - 标题
   * @param {Object} options - 选项
   * @returns {Promise} 输入结果
   */
  app.config.globalProperties.$prompt = (message, title = '输入', options = {}) => {
    return ElMessageBox.prompt(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      ...options
    })
  }
  
  /**
   * 成功通知
   * @param {string} title - 标题
   * @param {string} message - 消息内容
   * @param {Object} options - 选项
   */
  app.config.globalProperties.$notifySuccess = (title, message = '', options = {}) => {
    ElNotification.success({
      title,
      message,
      duration: 4000,
      ...options
    })
  }
  
  /**
   * 错误通知
   * @param {string} title - 标题
   * @param {string} message - 消息内容
   * @param {Object} options - 选项
   */
  app.config.globalProperties.$notifyError = (title, message = '', options = {}) => {
    ElNotification.error({
      title,
      message,
      duration: 6000,
      ...options
    })
  }
  
  /**
   * 复制文本到剪贴板
   * @param {string} text - 要复制的文本
   * @returns {Promise<boolean>} 复制是否成功
   */
  app.config.globalProperties.$copyText = async (text) => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        // 现代浏览器的 Clipboard API
        await navigator.clipboard.writeText(text)
      } else {
        // 兼容旧浏览器的方法
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        document.execCommand('copy')
        textArea.remove()
      }
      
      ElMessage.success('复制成功')
      return true
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败')
      return false
    }
  }
  
  /**
   * 下载文件
   * @param {string} url - 文件URL
   * @param {string} filename - 文件名
   */
  app.config.globalProperties.$downloadFile = (url, filename) => {
    const link = document.createElement('a')
    link.href = url
    link.download = filename || 'download'
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  
  /**
   * 预览图片
   * @param {string|Array} images - 图片URL或URL数组
   * @param {number} initialIndex - 初始显示的图片索引
   */
  app.config.globalProperties.$previewImages = (images, initialIndex = 0) => {
    // 这里可以集成图片预览组件，如 el-image-viewer
    console.log('预览图片:', images, '初始索引:', initialIndex)
    // 实际项目中可以使用第三方图片预览库
  }
  
  console.log('✅ 全局方法注册完成')
}

/**
 * 注册全局常量
 * @param {Object} app - Vue 应用实例
 */
const registerGlobalConstants = (app) => {
  // 应用信息
  app.config.globalProperties.$APP_INFO = {
    name: 'Wit Mall',
    version: '1.0.0',
    description: '微服务电商平台'
  }
  
  // API 基础地址
  app.config.globalProperties.$API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'
  
  // 文件上传地址
  app.config.globalProperties.$UPLOAD_URL = `${app.config.globalProperties.$API_BASE_URL}/system/upload`
  
  // 默认头像
  app.config.globalProperties.$DEFAULT_AVATAR = '/default-avatar.png'
  
  // 默认图片
  app.config.globalProperties.$DEFAULT_IMAGE = '/default-image.png'
  
  // 分页配置
  app.config.globalProperties.$PAGE_SIZES = [10, 20, 50, 100]
  app.config.globalProperties.$DEFAULT_PAGE_SIZE = 20
  
  // 状态常量
  app.config.globalProperties.$STATUS = {
    ENABLED: 1,
    DISABLED: 0,
    DELETED: -1
  }
  
  // 用户类型
  app.config.globalProperties.$USER_TYPES = {
    ADMIN: 'admin',
    USER: 'user',
    VIP: 'vip',
    GUEST: 'guest'
  }
  
  // 订单状态
  app.config.globalProperties.$ORDER_STATUS = {
    PENDING: 'pending',
    PAID: 'paid',
    SHIPPED: 'shipped',
    DELIVERED: 'delivered',
    CANCELLED: 'cancelled',
    REFUNDED: 'refunded'
  }
  
  console.log('✅ 全局常量注册完成')
}

/**
 * 注册全局过滤器（Vue 3 中使用全局属性替代）
 * @param {Object} app - Vue 应用实例
 */
const registerGlobalFilters = (app) => {
  // 格式化货币
  app.config.globalProperties.$formatCurrency = formatCurrency
  
  // 格式化数字
  app.config.globalProperties.$formatNumber = formatNumber
  
  // 格式化文件大小
  app.config.globalProperties.$formatFileSize = formatFileSize
  
  // 格式化日期
  app.config.globalProperties.$formatDate = (date, format = 'YYYY-MM-DD') => {
    return date ? dayjs(date).format(format) : ''
  }
  
  // 格式化日期时间
  app.config.globalProperties.$formatDateTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
    return date ? dayjs(date).format(format) : ''
  }
  
  // 相对时间
  app.config.globalProperties.$fromNow = (date) => {
    return date ? dayjs(date).fromNow() : ''
  }
  
  // 截取文本
  app.config.globalProperties.$truncate = (text, length = 50, suffix = '...') => {
    if (!text) return ''
    return text.length > length ? text.substring(0, length) + suffix : text
  }
  
  // 首字母大写
  app.config.globalProperties.$capitalize = (text) => {
    if (!text) return ''
    return text.charAt(0).toUpperCase() + text.slice(1)
  }
  
  // 格式化状态
  app.config.globalProperties.$formatStatus = (status) => {
    const statusMap = {
      1: '启用',
      0: '禁用',
      '-1': '已删除',
      'active': '活跃',
      'inactive': '非活跃',
      'pending': '待处理',
      'processing': '处理中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || status
  }
  
  console.log('✅ 全局过滤器注册完成')
}

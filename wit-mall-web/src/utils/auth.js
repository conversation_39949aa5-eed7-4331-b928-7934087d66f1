/**
 * 认证工具函数
 * 
 * 主要功能：
 * 1. Token 存储管理
 * 2. 用户权限检查
 * 3. 登录状态管理
 * 
 * <AUTHOR>
 */

import Cookies from 'js-cookie'

/**
 * Token 存储键名
 */
const TOKEN_KEY = 'wit_mall_token'

/**
 * Token 过期时间（天）
 */
const TOKEN_EXPIRES = 7

/**
 * 获取 Token
 * @returns {string|null} Token 值
 */
export const getToken = () => {
  // 优先从 localStorage 获取
  let token = localStorage.getItem(TOKEN_KEY)
  
  // 如果 localStorage 中没有，尝试从 Cookie 获取
  if (!token) {
    token = Cookies.get(TOKEN_KEY)
  }
  
  return token
}

/**
 * 设置 Token
 * @param {string} token - Token 值
 * @param {boolean} remember - 是否记住登录状态
 */
export const setToken = (token, remember = true) => {
  if (!token) {
    console.warn('Token 不能为空')
    return
  }
  
  if (remember) {
    // 记住登录状态，存储到 localStorage 和 Cookie
    localStorage.setItem(TOKEN_KEY, token)
    Cookies.set(TOKEN_KEY, token, { expires: TOKEN_EXPIRES })
  } else {
    // 不记住登录状态，只存储到 sessionStorage
    sessionStorage.setItem(TOKEN_KEY, token)
  }
  
  console.log('Token 已保存:', remember ? '长期' : '会话')
}

/**
 * 移除 Token
 */
export const removeToken = () => {
  localStorage.removeItem(TOKEN_KEY)
  sessionStorage.removeItem(TOKEN_KEY)
  Cookies.remove(TOKEN_KEY)
  
  console.log('Token 已清除')
}

/**
 * 检查 Token 是否存在
 * @returns {boolean} Token 是否存在
 */
export const hasToken = () => {
  return !!getToken()
}

/**
 * 解析 JWT Token
 * @param {string} token - JWT Token
 * @returns {Object|null} 解析后的 Token 数据
 */
export const parseToken = (token) => {
  if (!token) {
    return null
  }
  
  try {
    // JWT Token 由三部分组成，用 . 分隔
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format')
    }
    
    // 解码 payload 部分
    const payload = parts[1]
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'))
    
    return JSON.parse(decoded)
  } catch (error) {
    console.error('Token 解析失败:', error)
    return null
  }
}

/**
 * 检查 Token 是否过期
 * @param {string} token - JWT Token
 * @returns {boolean} Token 是否过期
 */
export const isTokenExpired = (token) => {
  const tokenData = parseToken(token)
  
  if (!tokenData || !tokenData.exp) {
    return true
  }
  
  // JWT 的 exp 是以秒为单位的时间戳
  const expirationTime = tokenData.exp * 1000
  const currentTime = Date.now()
  
  return currentTime >= expirationTime
}

/**
 * 获取 Token 剩余有效时间（毫秒）
 * @param {string} token - JWT Token
 * @returns {number} 剩余有效时间，如果已过期返回 0
 */
export const getTokenRemainingTime = (token) => {
  const tokenData = parseToken(token)
  
  if (!tokenData || !tokenData.exp) {
    return 0
  }
  
  const expirationTime = tokenData.exp * 1000
  const currentTime = Date.now()
  const remainingTime = expirationTime - currentTime
  
  return Math.max(0, remainingTime)
}

/**
 * 检查 Token 是否即将过期（默认30分钟内）
 * @param {string} token - JWT Token
 * @param {number} threshold - 阈值时间（毫秒），默认30分钟
 * @returns {boolean} Token 是否即将过期
 */
export const isTokenExpiringSoon = (token, threshold = 30 * 60 * 1000) => {
  const remainingTime = getTokenRemainingTime(token)
  return remainingTime > 0 && remainingTime <= threshold
}

/**
 * 从 Token 中获取用户信息
 * @param {string} token - JWT Token
 * @returns {Object|null} 用户信息
 */
export const getUserFromToken = (token) => {
  const tokenData = parseToken(token)
  
  if (!tokenData) {
    return null
  }
  
  return {
    id: tokenData.sub || tokenData.userId,
    username: tokenData.username,
    email: tokenData.email,
    roles: tokenData.roles || [],
    permissions: tokenData.permissions || [],
    exp: tokenData.exp,
    iat: tokenData.iat
  }
}

/**
 * 检查用户是否有指定权限
 * @param {Array} userPermissions - 用户权限列表
 * @param {string|Array} requiredPermissions - 需要的权限
 * @returns {boolean} 是否有权限
 */
export const hasPermission = (userPermissions, requiredPermissions) => {
  if (!requiredPermissions) {
    return true
  }
  
  if (!userPermissions || userPermissions.length === 0) {
    return false
  }
  
  // 如果是数组，检查是否有任一权限
  if (Array.isArray(requiredPermissions)) {
    return requiredPermissions.some(permission => 
      userPermissions.includes(permission)
    )
  }
  
  // 如果是字符串，检查是否有该权限
  return userPermissions.includes(requiredPermissions)
}

/**
 * 检查用户是否有指定角色
 * @param {Array} userRoles - 用户角色列表
 * @param {string|Array} requiredRoles - 需要的角色
 * @returns {boolean} 是否有角色
 */
export const hasRole = (userRoles, requiredRoles) => {
  if (!requiredRoles) {
    return true
  }
  
  if (!userRoles || userRoles.length === 0) {
    return false
  }
  
  // 如果是数组，检查是否有任一角色
  if (Array.isArray(requiredRoles)) {
    return requiredRoles.some(role => userRoles.includes(role))
  }
  
  // 如果是字符串，检查是否有该角色
  return userRoles.includes(requiredRoles)
}

/**
 * 检查用户是否为管理员
 * @param {Array} userRoles - 用户角色列表
 * @returns {boolean} 是否为管理员
 */
export const isAdmin = (userRoles) => {
  return hasRole(userRoles, ['admin', 'administrator', 'super_admin'])
}

/**
 * 格式化权限显示名称
 * @param {string} permission - 权限代码
 * @returns {string} 权限显示名称
 */
export const formatPermissionName = (permission) => {
  const permissionMap = {
    'user:read': '查看用户',
    'user:write': '编辑用户',
    'user:delete': '删除用户',
    'product:read': '查看商品',
    'product:write': '编辑商品',
    'product:delete': '删除商品',
    'order:read': '查看订单',
    'order:write': '编辑订单',
    'order:delete': '删除订单',
    'system:read': '查看系统',
    'system:write': '编辑系统',
    'system:delete': '删除系统'
  }
  
  return permissionMap[permission] || permission
}

/**
 * 格式化角色显示名称
 * @param {string} role - 角色代码
 * @returns {string} 角色显示名称
 */
export const formatRoleName = (role) => {
  const roleMap = {
    'admin': '管理员',
    'administrator': '系统管理员',
    'super_admin': '超级管理员',
    'user': '普通用户',
    'vip': 'VIP用户',
    'guest': '访客'
  }
  
  return roleMap[role] || role
}

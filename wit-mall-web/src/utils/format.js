/**
 * 格式化工具函数
 * 
 * 主要功能：
 * 1. 数字格式化
 * 2. 货币格式化
 * 3. 文件大小格式化
 * 4. 时间格式化
 * 5. 文本格式化
 * 
 * <AUTHOR>
 */

/**
 * 格式化数字，添加千分位分隔符
 * @param {number|string} num - 数字
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 格式化后的数字
 */
export const formatNumber = (num, decimals = 2) => {
  if (num === null || num === undefined || num === '') {
    return '0'
  }
  
  const number = parseFloat(num)
  if (isNaN(number)) {
    return '0'
  }
  
  return number.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 格式化货币
 * @param {number|string} amount - 金额
 * @param {string} currency - 货币符号，默认 ¥
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 格式化后的货币
 */
export const formatCurrency = (amount, currency = '¥', decimals = 2) => {
  if (amount === null || amount === undefined || amount === '') {
    return `${currency}0.00`
  }
  
  const number = parseFloat(amount)
  if (isNaN(number)) {
    return `${currency}0.00`
  }
  
  const formatted = number.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
  
  return `${currency}${formatted}`
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 B'
  if (!bytes || bytes < 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))
  return `${size} ${sizes[i]}`
}

/**
 * 格式化百分比
 * @param {number} value - 数值（0-1 或 0-100）
 * @param {number} decimals - 小数位数，默认2位
 * @param {boolean} isDecimal - 输入值是否为小数（0-1），默认false
 * @returns {string} 格式化后的百分比
 */
export const formatPercentage = (value, decimals = 2, isDecimal = false) => {
  if (value === null || value === undefined || value === '') {
    return '0%'
  }
  
  const number = parseFloat(value)
  if (isNaN(number)) {
    return '0%'
  }
  
  const percentage = isDecimal ? number * 100 : number
  return `${percentage.toFixed(decimals)}%`
}

/**
 * 格式化手机号
 * @param {string} phone - 手机号
 * @param {string} separator - 分隔符，默认空格
 * @returns {string} 格式化后的手机号
 */
export const formatPhone = (phone, separator = ' ') => {
  if (!phone) return ''
  
  const cleaned = phone.replace(/\D/g, '')
  
  if (cleaned.length === 11) {
    // 中国大陆手机号：138 0013 8000
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, `$1${separator}$2${separator}$3`)
  }
  
  return phone
}

/**
 * 格式化身份证号
 * @param {string} idCard - 身份证号
 * @param {boolean} mask - 是否脱敏，默认true
 * @returns {string} 格式化后的身份证号
 */
export const formatIdCard = (idCard, mask = true) => {
  if (!idCard) return ''
  
  if (mask && idCard.length >= 8) {
    // 脱敏处理：显示前4位和后4位
    const start = idCard.substring(0, 4)
    const end = idCard.substring(idCard.length - 4)
    const middle = '*'.repeat(idCard.length - 8)
    return `${start}${middle}${end}`
  }
  
  return idCard
}

/**
 * 格式化银行卡号
 * @param {string} cardNumber - 银行卡号
 * @param {boolean} mask - 是否脱敏，默认true
 * @param {string} separator - 分隔符，默认空格
 * @returns {string} 格式化后的银行卡号
 */
export const formatBankCard = (cardNumber, mask = true, separator = ' ') => {
  if (!cardNumber) return ''
  
  const cleaned = cardNumber.replace(/\D/g, '')
  
  if (mask && cleaned.length > 8) {
    // 脱敏处理：显示前4位和后4位
    const start = cleaned.substring(0, 4)
    const end = cleaned.substring(cleaned.length - 4)
    const middle = '*'.repeat(Math.max(0, cleaned.length - 8))
    const masked = `${start}${middle}${end}`
    
    // 添加分隔符
    return masked.replace(/(\d{4})/g, `$1${separator}`).trim()
  }
  
  // 不脱敏，只添加分隔符
  return cleaned.replace(/(\d{4})/g, `$1${separator}`).trim()
}

/**
 * 格式化邮箱
 * @param {string} email - 邮箱地址
 * @param {boolean} mask - 是否脱敏，默认false
 * @returns {string} 格式化后的邮箱
 */
export const formatEmail = (email, mask = false) => {
  if (!email) return ''
  
  if (mask) {
    const [username, domain] = email.split('@')
    if (username && domain) {
      const maskedUsername = username.length > 2 
        ? `${username[0]}***${username[username.length - 1]}`
        : username
      return `${maskedUsername}@${domain}`
    }
  }
  
  return email
}

/**
 * 格式化地址
 * @param {Object} address - 地址对象
 * @param {string} address.province - 省份
 * @param {string} address.city - 城市
 * @param {string} address.district - 区县
 * @param {string} address.detail - 详细地址
 * @param {string} separator - 分隔符，默认空字符串
 * @returns {string} 格式化后的地址
 */
export const formatAddress = (address, separator = '') => {
  if (!address) return ''
  
  const parts = [
    address.province,
    address.city,
    address.district,
    address.detail
  ].filter(part => part && part.trim())
  
  return parts.join(separator)
}

/**
 * 格式化订单号
 * @param {string} orderNo - 订单号
 * @param {string} separator - 分隔符，默认空格
 * @returns {string} 格式化后的订单号
 */
export const formatOrderNo = (orderNo, separator = ' ') => {
  if (!orderNo) return ''
  
  // 每4位添加一个分隔符
  return orderNo.replace(/(\d{4})/g, `$1${separator}`).trim()
}

/**
 * 格式化商品SKU
 * @param {string} sku - SKU编码
 * @param {string} separator - 分隔符，默认-
 * @returns {string} 格式化后的SKU
 */
export const formatSku = (sku, separator = '-') => {
  if (!sku) return ''
  
  // 如果SKU很长，可以添加分隔符提高可读性
  if (sku.length > 8) {
    return sku.replace(/(\w{4})/g, `$1${separator}`).replace(new RegExp(`${separator}$`), '')
  }
  
  return sku
}

/**
 * 格式化评分
 * @param {number} rating - 评分
 * @param {number} maxRating - 最高评分，默认5
 * @param {string} symbol - 评分符号，默认★
 * @returns {string} 格式化后的评分
 */
export const formatRating = (rating, maxRating = 5, symbol = '★') => {
  if (rating === null || rating === undefined) {
    return '未评分'
  }
  
  const score = Math.max(0, Math.min(maxRating, parseFloat(rating)))
  const fullStars = Math.floor(score)
  const hasHalfStar = score % 1 >= 0.5
  const emptyStars = maxRating - fullStars - (hasHalfStar ? 1 : 0)
  
  let result = symbol.repeat(fullStars)
  if (hasHalfStar) {
    result += '☆' // 半星
  }
  result += '☆'.repeat(emptyStars)
  
  return `${result} (${score.toFixed(1)})`
}

/**
 * 格式化距离
 * @param {number} distance - 距离（米）
 * @param {number} decimals - 小数位数，默认1位
 * @returns {string} 格式化后的距离
 */
export const formatDistance = (distance, decimals = 1) => {
  if (!distance || distance < 0) {
    return '0m'
  }
  
  if (distance < 1000) {
    return `${Math.round(distance)}m`
  } else {
    const km = distance / 1000
    return `${km.toFixed(decimals)}km`
  }
}

/**
 * 格式化时长
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时长
 */
export const formatDuration = (seconds) => {
  if (!seconds || seconds < 0) {
    return '0秒'
  }
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  
  const parts = []
  if (hours > 0) parts.push(`${hours}小时`)
  if (minutes > 0) parts.push(`${minutes}分钟`)
  if (remainingSeconds > 0 || parts.length === 0) parts.push(`${remainingSeconds}秒`)
  
  return parts.join('')
}

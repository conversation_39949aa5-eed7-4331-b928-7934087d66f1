/**
 * 租户管理工具函数
 * 
 * 主要功能：
 * 1. 租户ID存储管理
 * 2. 租户信息缓存
 * 3. 多租户路由处理
 * 
 * <AUTHOR>
 */

/**
 * 租户ID存储键名
 */
const TENANT_ID_KEY = 'wit_mall_tenant_id'

/**
 * 租户信息存储键名
 */
const TENANT_INFO_KEY = 'wit_mall_tenant_info'

/**
 * 获取当前租户ID
 * @returns {string|null} 租户ID
 */
export const getCurrentTenantId = () => {
  // 优先从URL路径获取
  const pathTenantId = getTenantIdFromPath()
  if (pathTenantId) {
    return pathTenantId
  }
  
  // 从localStorage获取
  return localStorage.getItem(TENANT_ID_KEY)
}

/**
 * 设置当前租户ID
 * @param {string} tenantId - 租户ID
 */
export const setCurrentTenantId = (tenantId) => {
  if (!tenantId) {
    console.warn('租户ID不能为空')
    return
  }
  
  localStorage.setItem(TENANT_ID_KEY, tenantId)
  console.log('租户ID已设置:', tenantId)
}

/**
 * 移除当前租户ID
 */
export const removeCurrentTenantId = () => {
  localStorage.removeItem(TENANT_ID_KEY)
  localStorage.removeItem(TENANT_INFO_KEY)
  console.log('租户信息已清除')
}

/**
 * 从URL路径中提取租户ID
 * 支持格式: /{tenantId}/... 或 /tenant/{tenantId}/...
 * @returns {string|null} 租户ID
 */
export const getTenantIdFromPath = () => {
  const path = window.location.pathname
  
  // 匹配 /{tenantId}/... 格式（tenantId为数字）
  const match1 = path.match(/^\/(\d+)\//)
  if (match1) {
    return match1[1]
  }
  
  // 匹配 /tenant/{tenantId}/... 格式
  const match2 = path.match(/^\/tenant\/(\d+)\//)
  if (match2) {
    return match2[1]
  }
  
  return null
}

/**
 * 构建多租户路由路径
 * @param {string} path - 原始路径
 * @param {string} tenantId - 租户ID（可选，默认使用当前租户ID）
 * @returns {string} 多租户路径
 */
export const buildTenantPath = (path, tenantId = null) => {
  const currentTenantId = tenantId || getCurrentTenantId()
  
  if (!currentTenantId) {
    return path
  }
  
  // 如果路径已经包含租户ID，直接返回
  if (path.startsWith(`/${currentTenantId}/`)) {
    return path
  }
  
  // 移除开头的斜杠
  const cleanPath = path.startsWith('/') ? path.substring(1) : path
  
  return `/${currentTenantId}/${cleanPath}`
}

/**
 * 获取租户信息
 * @returns {Object|null} 租户信息
 */
export const getTenantInfo = () => {
  const tenantInfoStr = localStorage.getItem(TENANT_INFO_KEY)
  
  if (!tenantInfoStr) {
    return null
  }
  
  try {
    return JSON.parse(tenantInfoStr)
  } catch (error) {
    console.error('租户信息解析失败:', error)
    return null
  }
}

/**
 * 设置租户信息
 * @param {Object} tenantInfo - 租户信息
 */
export const setTenantInfo = (tenantInfo) => {
  if (!tenantInfo) {
    console.warn('租户信息不能为空')
    return
  }
  
  localStorage.setItem(TENANT_INFO_KEY, JSON.stringify(tenantInfo))
  
  // 同时设置租户ID
  if (tenantInfo.id) {
    setCurrentTenantId(tenantInfo.id)
  }
  
  console.log('租户信息已设置:', tenantInfo)
}

/**
 * 检查是否为多租户模式
 * @returns {boolean} 是否为多租户模式
 */
export const isMultiTenant = () => {
  return !!getCurrentTenantId()
}

/**
 * 获取租户显示名称
 * @returns {string} 租户显示名称
 */
export const getTenantDisplayName = () => {
  const tenantInfo = getTenantInfo()
  
  if (tenantInfo && tenantInfo.name) {
    return tenantInfo.name
  }
  
  const tenantId = getCurrentTenantId()
  return tenantId ? `租户 ${tenantId}` : '未知租户'
}

/**
 * 验证租户ID格式
 * @param {string} tenantId - 租户ID
 * @returns {boolean} 是否为有效格式
 */
export const isValidTenantId = (tenantId) => {
  if (!tenantId) {
    return false
  }
  
  // 租户ID应该是数字字符串
  return /^\d+$/.test(tenantId)
}

/**
 * 切换租户
 * @param {string} tenantId - 新的租户ID
 * @param {Object} tenantInfo - 租户信息（可选）
 * @returns {Promise} 切换结果
 */
export const switchTenant = async (tenantId, tenantInfo = null) => {
  if (!isValidTenantId(tenantId)) {
    throw new Error('无效的租户ID格式')
  }
  
  try {
    // 设置新的租户ID
    setCurrentTenantId(tenantId)
    
    // 设置租户信息
    if (tenantInfo) {
      setTenantInfo(tenantInfo)
    }
    
    // 刷新页面以应用新的租户上下文
    window.location.reload()
    
    return Promise.resolve()
  } catch (error) {
    console.error('切换租户失败:', error)
    return Promise.reject(error)
  }
}

/**
 * 从Token中提取租户ID
 * @param {string} token - JWT Token
 * @returns {string|null} 租户ID
 */
export const getTenantIdFromToken = (token) => {
  if (!token) {
    return null
  }
  
  try {
    // JWT Token 由三部分组成，用 . 分隔
    const parts = token.split('.')
    if (parts.length !== 3) {
      return null
    }
    
    // 解码 payload 部分
    const payload = parts[1]
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'))
    const tokenData = JSON.parse(decoded)
    
    return tokenData.tenantId || null
  } catch (error) {
    console.error('从Token提取租户ID失败:', error)
    return null
  }
}

/**
 * 同步Token中的租户ID到本地存储
 * @param {string} token - JWT Token
 */
export const syncTenantIdFromToken = (token) => {
  const tenantId = getTenantIdFromToken(token)
  
  if (tenantId) {
    const currentTenantId = getCurrentTenantId()
    
    // 如果Token中的租户ID与当前不同，更新本地存储
    if (tenantId !== currentTenantId) {
      setCurrentTenantId(tenantId)
      console.log('已同步Token中的租户ID:', tenantId)
    }
  }
}

/**
 * 清理租户相关数据
 */
export const clearTenantData = () => {
  removeCurrentTenantId()
  console.log('租户数据已清理')
}

/**
 * 租户配置
 */
export const TENANT_CONFIG = {
  // 默认租户ID（用于演示）
  DEFAULT_TENANT_ID: '3921',
  
  // 租户路径前缀
  PATH_PREFIX: '',
  
  // 是否启用多租户模式
  MULTI_TENANT_ENABLED: true,
  
  // 租户切换确认提示
  SWITCH_CONFIRM_MESSAGE: '切换租户将刷新页面，确定要继续吗？'
}

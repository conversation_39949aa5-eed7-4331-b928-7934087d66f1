/**
 * 应用程序主入口文件
 * 
 * 主要功能：
 * 1. 创建 Vue 应用实例
 * 2. 注册全局插件和组件
 * 3. 配置全局属性和方法
 * 4. 挂载应用到 DOM
 * 
 * <AUTHOR>
 */

import { createApp } from 'vue'
import App from './App.vue'

// 路由
import router from './router'

// 状态管理
import { createPinia } from 'pinia'

// Element Plus 样式
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

// 全局样式
import '@/assets/styles/index.scss'

// 进度条样式
import 'nprogress/nprogress.css'

// 工具函数
import { setupGlobalProperties } from '@/utils/global'

/**
 * 创建应用实例
 */
const app = createApp(App)

/**
 * 创建 Pinia 实例
 */
const pinia = createPinia()

/**
 * 注册插件
 */
// 注册路由
app.use(router)

// 注册状态管理
app.use(pinia)

/**
 * 设置全局属性和方法
 */
setupGlobalProperties(app)

/**
 * 全局错误处理
 */
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误捕获:', err)
  console.error('错误信息:', info)
  console.error('组件实例:', vm)
  
  // 这里可以上报错误到监控系统
  // errorReporter.report(err, vm, info)
}

/**
 * 全局警告处理（仅在开发环境）
 */
if (import.meta.env.DEV) {
  app.config.warnHandler = (msg, vm, trace) => {
    console.warn('Vue 警告:', msg)
    console.warn('组件追踪:', trace)
  }
}

/**
 * 挂载应用
 */
app.mount('#app')

/**
 * 开发环境下的调试信息
 */
if (import.meta.env.DEV) {
  console.log('🚀 Wit Mall 前端应用启动成功!')
  console.log('📦 Vue 版本:', app.version)
  console.log('🌍 环境模式:', import.meta.env.MODE)
  console.log('🔗 API 基础地址:', import.meta.env.VITE_API_BASE_URL || '/api')
}

/**
 * 认证相关 API
 * 
 * 主要功能：
 * 1. 用户登录
 * 2. 用户注册
 * 3. 用户登出
 * 4. 获取用户信息
 * 5. 刷新令牌
 * 6. 密码相关操作
 * 
 * <AUTHOR>
 */

import request from './request'

/**
 * 用户登录
 * @param {Object} data - 登录数据
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @param {string} data.captcha - 验证码
 * @param {string} data.captchaKey - 验证码key
 * @returns {Promise} 登录结果
 */
export const login = (data) => {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}

/**
 * 用户注册
 * @param {Object} data - 注册数据
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @param {string} data.confirmPassword - 确认密码
 * @param {string} data.email - 邮箱
 * @param {string} data.phone - 手机号
 * @param {string} data.captcha - 验证码
 * @param {string} data.captchaKey - 验证码key
 * @returns {Promise} 注册结果
 */
export const register = (data) => {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  })
}

/**
 * 用户登出
 * @returns {Promise} 登出结果
 */
export const logout = () => {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

/**
 * 获取用户信息
 * @returns {Promise} 用户信息
 */
export const getUserInfo = () => {
  return request({
    url: '/auth/user-info',
    method: 'get'
  })
}

/**
 * 刷新访问令牌
 * @param {string} refreshToken - 刷新令牌
 * @returns {Promise} 新的令牌信息
 */
export const refreshToken = (refreshToken) => {
  return request({
    url: '/auth/refresh-token',
    method: 'post',
    data: { refreshToken }
  })
}

/**
 * 获取图形验证码
 * @returns {Promise} 验证码信息
 */
export const getCaptcha = () => {
  return request({
    url: '/auth/captcha',
    method: 'get'
  })
}

/**
 * 发送短信验证码
 * @param {Object} data - 请求数据
 * @param {string} data.phone - 手机号
 * @param {string} data.type - 验证码类型 (register|login|reset)
 * @returns {Promise} 发送结果
 */
export const sendSmsCode = (data) => {
  return request({
    url: '/auth/sms-code',
    method: 'post',
    data
  })
}

/**
 * 发送邮箱验证码
 * @param {Object} data - 请求数据
 * @param {string} data.email - 邮箱地址
 * @param {string} data.type - 验证码类型 (register|login|reset)
 * @returns {Promise} 发送结果
 */
export const sendEmailCode = (data) => {
  return request({
    url: '/auth/email-code',
    method: 'post',
    data
  })
}

/**
 * 手机号登录
 * @param {Object} data - 登录数据
 * @param {string} data.phone - 手机号
 * @param {string} data.smsCode - 短信验证码
 * @returns {Promise} 登录结果
 */
export const loginByPhone = (data) => {
  return request({
    url: '/auth/login-by-phone',
    method: 'post',
    data
  })
}

/**
 * 邮箱登录
 * @param {Object} data - 登录数据
 * @param {string} data.email - 邮箱地址
 * @param {string} data.emailCode - 邮箱验证码
 * @returns {Promise} 登录结果
 */
export const loginByEmail = (data) => {
  return request({
    url: '/auth/login-by-email',
    method: 'post',
    data
  })
}

/**
 * 修改密码
 * @param {Object} data - 修改密码数据
 * @param {string} data.oldPassword - 旧密码
 * @param {string} data.newPassword - 新密码
 * @param {string} data.confirmPassword - 确认新密码
 * @returns {Promise} 修改结果
 */
export const changePassword = (data) => {
  return request({
    url: '/auth/change-password',
    method: 'post',
    data
  })
}

/**
 * 忘记密码 - 发送重置链接
 * @param {Object} data - 请求数据
 * @param {string} data.email - 邮箱地址
 * @returns {Promise} 发送结果
 */
export const forgotPassword = (data) => {
  return request({
    url: '/auth/forgot-password',
    method: 'post',
    data
  })
}

/**
 * 重置密码
 * @param {Object} data - 重置密码数据
 * @param {string} data.token - 重置令牌
 * @param {string} data.newPassword - 新密码
 * @param {string} data.confirmPassword - 确认新密码
 * @returns {Promise} 重置结果
 */
export const resetPassword = (data) => {
  return request({
    url: '/auth/reset-password',
    method: 'post',
    data
  })
}

/**
 * 绑定手机号
 * @param {Object} data - 绑定数据
 * @param {string} data.phone - 手机号
 * @param {string} data.smsCode - 短信验证码
 * @returns {Promise} 绑定结果
 */
export const bindPhone = (data) => {
  return request({
    url: '/auth/bind-phone',
    method: 'post',
    data
  })
}

/**
 * 绑定邮箱
 * @param {Object} data - 绑定数据
 * @param {string} data.email - 邮箱地址
 * @param {string} data.emailCode - 邮箱验证码
 * @returns {Promise} 绑定结果
 */
export const bindEmail = (data) => {
  return request({
    url: '/auth/bind-email',
    method: 'post',
    data
  })
}

/**
 * 解绑手机号
 * @param {Object} data - 解绑数据
 * @param {string} data.smsCode - 短信验证码
 * @returns {Promise} 解绑结果
 */
export const unbindPhone = (data) => {
  return request({
    url: '/auth/unbind-phone',
    method: 'post',
    data
  })
}

/**
 * 解绑邮箱
 * @param {Object} data - 解绑数据
 * @param {string} data.emailCode - 邮箱验证码
 * @returns {Promise} 解绑结果
 */
export const unbindEmail = (data) => {
  return request({
    url: '/auth/unbind-email',
    method: 'post',
    data
  })
}

/**
 * 检查用户名是否可用
 * @param {string} username - 用户名
 * @returns {Promise} 检查结果
 */
export const checkUsername = (username) => {
  return request({
    url: '/auth/check-username',
    method: 'get',
    params: { username }
  })
}

/**
 * 检查邮箱是否可用
 * @param {string} email - 邮箱地址
 * @returns {Promise} 检查结果
 */
export const checkEmail = (email) => {
  return request({
    url: '/auth/check-email',
    method: 'get',
    params: { email }
  })
}

/**
 * 检查手机号是否可用
 * @param {string} phone - 手机号
 * @returns {Promise} 检查结果
 */
export const checkPhone = (phone) => {
  return request({
    url: '/auth/check-phone',
    method: 'get',
    params: { phone }
  })
}

/**
 * 获取第三方登录配置
 * @returns {Promise} 第三方登录配置
 */
export const getOAuthConfig = () => {
  return request({
    url: '/auth/oauth-config',
    method: 'get'
  })
}

/**
 * 第三方登录回调处理
 * @param {Object} data - 回调数据
 * @param {string} data.provider - 第三方提供商 (wechat|qq|weibo|github)
 * @param {string} data.code - 授权码
 * @param {string} data.state - 状态参数
 * @returns {Promise} 登录结果
 */
export const oauthCallback = (data) => {
  return request({
    url: '/auth/oauth-callback',
    method: 'post',
    data
  })
}

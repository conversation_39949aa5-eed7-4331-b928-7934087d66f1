/**
 * Axios 请求配置
 * 
 * 主要功能：
 * 1. 配置请求和响应拦截器
 * 2. 统一错误处理
 * 3. 自动添加认证头
 * 4. 请求和响应日志记录
 * 5. 请求重试机制
 * 
 * <AUTHOR>
 */

import axios from 'axios'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'
import router from '@/router'

/**
 * 创建 axios 实例
 */
const request = axios.create({
  // API 基础地址
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  
  // 请求超时时间（毫秒）
  timeout: 30000,
  
  // 默认请求头
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  
  // 跨域请求时是否携带凭证
  withCredentials: false
})

/**
 * 请求加载实例
 */
let loadingInstance = null

/**
 * 当前请求数量
 */
let requestCount = 0

/**
 * 显示全局加载
 */
const showLoading = () => {
  if (requestCount === 0) {
    loadingInstance = ElLoading.service({
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
      spinner: 'el-icon-loading'
    })
  }
  requestCount++
}

/**
 * 隐藏全局加载
 */
const hideLoading = () => {
  requestCount--
  if (requestCount <= 0) {
    requestCount = 0
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
  }
}

/**
 * 请求拦截器
 */
request.interceptors.request.use(
  (config) => {
    // 开发环境下打印请求信息
    if (import.meta.env.DEV) {
      console.log('🚀 发送请求:', {
        url: config.url,
        method: config.method,
        params: config.params,
        data: config.data
      })
    }
    
    // 显示加载动画（如果配置了需要显示）
    if (config.loading !== false) {
      showLoading()
    }
    
    // 添加认证头
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    // 添加租户ID（如果是多租户系统）
    const tenantId = localStorage.getItem('tenantId')
    if (tenantId) {
      config.headers['Tenant-Id'] = tenantId
    }
    
    return config
  },
  (error) => {
    console.error('❌ 请求配置错误:', error)
    hideLoading()
    return Promise.reject(error)
  }
)

/**
 * 响应拦截器
 */
request.interceptors.response.use(
  (response) => {
    // 隐藏加载动画
    hideLoading()
    
    // 开发环境下打印响应信息
    if (import.meta.env.DEV) {
      console.log('✅ 收到响应:', {
        url: response.config.url,
        status: response.status,
        data: response.data
      })
    }
    
    const { data } = response
    
    // 如果是文件下载，直接返回响应
    if (response.config.responseType === 'blob') {
      return response
    }
    
    // 统一的响应数据结构处理
    if (data.success === false) {
      // 业务错误处理
      handleBusinessError(data)
      return Promise.reject(new Error(data.message || '请求失败'))
    }
    
    return data
  },
  (error) => {
    // 隐藏加载动画
    hideLoading()
    
    console.error('❌ 请求失败:', error)
    
    // 处理不同类型的错误
    handleRequestError(error)
    
    return Promise.reject(error)
  }
)

/**
 * 处理业务错误
 * @param {Object} data - 响应数据
 */
const handleBusinessError = (data) => {
  const { code, message } = data
  
  switch (code) {
    case 401:
      // 未授权，跳转到登录页
      handleUnauthorized()
      break
      
    case 403:
      // 无权限
      ElMessage.error(message || '您没有权限执行此操作')
      break
      
    case 404:
      // 资源不存在
      ElMessage.error(message || '请求的资源不存在')
      break
      
    case 500:
      // 服务器错误
      ElMessage.error(message || '服务器内部错误')
      break
      
    default:
      // 其他业务错误
      ElMessage.error(message || '操作失败')
  }
}

/**
 * 处理请求错误
 * @param {Error} error - 错误对象
 */
const handleRequestError = (error) => {
  if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
    // 请求超时
    ElMessage.error('请求超时，请稍后重试')
  } else if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response
    
    switch (status) {
      case 401:
        handleUnauthorized()
        break
        
      case 403:
        ElMessage.error('您没有权限访问此资源')
        break
        
      case 404:
        ElMessage.error('请求的接口不存在')
        break
        
      case 500:
        ElMessage.error('服务器内部错误')
        break
        
      case 502:
        ElMessage.error('网关错误')
        break
        
      case 503:
        ElMessage.error('服务暂时不可用')
        break
        
      default:
        ElMessage.error(data?.message || `请求失败 (${status})`)
    }
  } else if (error.request) {
    // 网络错误
    ElMessage.error('网络连接失败，请检查网络设置')
  } else {
    // 其他错误
    ElMessage.error(error.message || '未知错误')
  }
}

/**
 * 处理未授权错误
 */
const handleUnauthorized = () => {
  const userStore = useUserStore()
  
  ElMessageBox.confirm(
    '您的登录状态已过期，请重新登录',
    '登录过期',
    {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 清除用户数据并跳转到登录页
    userStore.logout(false)
  }).catch(() => {
    // 用户取消，也清除数据
    userStore.clearUserData()
  })
}

/**
 * 请求重试配置
 */
request.defaults.retry = 3 // 重试次数
request.defaults.retryDelay = 1000 // 重试延迟

/**
 * 请求重试拦截器
 */
request.interceptors.response.use(undefined, (error) => {
  const config = error.config
  
  // 如果配置了重试且不是取消请求的错误
  if (config && config.retry && !axios.isCancel(error)) {
    config.__retryCount = config.__retryCount || 0
    
    if (config.__retryCount >= config.retry) {
      return Promise.reject(error)
    }
    
    config.__retryCount += 1
    
    // 延迟重试
    const delay = config.retryDelay || 1000
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log(`🔄 重试请求 (${config.__retryCount}/${config.retry}):`, config.url)
        resolve(request(config))
      }, delay)
    })
  }
  
  return Promise.reject(error)
})

/**
 * 取消请求的 token 存储
 */
const cancelTokens = new Map()

/**
 * 添加取消请求的 token
 * @param {string} key - 请求标识
 * @param {Function} cancel - 取消函数
 */
export const addCancelToken = (key, cancel) => {
  cancelTokens.set(key, cancel)
}

/**
 * 取消指定请求
 * @param {string} key - 请求标识
 */
export const cancelRequest = (key) => {
  const cancel = cancelTokens.get(key)
  if (cancel) {
    cancel('请求被取消')
    cancelTokens.delete(key)
  }
}

/**
 * 取消所有请求
 */
export const cancelAllRequests = () => {
  cancelTokens.forEach((cancel, key) => {
    cancel('请求被取消')
  })
  cancelTokens.clear()
}

export default request

/**
 * 系统相关 API
 * 
 * 主要功能：
 * 1. 获取系统配置
 * 2. 获取字典数据
 * 3. 文件上传下载
 * 4. 系统监控信息
 * 5. 操作日志
 * 
 * <AUTHOR>
 */

import request from './request'

/**
 * 获取应用配置信息
 * @returns {Promise} 应用配置
 */
export const getAppConfig = () => {
  return request({
    url: '/system/app-config',
    method: 'get'
  })
}

/**
 * 获取系统信息
 * @returns {Promise} 系统信息
 */
export const getSystemInfo = () => {
  return request({
    url: '/system/info',
    method: 'get'
  })
}

/**
 * 获取字典数据
 * @param {string} dictType - 字典类型
 * @returns {Promise} 字典数据
 */
export const getDictData = (dictType) => {
  return request({
    url: '/system/dict-data',
    method: 'get',
    params: { dictType }
  })
}

/**
 * 获取所有字典数据
 * @returns {Promise} 所有字典数据
 */
export const getAllDictData = () => {
  return request({
    url: '/system/dict-data/all',
    method: 'get'
  })
}

/**
 * 文件上传
 * @param {FormData} formData - 文件数据
 * @param {Object} config - 上传配置
 * @returns {Promise} 上传结果
 */
export const uploadFile = (formData, config = {}) => {
  return request({
    url: '/system/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}

/**
 * 批量文件上传
 * @param {FormData} formData - 文件数据
 * @param {Object} config - 上传配置
 * @returns {Promise} 上传结果
 */
export const uploadFiles = (formData, config = {}) => {
  return request({
    url: '/system/upload/batch',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}

/**
 * 文件下载
 * @param {string} fileId - 文件ID
 * @param {string} fileName - 文件名
 * @returns {Promise} 文件流
 */
export const downloadFile = (fileId, fileName) => {
  return request({
    url: `/system/download/${fileId}`,
    method: 'get',
    params: { fileName },
    responseType: 'blob'
  })
}

/**
 * 删除文件
 * @param {string} fileId - 文件ID
 * @returns {Promise} 删除结果
 */
export const deleteFile = (fileId) => {
  return request({
    url: `/system/file/${fileId}`,
    method: 'delete'
  })
}

/**
 * 获取文件信息
 * @param {string} fileId - 文件ID
 * @returns {Promise} 文件信息
 */
export const getFileInfo = (fileId) => {
  return request({
    url: `/system/file/${fileId}`,
    method: 'get'
  })
}

/**
 * 获取文件列表
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页
 * @param {number} params.size - 页大小
 * @param {string} params.fileName - 文件名
 * @param {string} params.fileType - 文件类型
 * @returns {Promise} 文件列表
 */
export const getFileList = (params) => {
  return request({
    url: '/system/file/list',
    method: 'get',
    params
  })
}

/**
 * 获取系统监控信息
 * @returns {Promise} 监控信息
 */
export const getMonitorInfo = () => {
  return request({
    url: '/system/monitor',
    method: 'get'
  })
}

/**
 * 获取服务器信息
 * @returns {Promise} 服务器信息
 */
export const getServerInfo = () => {
  return request({
    url: '/system/server',
    method: 'get'
  })
}

/**
 * 获取操作日志列表
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页
 * @param {number} params.size - 页大小
 * @param {string} params.username - 用户名
 * @param {string} params.operation - 操作类型
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @returns {Promise} 操作日志列表
 */
export const getOperationLogs = (params) => {
  return request({
    url: '/system/operation-logs',
    method: 'get',
    params
  })
}

/**
 * 获取登录日志列表
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页
 * @param {number} params.size - 页大小
 * @param {string} params.username - 用户名
 * @param {string} params.ip - IP地址
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @returns {Promise} 登录日志列表
 */
export const getLoginLogs = (params) => {
  return request({
    url: '/system/login-logs',
    method: 'get',
    params
  })
}

/**
 * 清理系统缓存
 * @param {string} cacheType - 缓存类型 (all|user|dict|config)
 * @returns {Promise} 清理结果
 */
export const clearCache = (cacheType = 'all') => {
  return request({
    url: '/system/cache/clear',
    method: 'post',
    data: { cacheType }
  })
}

/**
 * 获取缓存信息
 * @returns {Promise} 缓存信息
 */
export const getCacheInfo = () => {
  return request({
    url: '/system/cache/info',
    method: 'get'
  })
}

/**
 * 发送系统通知
 * @param {Object} data - 通知数据
 * @param {string} data.title - 通知标题
 * @param {string} data.content - 通知内容
 * @param {string} data.type - 通知类型 (info|success|warning|error)
 * @param {Array} data.userIds - 接收用户ID列表
 * @returns {Promise} 发送结果
 */
export const sendNotification = (data) => {
  return request({
    url: '/system/notification/send',
    method: 'post',
    data
  })
}

/**
 * 获取系统通知列表
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页
 * @param {number} params.size - 页大小
 * @param {string} params.type - 通知类型
 * @param {boolean} params.read - 是否已读
 * @returns {Promise} 通知列表
 */
export const getNotifications = (params) => {
  return request({
    url: '/system/notification/list',
    method: 'get',
    params
  })
}

/**
 * 标记通知为已读
 * @param {Array} notificationIds - 通知ID列表
 * @returns {Promise} 标记结果
 */
export const markNotificationsRead = (notificationIds) => {
  return request({
    url: '/system/notification/mark-read',
    method: 'post',
    data: { notificationIds }
  })
}

/**
 * 删除通知
 * @param {Array} notificationIds - 通知ID列表
 * @returns {Promise} 删除结果
 */
export const deleteNotifications = (notificationIds) => {
  return request({
    url: '/system/notification/delete',
    method: 'post',
    data: { notificationIds }
  })
}

/**
 * 获取未读通知数量
 * @returns {Promise} 未读通知数量
 */
export const getUnreadNotificationCount = () => {
  return request({
    url: '/system/notification/unread-count',
    method: 'get'
  })
}

/**
 * 导出数据
 * @param {Object} data - 导出参数
 * @param {string} data.type - 导出类型
 * @param {Object} data.params - 导出参数
 * @returns {Promise} 导出文件
 */
export const exportData = (data) => {
  return request({
    url: '/system/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

/**
 * 导入数据
 * @param {FormData} formData - 导入文件
 * @param {string} importType - 导入类型
 * @returns {Promise} 导入结果
 */
export const importData = (formData, importType) => {
  return request({
    url: `/system/import/${importType}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

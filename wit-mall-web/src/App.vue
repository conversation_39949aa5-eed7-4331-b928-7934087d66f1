<!--
  应用根组件
  
  主要功能：
  1. 提供应用的根容器
  2. 渲染路由视图
  3. 全局加载状态管理
  4. 全局主题切换
  
  <AUTHOR>
-->

<template>
  <div id="app" :class="{ 'dark': isDark }">
    <!-- 全局加载进度条 -->
    <div v-if="isLoading" class="global-loading">
      <el-loading-service />
    </div>
    
    <!-- 路由视图容器 -->
    <router-view v-slot="{ Component, route }">
      <!-- 路由过渡动画 -->
      <transition 
        :name="route.meta.transition || 'fade'" 
        mode="out-in"
        appear
      >
        <!-- 页面组件 -->
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>
    
    <!-- 全局消息提示容器 -->
    <div id="message-container"></div>
    
    <!-- 全局对话框容器 -->
    <div id="dialog-container"></div>
  </div>
</template>

<script>
/**
 * 应用根组件逻辑
 */
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'

export default {
  name: 'App',
  
  setup() {
    // 路由实例
    const router = useRouter()
    
    // 状态管理
    const appStore = useAppStore()
    const userStore = useUserStore()
    
    // 响应式数据
    const isLoading = ref(false)
    const isDark = ref(false)
    
    /**
     * 初始化应用
     */
    const initApp = async () => {
      try {
        isLoading.value = true
        
        // 初始化主题
        initTheme()
        
        // 初始化用户信息（如果已登录）
        await initUser()
        
        // 初始化应用配置
        await initAppConfig()
        
      } catch (error) {
        console.error('应用初始化失败:', error)
      } finally {
        isLoading.value = false
      }
    }
    
    /**
     * 初始化主题
     */
    const initTheme = () => {
      // 从本地存储获取主题设置
      const savedTheme = localStorage.getItem('theme')
      if (savedTheme) {
        isDark.value = savedTheme === 'dark'
        appStore.setTheme(savedTheme)
      } else {
        // 检测系统主题偏好
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        isDark.value = prefersDark
        appStore.setTheme(prefersDark ? 'dark' : 'light')
      }
      
      // 监听系统主题变化
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          isDark.value = e.matches
          appStore.setTheme(e.matches ? 'dark' : 'light')
        }
      })
    }
    
    /**
     * 初始化用户信息
     */
    const initUser = async () => {
      const token = localStorage.getItem('token')
      if (token) {
        try {
          // 验证 token 有效性并获取用户信息
          await userStore.getUserInfo()
        } catch (error) {
          console.error('获取用户信息失败:', error)
          // token 无效，清除本地存储
          localStorage.removeItem('token')
          userStore.logout()
        }
      }
    }
    
    /**
     * 初始化应用配置
     */
    const initAppConfig = async () => {
      try {
        // 获取应用配置信息
        await appStore.getAppConfig()
      } catch (error) {
        console.error('获取应用配置失败:', error)
      }
    }
    
    /**
     * 监听主题变化
     */
    watch(
      () => appStore.theme,
      (newTheme) => {
        isDark.value = newTheme === 'dark'
        // 更新 HTML 根元素的 class
        document.documentElement.className = newTheme === 'dark' ? 'dark' : ''
      },
      { immediate: true }
    )
    
    /**
     * 监听路由变化
     */
    watch(
      () => router.currentRoute.value,
      (to, from) => {
        // 路由变化时的处理逻辑
        console.log('路由变化:', from?.path, '->', to.path)
        
        // 更新页面标题
        if (to.meta.title) {
          document.title = `${to.meta.title} - Wit Mall`
        }
      },
      { immediate: true }
    )
    
    /**
     * 组件挂载后执行
     */
    onMounted(() => {
      initApp()
    })
    
    return {
      isLoading,
      isDark
    }
  }
}
</script>

<style lang="scss">
/**
 * 全局样式
 */

// 应用根容器
#app {
  width: 100%;
  min-height: 100vh;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
  transition: all 0.3s ease;
}

// 全局加载样式
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 暗色主题适配
.dark {
  color-scheme: dark;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-fill-color-darker);
  }
}

// 响应式设计
@media (max-width: 768px) {
  #app {
    font-size: 14px;
  }
}
</style>

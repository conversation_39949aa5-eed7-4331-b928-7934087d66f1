/**
 * 全局样式文件
 * 
 * 主要功能：
 * 1. 重置默认样式
 * 2. 定义全局样式
 * 3. 工具类样式
 * 4. 响应式样式
 * 5. 自定义组件样式
 * 
 * <AUTHOR>
 */

// 导入变量
@import './variables.scss';

// ==================== 重置样式 ====================

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
  font-size: 14px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
}

body {
  height: 100%;
  font-family: $font-family-base;
  font-size: $font-size-base;
  color: $text-color-primary;
  background-color: $background-color-base;
  overflow-x: hidden;
}

// 移除默认样式
ul, ol {
  list-style: none;
}

a {
  color: $primary-color;
  text-decoration: none;
  transition: $transition-color;
  
  &:hover {
    color: $primary-light;
  }
  
  &:focus {
    outline: none;
  }
}

button {
  border: none;
  outline: none;
  cursor: pointer;
  background: transparent;
}

input, textarea, select {
  outline: none;
  border: none;
  background: transparent;
  font-family: inherit;
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

// ==================== 工具类样式 ====================

// 文本对齐
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

// 文本颜色
.text-primary { color: $primary-color !important; }
.text-success { color: $success-color !important; }
.text-warning { color: $warning-color !important; }
.text-danger { color: $danger-color !important; }
.text-info { color: $info-color !important; }
.text-muted { color: $text-color-secondary !important; }
.text-white { color: #ffffff !important; }

// 背景颜色
.bg-primary { background-color: $primary-color !important; }
.bg-success { background-color: $success-color !important; }
.bg-warning { background-color: $warning-color !important; }
.bg-danger { background-color: $danger-color !important; }
.bg-info { background-color: $info-color !important; }
.bg-light { background-color: $background-color-light !important; }
.bg-white { background-color: #ffffff !important; }

// 边距
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mr-0 { margin-right: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }

.m-1 { margin: $spacing-xs !important; }
.mt-1 { margin-top: $spacing-xs !important; }
.mr-1 { margin-right: $spacing-xs !important; }
.mb-1 { margin-bottom: $spacing-xs !important; }
.ml-1 { margin-left: $spacing-xs !important; }

.m-2 { margin: $spacing-sm !important; }
.mt-2 { margin-top: $spacing-sm !important; }
.mr-2 { margin-right: $spacing-sm !important; }
.mb-2 { margin-bottom: $spacing-sm !important; }
.ml-2 { margin-left: $spacing-sm !important; }

.m-3 { margin: $spacing-md !important; }
.mt-3 { margin-top: $spacing-md !important; }
.mr-3 { margin-right: $spacing-md !important; }
.mb-3 { margin-bottom: $spacing-md !important; }
.ml-3 { margin-left: $spacing-md !important; }

.m-4 { margin: $spacing-lg !important; }
.mt-4 { margin-top: $spacing-lg !important; }
.mr-4 { margin-right: $spacing-lg !important; }
.mb-4 { margin-bottom: $spacing-lg !important; }
.ml-4 { margin-left: $spacing-lg !important; }

.m-5 { margin: $spacing-xl !important; }
.mt-5 { margin-top: $spacing-xl !important; }
.mr-5 { margin-right: $spacing-xl !important; }
.mb-5 { margin-bottom: $spacing-xl !important; }
.ml-5 { margin-left: $spacing-xl !important; }

// 内边距
.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pr-0 { padding-right: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }

.p-1 { padding: $spacing-xs !important; }
.pt-1 { padding-top: $spacing-xs !important; }
.pr-1 { padding-right: $spacing-xs !important; }
.pb-1 { padding-bottom: $spacing-xs !important; }
.pl-1 { padding-left: $spacing-xs !important; }

.p-2 { padding: $spacing-sm !important; }
.pt-2 { padding-top: $spacing-sm !important; }
.pr-2 { padding-right: $spacing-sm !important; }
.pb-2 { padding-bottom: $spacing-sm !important; }
.pl-2 { padding-left: $spacing-sm !important; }

.p-3 { padding: $spacing-md !important; }
.pt-3 { padding-top: $spacing-md !important; }
.pr-3 { padding-right: $spacing-md !important; }
.pb-3 { padding-bottom: $spacing-md !important; }
.pl-3 { padding-left: $spacing-md !important; }

.p-4 { padding: $spacing-lg !important; }
.pt-4 { padding-top: $spacing-lg !important; }
.pr-4 { padding-right: $spacing-lg !important; }
.pb-4 { padding-bottom: $spacing-lg !important; }
.pl-4 { padding-left: $spacing-lg !important; }

.p-5 { padding: $spacing-xl !important; }
.pt-5 { padding-top: $spacing-xl !important; }
.pr-5 { padding-right: $spacing-xl !important; }
.pb-5 { padding-bottom: $spacing-xl !important; }
.pl-5 { padding-left: $spacing-xl !important; }

// 显示/隐藏
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

// Flex 布局
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

// 位置
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

// 浮动
.float-left { float: left !important; }
.float-right { float: right !important; }
.float-none { float: none !important; }

// 清除浮动
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

// 文本省略
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 圆角
.rounded { border-radius: $border-radius-base !important; }
.rounded-sm { border-radius: $border-radius-small !important; }
.rounded-lg { border-radius: $border-radius-large !important; }
.rounded-circle { border-radius: $border-radius-circle !important; }
.rounded-0 { border-radius: 0 !important; }

// 阴影
.shadow { box-shadow: $box-shadow-base !important; }
.shadow-sm { box-shadow: $box-shadow-light !important; }
.shadow-lg { box-shadow: $box-shadow-dark !important; }
.shadow-none { box-shadow: none !important; }

// 边框
.border { border: 1px solid $border-color-base !important; }
.border-top { border-top: 1px solid $border-color-base !important; }
.border-right { border-right: 1px solid $border-color-base !important; }
.border-bottom { border-bottom: 1px solid $border-color-base !important; }
.border-left { border-left: 1px solid $border-color-base !important; }
.border-0 { border: 0 !important; }

// ==================== 自定义组件样式 ====================

// 卡片样式
.card {
  background: #ffffff;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: $card-padding;
  transition: $transition-box-shadow;
  
  &:hover {
    box-shadow: $card-shadow-hover;
  }
  
  &.card-bordered {
    border: 1px solid $border-color-base;
    box-shadow: none;
  }
}

// 页面容器
.page-container {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $container-padding;
}

// 页面头部
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-lg;
  padding-bottom: $spacing-md;
  border-bottom: 1px solid $border-color-base;
  
  .page-title {
    font-size: $font-size-large;
    font-weight: $font-weight-medium;
    color: $text-color-primary;
  }
  
  .page-actions {
    display: flex;
    gap: $spacing-sm;
  }
}

// 加载状态
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  color: $text-color-secondary;
}

// 空状态
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  color: $text-color-secondary;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: $spacing-md;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: $font-size-base;
  }
}

// ==================== 响应式样式 ====================

// 小屏幕
@media (max-width: $breakpoint-sm) {
  .container {
    padding: 0 $spacing-md;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-md;
    
    .page-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }
  
  // 隐藏/显示类
  .d-sm-none { display: none !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
}

// 中等屏幕
@media (min-width: $breakpoint-md) {
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
}

// 大屏幕
@media (min-width: $breakpoint-lg) {
  .d-lg-none { display: none !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
}

// 超大屏幕
@media (min-width: $breakpoint-xl) {
  .d-xl-none { display: none !important; }
  .d-xl-block { display: block !important; }
  .d-xl-flex { display: flex !important; }
}

// ==================== 动画样式 ====================

// 淡入淡出
.fade-enter-active,
.fade-leave-active {
  transition: opacity $transition-duration-base;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滑动
.slide-enter-active,
.slide-leave-active {
  transition: transform $transition-duration-base;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

// 缩放
.zoom-enter-active,
.zoom-leave-active {
  transition: transform $transition-duration-base;
}

.zoom-enter-from,
.zoom-leave-to {
  transform: scale(0);
}

// ==================== 滚动条样式 ====================

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: $fill-color-lighter;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: $fill-color-dark;
  border-radius: 4px;
  
  &:hover {
    background: $fill-color-darker;
  }
}

// Firefox 滚动条
* {
  scrollbar-width: thin;
  scrollbar-color: $fill-color-dark $fill-color-lighter;
}

/**
 * SCSS 变量定义
 * 
 * 主要功能：
 * 1. 定义全局颜色变量
 * 2. 定义尺寸变量
 * 3. 定义字体变量
 * 4. 定义动画变量
 * 5. 定义断点变量
 * 
 * <AUTHOR>
 */

// ==================== 颜色变量 ====================

// 主色调
$primary-color: #409eff;
$primary-light: #79bbff;
$primary-lighter: #a0cfff;
$primary-dark: #337ecc;

// 成功色
$success-color: #67c23a;
$success-light: #95d475;
$success-lighter: #b3e19d;
$success-dark: #529b2e;

// 警告色
$warning-color: #e6a23c;
$warning-light: #ebb563;
$warning-lighter: #f0c78a;
$warning-dark: #b88230;

// 危险色
$danger-color: #f56c6c;
$danger-light: #f78989;
$danger-lighter: #fab6b6;
$danger-dark: #c45656;

// 信息色
$info-color: #909399;
$info-light: #a6a9ad;
$info-lighter: #c8c9cc;
$info-dark: #73767a;

// 文本颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #c0c4cc;

// 边框颜色
$border-color-base: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

// 背景颜色
$background-color-base: #f5f7fa;
$background-color-light: #fafafa;
$background-color-lighter: #ffffff;

// 填充颜色
$fill-color-base: #f0f2f5;
$fill-color-light: #f5f7fa;
$fill-color-lighter: #fafafa;
$fill-color-extra-light: #fafcff;
$fill-color-blank: #ffffff;
$fill-color-dark: #ebedf0;
$fill-color-darker: #e6e8eb;

// 阴影颜色
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// ==================== 尺寸变量 ====================

// 基础尺寸
$base-size: 4px;
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-large: 6px;
$border-radius-round: 20px;
$border-radius-circle: 50%;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 组件尺寸
$component-size-large: 40px;
$component-size-default: 32px;
$component-size-small: 24px;

// 布局尺寸
$header-height: 60px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;
$footer-height: 60px;

// 容器最大宽度
$container-max-width: 1200px;
$container-padding: 20px;

// ==================== 字体变量 ====================

// 字体族
$font-family-base: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
$font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

// 字体大小
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;

// 行高
$line-height-base: 1.5;
$line-height-small: 1.2;
$line-height-large: 1.8;

// ==================== 动画变量 ====================

// 动画时长
$transition-duration-fast: 0.2s;
$transition-duration-base: 0.3s;
$transition-duration-slow: 0.5s;

// 动画函数
$transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);

// 常用动画
$transition-base: all $transition-duration-base $transition-function-ease-in-out-bezier;
$transition-fade: opacity $transition-duration-base $transition-function-fast-bezier;
$transition-md-fade: transform $transition-duration-base $transition-function-fast-bezier, opacity $transition-duration-base $transition-function-fast-bezier;
$transition-border: border-color $transition-duration-base $transition-function-ease-in-out-bezier;
$transition-box-shadow: box-shadow $transition-duration-base $transition-function-ease-in-out-bezier;
$transition-color: color $transition-duration-base $transition-function-ease-in-out-bezier;

// ==================== 断点变量 ====================

// 响应式断点
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1600px;

// ==================== Z-index 变量 ====================

$z-index-normal: 1;
$z-index-top: 1000;
$z-index-popper: 2000;
$z-index-message: 3000;
$z-index-message-box: 4000;

// ==================== 自定义变量 ====================

// 品牌色
$brand-primary: #1890ff;
$brand-success: #52c41a;
$brand-warning: #faad14;
$brand-error: #f5222d;
$brand-info: #1890ff;

// 渐变色
$gradient-primary: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
$gradient-success: linear-gradient(135deg, $success-color 0%, $success-light 100%);
$gradient-warning: linear-gradient(135deg, $warning-color 0%, $warning-light 100%);
$gradient-danger: linear-gradient(135deg, $danger-color 0%, $danger-light 100%);

// 卡片样式
$card-border-radius: 8px;
$card-padding: 20px;
$card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
$card-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);

// 表单样式
$form-item-margin-bottom: 22px;
$form-label-width: 100px;
$input-height-large: 40px;
$input-height-default: 32px;
$input-height-small: 24px;

// 表格样式
$table-header-background: #fafafa;
$table-row-hover-background: #f5f7fa;
$table-border-color: #ebeef5;

// 按钮样式
$button-border-radius: 4px;
$button-padding-horizontal: 15px;
$button-padding-vertical: 8px;

// 导航样式
$nav-item-padding: 12px 16px;
$nav-item-border-radius: 4px;
$nav-active-background: rgba($primary-color, 0.1);
$nav-active-color: $primary-color;

// 侧边栏样式
$sidebar-background: #001529;
$sidebar-text-color: rgba(255, 255, 255, 0.65);
$sidebar-active-background: $primary-color;
$sidebar-active-text-color: #ffffff;

// 头部样式
$header-background: #ffffff;
$header-border-bottom: 1px solid $border-color-base;
$header-text-color: $text-color-primary;

// 页脚样式
$footer-background: #f0f2f5;
$footer-text-color: $text-color-secondary;
$footer-border-top: 1px solid $border-color-base;

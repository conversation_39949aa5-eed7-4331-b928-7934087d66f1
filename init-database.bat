@echo off
chcp 65001 >nul
echo ========================================
echo    Wit Mall 数据库初始化脚本
echo ========================================
echo.

:: 设置数据库连接参数
set DB_HOST=localhost
set DB_PORT=3306
set DB_USER=root
set DB_PASSWORD=123456

echo 🔍 检查MySQL连接...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo ❌ 无法连接到MySQL数据库
    echo 请检查：
    echo   1. MySQL服务是否启动
    echo   2. 连接参数是否正确（主机：%DB_HOST%，端口：%DB_PORT%，用户：%DB_USER%）
    echo   3. 用户密码是否正确
    pause
    exit /b 1
)
echo ✅ MySQL连接成功

echo.
echo ========================================
echo    开始执行数据库初始化
echo ========================================

echo 📋 执行顺序：
echo   1. 创建所有数据库和基础表结构
echo   2. 创建用户服务表结构
echo   3. 创建商品服务表结构  
echo   4. 创建库存服务表结构
echo   5. 创建订单服务表结构
echo   6. 创建支付服务表结构
echo   7. 插入初始化数据
echo.

:: 1. 执行主初始化脚本
echo 🚀 [1/7] 执行主数据库初始化...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% < sql/00_init_all_databases.sql
if errorlevel 1 (
    echo ❌ 主数据库初始化失败
    goto :error
)
echo ✅ 主数据库初始化完成

:: 2. 执行用户服务表结构
echo 🚀 [2/7] 创建用户服务表结构...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% < sql/01_wit_user.sql
if errorlevel 1 (
    echo ❌ 用户服务表创建失败
    goto :error
)
echo ✅ 用户服务表创建完成

:: 3. 执行商品服务表结构
echo 🚀 [3/7] 创建商品服务表结构...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% < sql/02_wit_product.sql
if errorlevel 1 (
    echo ❌ 商品服务表创建失败
    goto :error
)
echo ✅ 商品服务表创建完成

:: 4. 执行库存服务表结构
echo 🚀 [4/7] 创建库存服务表结构...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% < sql/03_wit_inventory.sql
if errorlevel 1 (
    echo ❌ 库存服务表创建失败
    goto :error
)
echo ✅ 库存服务表创建完成

:: 5. 执行订单服务表结构
echo 🚀 [5/7] 创建订单服务表结构...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% < sql/04_wit_order.sql
if errorlevel 1 (
    echo ❌ 订单服务表创建失败
    goto :error
)
echo ✅ 订单服务表创建完成

:: 6. 执行支付服务表结构
echo 🚀 [6/7] 创建支付服务表结构...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% < sql/05_wit_payment.sql
if errorlevel 1 (
    echo ❌ 支付服务表创建失败
    goto :error
)
echo ✅ 支付服务表创建完成

:: 7. 插入初始化数据
echo 🚀 [7/7] 插入初始化数据...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% < sql/99_init_data.sql
if errorlevel 1 (
    echo ❌ 初始化数据插入失败
    goto :error
)
echo ✅ 初始化数据插入完成

echo.
echo ========================================
echo    数据库初始化完成
echo ========================================
echo.
echo 🎉 所有数据库表结构和初始数据创建完成！
echo.
echo 📊 已创建的数据库：
echo   • wit_user          - 用户服务数据库
echo   • wit_product       - 商品服务数据库  
echo   • wit_inventory     - 库存服务数据库
echo   • wit_order         - 订单服务数据库
echo   • wit_payment       - 支付服务数据库
echo   • wit_cart          - 购物车服务数据库
echo   • wit_marketing     - 营销服务数据库
echo   • wit_review        - 评价服务数据库
echo   • wit_notification  - 通知服务数据库
echo   • wit_file          - 文件服务数据库
echo   • wit_system        - 系统服务数据库
echo   • wit_schedule      - 定时任务数据库
echo   • wit_analytics     - 数据分析数据库
echo   • wit_aftersales    - 售后服务数据库
echo   • wit_search        - 搜索服务数据库
echo   • wit_recommendation - 推荐服务数据库
echo.
echo 👤 默认账号信息：
echo   管理员：admin / 123456
echo   测试用户：testuser / 123456
echo.
echo 💡 提示：
echo   1. 请妥善保管数据库账号密码
echo   2. 生产环境请修改默认密码
echo   3. 建议定期备份数据库
echo.
goto :end

:error
echo.
echo ❌ 数据库初始化失败！
echo 请检查错误信息并重试
echo.
pause
exit /b 1

:end
pause

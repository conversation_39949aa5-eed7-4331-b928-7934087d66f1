# Wit Mall 项目诊断脚本 (PowerShell 版本)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Wit Mall 项目诊断脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 1. 检查 Java 版本
Write-Host "1. 检查 Java 版本..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1
    Write-Host "✅ Java 版本检查成功" -ForegroundColor Green
    $javaVersion | Select-Object -First 1
} catch {
    Write-Host "❌ Java 未安装或不在 PATH 中" -ForegroundColor Red
}
Write-Host ""

# 2. 检查 Maven 版本
Write-Host "2. 检查 Maven 版本..." -ForegroundColor Yellow
try {
    $mavenVersion = mvn -version 2>&1
    Write-Host "✅ Maven 版本检查成功" -ForegroundColor Green
    $mavenVersion | Select-Object -First 1
} catch {
    Write-Host "❌ Maven 未安装或不在 PATH 中" -ForegroundColor Red
}
Write-Host ""

# 3. 检查项目结构
Write-Host "3. 检查项目结构..." -ForegroundColor Yellow

# 检查根目录 pom.xml
if (Test-Path "pom.xml") {
    Write-Host "✅ 根目录 pom.xml 存在" -ForegroundColor Green
} else {
    Write-Host "❌ 根目录 pom.xml 不存在" -ForegroundColor Red
}

# 检查子模块
$modules = @(
    "wit-common", "wit-gateway", "wit-auth", "wit-user", "wit-product", 
    "wit-order", "wit-cart", "wit-payment", "wit-inventory", "wit-marketing", 
    "wit-search", "wit-recommendation", "wit-review", "wit-notification", 
    "wit-file", "wit-system", "wit-schedule", "wit-analytics", "wit-aftersales"
)

Write-Host ""
Write-Host "检查子模块:" -ForegroundColor Yellow

foreach ($module in $modules) {
    Write-Host "检查 $module..." -ForegroundColor Cyan
    
    if (Test-Path "$module\pom.xml") {
        Write-Host "  ✅ $module - pom.xml 存在" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $module - pom.xml 不存在" -ForegroundColor Red
    }
    
    if (Test-Path "$module\src\main\java") {
        Write-Host "  ✅ $module - src/main/java 存在" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $module - src/main/java 不存在" -ForegroundColor Red
    }
}

Write-Host ""

# 4. 检查 Maven 依赖
Write-Host "4. 检查 Maven 依赖..." -ForegroundColor Yellow
try {
    Write-Host "正在检查依赖解析..." -ForegroundColor Cyan
    $dependencyResult = mvn dependency:resolve -q 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Maven 依赖解析成功" -ForegroundColor Green
    } else {
        Write-Host "❌ Maven 依赖解析失败" -ForegroundColor Red
        Write-Host "错误信息: $dependencyResult" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Maven 依赖检查失败" -ForegroundColor Red
}
Write-Host ""

# 5. 检查编译状态
Write-Host "5. 检查编译状态..." -ForegroundColor Yellow
try {
    Write-Host "正在编译项目..." -ForegroundColor Cyan
    $compileResult = mvn compile -q 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 项目编译成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 项目编译失败" -ForegroundColor Red
        Write-Host "错误信息: $compileResult" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 项目编译检查失败" -ForegroundColor Red
}
Write-Host ""

# 6. 检查 IDE 配置文件
Write-Host "6. 检查 IDE 配置..." -ForegroundColor Yellow

# 检查 IntelliJ IDEA 配置
if (Test-Path ".idea") {
    Write-Host "✅ IntelliJ IDEA 配置目录存在" -ForegroundColor Green
} else {
    Write-Host "⚠️  IntelliJ IDEA 配置目录不存在" -ForegroundColor Yellow
}

# 检查 Eclipse 配置
if (Test-Path ".project") {
    Write-Host "✅ Eclipse 项目配置存在" -ForegroundColor Green
} else {
    Write-Host "⚠️  Eclipse 项目配置不存在" -ForegroundColor Yellow
}

Write-Host ""

# 7. 检查目标目录
Write-Host "7. 检查编译输出..." -ForegroundColor Yellow
$hasTargetDirs = $false
foreach ($module in $modules) {
    if (Test-Path "$module\target") {
        $hasTargetDirs = $true
        break
    }
}

if ($hasTargetDirs) {
    Write-Host "✅ 发现编译输出目录" -ForegroundColor Green
} else {
    Write-Host "⚠️  未发现编译输出目录，可能需要编译" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "诊断完成！" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "建议操作:" -ForegroundColor Yellow
Write-Host "1. 如果看到编译错误，请先运行: mvn clean install" -ForegroundColor White
Write-Host "2. 如果使用 IntelliJ IDEA，请重新导入项目:" -ForegroundColor White
Write-Host "   - 关闭项目" -ForegroundColor Gray
Write-Host "   - 删除 .idea 目录" -ForegroundColor Gray
Write-Host "   - 重新打开项目 (Open -> 选择 pom.xml)" -ForegroundColor Gray
Write-Host "3. 如果使用 Eclipse，请刷新项目并重新导入" -ForegroundColor White
Write-Host "4. 确保使用 Java 17 和正确的 Maven 配置" -ForegroundColor White
Write-Host ""

# 等待用户按键
Write-Host "按任意键继续..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

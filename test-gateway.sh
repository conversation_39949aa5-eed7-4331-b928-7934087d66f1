#!/bin/bash

# Wit Mall 多租户网关测试脚本
# 作者: Wit
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
GATEWAY_URL="http://localhost:8080"
NGINX_URL="http://localhost"
TENANT_ID="3921"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# 测试函数
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected="$3"
    
    log_test "测试: $name"
    echo "URL: $url"
    
    response=$(curl -s "$url" 2>/dev/null || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        log_error "❌ 请求失败"
        return 1
    fi
    
    if [[ -n "$expected" ]] && [[ "$response" != *"$expected"* ]]; then
        log_error "❌ 响应不符合预期"
        echo "期望包含: $expected"
        echo "实际响应: $response"
        return 1
    fi
    
    log_info "✅ 测试通过"
    echo "响应: $response"
    echo ""
    return 0
}

# 主测试函数
main() {
    echo "🧪 Wit Mall 多租户网关测试"
    echo "=========================="
    echo ""
    
    local passed=0
    local failed=0
    
    # 1. 测试网关健康检查
    if test_endpoint "网关健康检查" "$GATEWAY_URL/gateway/health" "UP"; then
        ((passed++))
    else
        ((failed++))
    fi
    
    # 2. 测试网关信息
    if test_endpoint "网关信息" "$GATEWAY_URL/gateway/info" "Wit Mall Gateway"; then
        ((passed++))
    else
        ((failed++))
    fi
    
    # 3. 测试租户验证
    if test_endpoint "租户验证" "$GATEWAY_URL/gateway/tenants/$TENANT_ID/validate" "valid"; then
        ((passed++))
    else
        ((failed++))
    fi
    
    # 4. 测试路由测试接口
    if test_endpoint "路由测试" "$GATEWAY_URL/gateway/test/route?tenantId=$TENANT_ID&service=user" "路由测试成功"; then
        ((passed++))
    else
        ((failed++))
    fi
    
    # 5. 测试多租户路由格式
    if test_endpoint "多租户路由格式测试" "$GATEWAY_URL/gateway/test/route" "路由测试成功"; then
        ((passed++))
    else
        ((failed++))
    fi
    
    # 6. 测试Nginx代理
    if test_endpoint "Nginx代理健康检查" "$NGINX_URL/gateway/health" "UP"; then
        ((passed++))
    else
        ((failed++))
        log_warn "Nginx代理测试失败，可能Nginx未启动或配置有误"
    fi
    
    # 7. 测试Nginx多租户路由代理
    if test_endpoint "Nginx多租户路由代理" "$NGINX_URL/gateway/test/route?tenantId=$TENANT_ID&service=user" "路由测试成功"; then
        ((passed++))
    else
        ((failed++))
        log_warn "Nginx多租户路由代理测试失败"
    fi
    
    # 8. 测试CORS支持
    log_test "测试CORS支持"
    cors_response=$(curl -s -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: GET" -H "Access-Control-Request-Headers: X-Requested-With" -X OPTIONS "$NGINX_URL/gateway/health" -I 2>/dev/null || echo "ERROR")
    
    if [[ "$cors_response" == *"Access-Control-Allow-Origin"* ]]; then
        log_info "✅ CORS支持测试通过"
        ((passed++))
    else
        log_error "❌ CORS支持测试失败"
        ((failed++))
    fi
    echo ""
    
    # 9. 测试错误处理
    if test_endpoint "错误处理测试" "$GATEWAY_URL/nonexistent" ""; then
        log_warn "错误处理测试：应该返回错误，但返回了成功响应"
    else
        log_info "✅ 错误处理测试通过（正确返回错误）"
        ((passed++))
    fi
    
    # 10. 性能测试（简单）
    log_test "简单性能测试（10个并发请求）"
    start_time=$(date +%s.%N)
    
    for i in {1..10}; do
        curl -s "$GATEWAY_URL/gateway/health" > /dev/null &
    done
    wait
    
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc)
    
    log_info "✅ 性能测试完成，10个请求耗时: ${duration}秒"
    ((passed++))
    echo ""
    
    # 测试结果汇总
    echo "📊 测试结果汇总"
    echo "=============="
    echo "✅ 通过: $passed"
    echo "❌ 失败: $failed"
    echo "📈 成功率: $(echo "scale=2; $passed * 100 / ($passed + $failed)" | bc)%"
    echo ""
    
    if [ $failed -eq 0 ]; then
        log_info "🎉 所有测试通过！网关部署成功！"
        echo ""
        echo "🚀 可以开始使用多租户网关了："
        echo "   示例请求: curl $NGINX_URL/$TENANT_ID/user/api/v1/users"
        echo "   管理接口: $GATEWAY_URL/gateway/info"
        return 0
    else
        log_error "❌ 有 $failed 个测试失败，请检查配置和日志"
        return 1
    fi
}

# 执行测试
main "$@"

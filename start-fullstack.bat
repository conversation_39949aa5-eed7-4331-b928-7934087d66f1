@echo off
chcp 65001 >nul
echo ========================================
echo    Wit Mall 全栈项目启动脚本
echo ========================================
echo.

:: 检查Java环境
echo 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Java，请先安装JDK 17+
    pause
    exit /b 1
)
echo ✅ Java环境正常

:: 检查Maven环境
echo 检查Maven环境...
mvn -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Maven，请先安装Maven 3.6+
    pause
    exit /b 1
)
echo ✅ Maven环境正常

:: 检查Docker环境
echo 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Docker，请先安装Docker
    pause
    exit /b 1
)
echo ✅ Docker环境正常

:: 检查Node.js环境
echo 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Node.js，请先安装Node.js 16+
    pause
    exit /b 1
)
echo ✅ Node.js环境正常

echo.
echo ========================================
echo    启动基础设施服务
echo ========================================

:: 启动Docker Compose服务
echo 🐳 启动基础设施服务（MySQL、Redis、Nacos等）...
docker-compose up -d
if errorlevel 1 (
    echo ❌ 基础设施启动失败
    pause
    exit /b 1
)

echo ✅ 基础设施服务启动成功
echo 等待服务完全启动（30秒）...
timeout /t 30 /nobreak >nul

echo.
echo ========================================
echo    编译后端项目
echo ========================================

:: 编译Maven项目
echo 🔨 编译后端项目...
mvn clean compile -DskipTests -q
if errorlevel 1 (
    echo ❌ 后端项目编译失败
    pause
    exit /b 1
)
echo ✅ 后端项目编译完成

echo.
echo ========================================
echo    启动后端服务
echo ========================================

:: 启动网关服务
echo 🚀 启动网关服务...
start "wit-gateway" cmd /k "cd wit-gateway && mvn spring-boot:run -Dspring-boot.run.profiles=dev"
timeout /t 10 /nobreak >nul

:: 启动认证服务
echo 🚀 启动认证服务...
start "wit-auth" cmd /k "cd wit-auth && mvn spring-boot:run -Dspring-boot.run.profiles=dev"
timeout /t 5 /nobreak >nul

:: 启动用户服务
echo 🚀 启动用户服务...
start "wit-user" cmd /k "cd wit-user && mvn spring-boot:run -Dspring-boot.run.profiles=dev"
timeout /t 5 /nobreak >nul

:: 启动商品服务
echo 🚀 启动商品服务...
start "wit-product" cmd /k "cd wit-product && mvn spring-boot:run -Dspring-boot.run.profiles=dev"
timeout /t 5 /nobreak >nul

:: 启动文件服务
echo 🚀 启动文件服务...
start "wit-file" cmd /k "cd wit-file && mvn spring-boot:run -Dspring-boot.run.profiles=dev"
timeout /t 5 /nobreak >nul

echo ✅ 后端服务启动完成

echo.
echo ========================================
echo    安装前端依赖
echo ========================================

:: 进入前端目录并安装依赖
cd /d "%~dp0wit-mall-web"
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        cd /d "%~dp0"
        pause
        exit /b 1
    )
    echo ✅ 前端依赖安装完成
) else (
    echo ✅ 前端依赖已安装
)

echo.
echo ========================================
echo    启动前端服务
echo ========================================

:: 启动前端开发服务器
echo 🚀 启动前端开发服务器...
start "wit-mall-web" cmd /k "npm run dev"

:: 返回根目录
cd /d "%~dp0"

echo.
echo ========================================
echo    启动完成
echo ========================================
echo.
echo 🎉 全栈项目启动完成！
echo.
echo 📋 服务地址：
echo   前端应用:     http://localhost:3000
echo   API网关:      http://localhost:8080
echo   API文档:      http://localhost:8080/doc.html
echo   Nacos控制台:  http://localhost:8848/nacos (nacos/nacos)
echo.
echo 💡 提示：
echo   - 所有服务都在独立的命令行窗口中运行
echo   - 关闭对应窗口即可停止相应服务
echo   - 建议等待2-3分钟让所有服务完全启动
echo.

pause

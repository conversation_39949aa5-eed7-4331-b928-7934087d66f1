# 🚀 Wit Mall 代码生成器使用指南

## 📋 概述

Wit Mall 项目集成了 MyBatis Plus 代码生成器，可以根据数据库表自动生成实体类、Mapper接口、Service接口及实现类、Controller等代码，大大提高开发效率。

## 🛠️ 功能特性

### ✅ 支持生成的代码
- **Entity** - 实体类（包含Lombok注解、Swagger注解）
- **Mapper** - MyBatis Mapper接口和XML文件
- **Service** - 服务接口和实现类
- **Controller** - REST控制器

### ✅ 内置配置
- **统一基类** - 继承BaseEntity，包含公共字段
- **逻辑删除** - 自动配置deleted字段
- **乐观锁** - 自动配置version字段
- **自动填充** - 创建时间、更新时间、操作人自动填充
- **Swagger文档** - 自动生成API文档注解

## 📁 项目结构

```
wit-common/
└── src/test/java/com/wit/common/generator/
    ├── CodeGenerator.java        # 交互式代码生成器
    └── SimpleCodeGenerator.java  # 简化版代码生成器
```

## 🎯 使用方式

### 方式一：交互式代码生成器（推荐）

#### 1. 准备数据库表
```sql
-- 示例：用户表
CREATE TABLE `user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(100) NOT NULL COMMENT '密码',
    `email` VARCHAR(100) COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '手机号',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 2. 运行代码生成器
```bash
# 在IDEA中右键运行
wit-common/src/test/java/com/wit/common/generator/CodeGenerator.java

# 或使用Maven命令
cd wit-common
mvn test-compile exec:java -Dexec.mainClass="com.wit.common.generator.CodeGenerator"
```

#### 3. 按提示输入参数
```
========================================
   Wit Mall 代码生成器
========================================
请输入服务名称 (如: user, product, order): user
请输入表名 (多个表用逗号分隔): user,user_role
请输入表前缀 (如: t_, 没有则直接回车): 
请输入作者名称: Wit

请输入数据库连接信息:
数据库地址 (默认: localhost:3306): 
数据库名称 (默认: wit_user): 
数据库用户名 (默认: root): 
数据库密码 (默认: 123456): 

代码生成完成！
```

### 方式二：简化版代码生成器

#### 1. 修改配置参数
编辑 `SimpleCodeGenerator.java` 文件中的配置：

```java
// 数据库配置
private static final String DB_URL = "************************************?...";
private static final String DB_USERNAME = "root";
private static final String DB_PASSWORD = "123456";

// 生成配置
private static final String AUTHOR = "Wit";
private static final String SERVICE_NAME = "user"; // 修改服务名
private static final String TABLE_NAMES = "user,user_role"; // 修改表名
private static final String TABLE_PREFIX = ""; // 表前缀
```

#### 2. 运行生成器
```bash
# 在IDEA中右键运行
wit-common/src/test/java/com/wit/common/generator/SimpleCodeGenerator.java
```

## 📂 生成的代码结构

运行成功后，会在对应的服务模块中生成以下代码：

```
wit-user/
├── src/main/java/com/wit/user/
│   ├── entity/
│   │   ├── User.java              # 用户实体类
│   │   └── UserRole.java          # 用户角色实体类
│   ├── mapper/
│   │   ├── UserMapper.java        # 用户Mapper接口
│   │   └── UserRoleMapper.java    # 用户角色Mapper接口
│   ├── service/
│   │   ├── UserService.java       # 用户服务接口
│   │   ├── UserServiceImpl.java   # 用户服务实现
│   │   ├── UserRoleService.java   # 用户角色服务接口
│   │   └── UserRoleServiceImpl.java # 用户角色服务实现
│   └── controller/
│       ├── UserController.java    # 用户控制器
│       └── UserRoleController.java # 用户角色控制器
└── src/main/resources/mapper/
    ├── UserMapper.xml             # 用户Mapper XML
    └── UserRoleMapper.xml         # 用户角色Mapper XML
```

## 📝 生成的代码示例

### 1. 实体类示例
```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user")
@ApiModel(value = "User对象", description = "用户表")
public class User extends BaseEntity {

    @ApiModelProperty("用户名")
    @TableField("username")
    private String username;

    @ApiModelProperty("密码")
    @TableField("password")
    private String password;

    @ApiModelProperty("邮箱")
    @TableField("email")
    private String email;

    @ApiModelProperty("手机号")
    @TableField("phone")
    private String phone;

    @ApiModelProperty("状态：1-正常，0-禁用")
    @TableField("status")
    private Integer status;
}
```

### 2. Mapper接口示例
```java
@Mapper
public interface UserMapper extends BaseMapper<User> {
    // 继承BaseMapper，拥有基本的CRUD方法
    // 可以在这里添加自定义方法
}
```

### 3. Service接口示例
```java
public interface UserService extends IService<User> {
    // 继承IService，拥有基本的服务方法
    // 可以在这里添加自定义业务方法
}
```

### 4. Service实现类示例
```java
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    // 继承ServiceImpl，实现基本的服务方法
    // 可以在这里实现自定义业务逻辑
}
```

### 5. Controller示例
```java
@RestController
@RequestMapping("/user")
@Api(tags = "用户表")
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping
    @ApiOperation("查询用户表列表")
    public Result<List<User>> list() {
        return Result.success(userService.list());
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询用户表")
    public Result<User> getById(@PathVariable Long id) {
        return Result.success(userService.getById(id));
    }

    @PostMapping
    @ApiOperation("新增用户表")
    public Result<Boolean> save(@RequestBody User user) {
        return Result.success(userService.save(user));
    }

    @PutMapping
    @ApiOperation("修改用户表")
    public Result<Boolean> updateById(@RequestBody User user) {
        return Result.success(userService.updateById(user));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("根据ID删除用户表")
    public Result<Boolean> removeById(@PathVariable Long id) {
        return Result.success(userService.removeById(id));
    }
}
```

## ⚙️ 高级配置

### 1. 自定义模板
如果需要自定义生成的代码模板，可以：

1. 在 `src/main/resources/templates` 目录下创建自定义模板
2. 修改代码生成器中的模板配置

### 2. 添加自定义字段填充
在 `MybatisPlusConfig.java` 中的 `MyMetaObjectHandler` 类中添加：

```java
@Override
public void insertFill(MetaObject metaObject) {
    // 现有填充逻辑...
    
    // 添加自定义字段填充
    this.strictInsertFill(metaObject, "tenantId", Long.class, getCurrentTenantId());
}
```

### 3. 自定义生成策略
可以修改 `CodeGenerator.java` 中的策略配置：

```java
.strategyConfig(builder -> {
    builder
        // 添加更多配置...
        .entityBuilder()
        .superClass("com.wit.common.entity.CustomBaseEntity") // 自定义父类
        .addIgnoreColumns("tenant_id") // 忽略字段
        // 更多配置...
})
```

## 🔧 常见问题

### Q1: 生成的代码中缺少某些注解？
**A:** 检查父项目的依赖管理，确保包含了所需的依赖（如Swagger、Lombok等）。

### Q2: 数据库连接失败？
**A:** 检查数据库连接信息是否正确，确保数据库服务已启动。

### Q3: 生成的代码覆盖了现有代码？
**A:** 代码生成器会覆盖同名文件，建议在生成前备份重要代码，或使用版本控制。

### Q4: 如何只生成部分代码？
**A:** 可以修改生成器配置，禁用不需要的代码生成：

```java
.strategyConfig(builder -> {
    builder
        .controllerBuilder()
        .disable() // 禁用Controller生成
        
        .serviceBuilder()
        .disable() // 禁用Service生成
})
```

### Q5: 生成的表名映射不正确？
**A:** 检查表前缀配置，确保正确设置了 `TABLE_PREFIX` 参数。

## 📋 最佳实践

### 1. 数据库表设计规范
- 统一使用下划线命名法
- 必须包含基础字段：id, create_time, update_time, create_by, update_by, deleted, version
- 合理设置字段注释

### 2. 代码生成流程
1. 先设计数据库表结构
2. 运行代码生成器生成基础代码
3. 在生成的代码基础上添加业务逻辑
4. 编写单元测试验证功能

### 3. 版本控制
- 生成代码前先提交现有代码
- 生成后检查差异，确认无误后再提交
- 重要的自定义代码要做好备份

## 🎯 扩展功能

### 1. 批量生成脚本
可以创建批量生成脚本，一次性生成多个服务的代码：

```bash
#!/bin/bash
# batch-generate.sh

services=("user" "product" "order" "payment")

for service in "${services[@]}"; do
    echo "生成 $service 服务代码..."
    # 调用代码生成器
done
```

### 2. 集成到Maven插件
可以将代码生成器集成到Maven构建过程中：

```xml
<plugin>
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>exec-maven-plugin</artifactId>
    <configuration>
        <mainClass>com.wit.common.generator.CodeGenerator</mainClass>
    </configuration>
</plugin>
```

## 🚀 总结

MyBatis Plus 代码生成器是提高开发效率的重要工具，通过合理配置可以生成符合项目规范的高质量代码。建议在项目初期就配置好代码生成器，后续开发中可以大大减少重复性工作。

祝您使用愉快！🎉

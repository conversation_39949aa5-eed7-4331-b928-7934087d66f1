package com.wit.search;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Search Service 启动类
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.wit.search", "com.wit.common"})
@EnableDiscoveryClient
@EnableDubbo
public class SearchApplication {

    public static void main(String[] args) {
        SpringApplication.run(SearchApplication.class, args);
    }
}

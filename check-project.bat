@echo off
chcp 65001 >nul
echo ========================================
echo    Wit Mall 项目完整性检查
echo ========================================
echo.

:: 检查Java环境
echo 🔍 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Java环境未安装或配置错误
    echo 请安装JDK 17+并配置JAVA_HOME环境变量
    goto :error
) else (
    for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
        set JAVA_VERSION=%%g
    )
    echo ✅ Java环境正常: %JAVA_VERSION%
)

:: 检查Maven环境
echo 🔍 检查Maven环境...
mvn -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Maven环境未安装或配置错误
    echo 请安装Maven 3.6+并配置PATH环境变量
    goto :error
) else (
    for /f "tokens=3" %%g in ('mvn -version 2^>^&1 ^| findstr "Apache Maven"') do (
        set MAVEN_VERSION=%%g
    )
    echo ✅ Maven环境正常: %MAVEN_VERSION%
)

:: 检查Docker环境
echo 🔍 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker环境未安装或未启动
    echo 请安装Docker Desktop并确保服务已启动
    goto :error
) else (
    for /f "tokens=3" %%g in ('docker --version') do (
        set DOCKER_VERSION=%%g
    )
    echo ✅ Docker环境正常: %DOCKER_VERSION%
)

:: 检查Node.js环境
echo 🔍 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js环境未安装
    echo 请安装Node.js 16+
    goto :error
) else (
    for /f "tokens=*" %%g in ('node --version') do (
        set NODE_VERSION=%%g
    )
    echo ✅ Node.js环境正常: %NODE_VERSION%
)

echo.
echo ========================================
echo    检查项目文件结构
echo ========================================

:: 检查后端项目文件
echo 🔍 检查后端项目文件...
if not exist "pom.xml" (
    echo ❌ 缺少根目录pom.xml文件
    goto :error
)
echo ✅ 根pom.xml存在

if not exist "wit-common\pom.xml" (
    echo ❌ 缺少wit-common模块
    goto :error
)
echo ✅ wit-common模块存在

if not exist "wit-gateway\pom.xml" (
    echo ❌ 缺少wit-gateway模块
    goto :error
)
echo ✅ wit-gateway模块存在

if not exist "wit-user\pom.xml" (
    echo ❌ 缺少wit-user模块
    goto :error
)
echo ✅ wit-user模块存在

:: 检查前端项目文件
echo 🔍 检查前端项目文件...
if not exist "wit-mall-web\package.json" (
    echo ❌ 缺少前端项目package.json
    goto :error
)
echo ✅ 前端package.json存在

if not exist "wit-mall-web\vite.config.js" (
    echo ❌ 缺少前端Vite配置文件
    goto :error
)
echo ✅ 前端Vite配置存在

:: 检查Docker配置
echo 🔍 检查Docker配置...
if not exist "docker-compose.yml" (
    echo ❌ 缺少docker-compose.yml文件
    goto :error
)
echo ✅ docker-compose.yml存在

:: 检查数据库初始化脚本
if not exist "docker\mysql\init\init.sql" (
    echo ❌ 缺少数据库初始化脚本
    goto :error
)
echo ✅ 数据库初始化脚本存在

echo.
echo ========================================
echo    编译检查
echo ========================================

:: 编译后端项目
echo 🔨 编译后端项目...
mvn clean compile -DskipTests -q
if errorlevel 1 (
    echo ❌ 后端项目编译失败
    echo 请检查代码和依赖配置
    goto :error
)
echo ✅ 后端项目编译成功

:: 检查前端依赖
echo 📦 检查前端依赖...
cd wit-mall-web
if not exist "node_modules" (
    echo 📥 安装前端依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        cd ..
        goto :error
    )
)
echo ✅ 前端依赖检查完成
cd ..

echo.
echo ========================================
echo    检查完成
echo ========================================
echo.
echo 🎉 项目完整性检查通过！
echo.
echo 📋 环境信息：
echo   Java版本:    %JAVA_VERSION%
echo   Maven版本:   %MAVEN_VERSION%
echo   Docker版本:  %DOCKER_VERSION%
echo   Node.js版本: %NODE_VERSION%
echo.
echo 🚀 可以开始启动项目了！
echo.
echo 💡 启动建议：
echo   1. 先运行: docker-compose up -d  (启动基础设施)
echo   2. 再运行: start-fullstack.bat   (启动全栈项目)
echo   3. 或分别运行后端和前端启动脚本
echo.
goto :end

:error
echo.
echo ❌ 项目检查失败，请根据上述提示解决问题后重试
echo.
pause
exit /b 1

:end
pause

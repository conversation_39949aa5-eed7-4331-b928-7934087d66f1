# MySQL配置文件 - 在Nacos中创建
# Data ID: mysql-config.yml
# Group: DEFAULT_GROUP

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 5
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1
      validation-query-timeout: 3
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 是否缓存preparedStatement，也就是PSCache  官方建议MySQL下建议关闭   个人建议如果想用SQL防火墙 建议打开
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1,*************
        # IP黑名单 (存在共同时，deny优先于allow)
        deny: ************
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: admin
        url-pattern: /druid/*
      # 配置StatFilter
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 2000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  type-aliases-package: com.wit.**.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mapper/**/*.xml
  # 加载全局的配置文件
  configuration:
    # 自动驼峰命名规则（camel case）映射
    map-underscore-to-camel-case: true
    # 当设置为true的时候，懒加载的对象可能被任何懒属性全部加载，否则，每个属性都按需加载。
    aggressive-lazy-loading: true
    # 是否开启缓存
    cache-enabled: false
    # 打印sql
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # 是否控制台 print mybatis-plus 的 LOGO
    banner: false
    db-config:
      # 主键类型
      id-type: ASSIGN_ID
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除全局值（1表示已删除）
      logic-delete-value: 1
      # 逻辑删除全局值（0表示未删除）
      logic-not-delete-value: 0

#!/bin/bash

# Nacos 配置部署脚本
# 用于将本地配置文件批量上传到 Nacos 配置中心

# 配置参数
NACOS_SERVER="http://localhost:8848"
NACOS_USERNAME="nacos"
NACOS_PASSWORD="nacos"
NAMESPACE_ID="1f290fcd-60d0-48d1-893c-0d2ffb30625d"  # wit 命名空间ID
CONFIG_DIR="./prod"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，建议安装 jq 以获得更好的体验"
    fi
    
    log_success "依赖检查完成"
}

# 获取访问令牌
get_access_token() {
    log_info "获取访问令牌..."
    
    local response=$(curl -s -X POST \
        "${NACOS_SERVER}/nacos/v1/auth/login" \
        -d "username=${NACOS_USERNAME}&password=${NACOS_PASSWORD}")
    
    if [[ $? -ne 0 ]]; then
        log_error "无法连接到 Nacos 服务器: ${NACOS_SERVER}"
        exit 1
    fi
    
    # 提取访问令牌
    if command -v jq &> /dev/null; then
        ACCESS_TOKEN=$(echo "$response" | jq -r '.accessToken // empty')
    else
        ACCESS_TOKEN=$(echo "$response" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
    fi
    
    if [[ -z "$ACCESS_TOKEN" ]]; then
        log_error "获取访问令牌失败，请检查用户名和密码"
        log_error "响应: $response"
        exit 1
    fi
    
    log_success "访问令牌获取成功"
}

# 上传配置文件
upload_config() {
    local file_path="$1"
    local group="$2"
    local data_id="$3"

    log_info "上传配置: ${group}/${data_id}"

    # 检查文件是否存在
    if [[ ! -f "$file_path" ]]; then
        log_error "配置文件不存在: $file_path"
        return 1
    fi

    # 检查文件是否为空
    if [[ ! -s "$file_path" ]]; then
        log_error "配置文件为空: $file_path"
        return 1
    fi

    # 检查文件大小（避免过大的文件）
    local file_size=$(wc -c < "$file_path")
    if [[ $file_size -gt 1048576 ]]; then  # 1MB
        log_error "配置文件过大 (${file_size} bytes): $file_path"
        return 1
    fi

    # 创建临时文件来存储内容
    local temp_file=$(mktemp)
    cat "$file_path" > "$temp_file"

    # 使用文件上传方式而不是参数传递
    local response=$(curl -s -X POST \
        "${NACOS_SERVER}/nacos/v1/cs/configs" \
        -H "Authorization: Bearer ${ACCESS_TOKEN}" \
        -F "dataId=${data_id}" \
        -F "group=${group}" \
        -F "tenant=${NAMESPACE_ID}" \
        -F "content=@${temp_file}" \
        -F "type=yaml")

    # 清理临时文件
    rm -f "$temp_file"

    if [[ "$response" == "true" ]]; then
        log_success "配置上传成功: ${group}/${data_id}"
        return 0
    else
        log_error "配置上传失败: ${group}/${data_id}"
        log_error "响应: $response"

        # 尝试备用方法：使用 --data-binary
        log_info "尝试备用上传方法..."
        local response2=$(curl -s -X POST \
            "${NACOS_SERVER}/nacos/v1/cs/configs" \
            -H "Authorization: Bearer ${ACCESS_TOKEN}" \
            -H "Content-Type: application/x-www-form-urlencoded" \
            --data-urlencode "dataId=${data_id}" \
            --data-urlencode "group=${group}" \
            --data-urlencode "tenant=${NAMESPACE_ID}" \
            --data-urlencode "content@${file_path}" \
            --data-urlencode "type=yaml")

        if [[ "$response2" == "true" ]]; then
            log_success "配置上传成功 (备用方法): ${group}/${data_id}"
            return 0
        else
            log_error "备用方法也失败: $response2"
            return 1
        fi
    fi
}

# 批量上传配置
batch_upload() {
    log_info "开始批量上传配置..."
    
    local success_count=0
    local fail_count=0
    
    # DEFAULT_GROUP 配置
    log_info "上传 DEFAULT_GROUP 配置..."
    for config_file in "${CONFIG_DIR}/DEFAULT_GROUP"/*.yml; do
        if [[ -f "$config_file" ]]; then
            local filename=$(basename "$config_file")
            if upload_config "$config_file" "DEFAULT_GROUP" "$filename"; then
                success_count=$((success_count + 1))
            else
                fail_count=$((fail_count + 1))
            fi
        fi
    done
    
    # BUSINESS_GROUP 配置
    log_info "上传 BUSINESS_GROUP 配置..."
    for config_file in "${CONFIG_DIR}/BUSINESS_GROUP"/*.yml; do
        if [[ -f "$config_file" ]]; then
            local filename=$(basename "$config_file")
            if upload_config "$config_file" "BUSINESS_GROUP" "$filename"; then
                ((success_count++))
            else
                ((fail_count++))
            fi
        fi
    done
    
    # COMMON_GROUP 配置
    log_info "上传 COMMON_GROUP 配置..."
    for config_file in "${CONFIG_DIR}/COMMON_GROUP"/*.yml; do
        if [[ -f "$config_file" ]]; then
            local filename=$(basename "$config_file")
            if upload_config "$config_file" "COMMON_GROUP" "$filename"; then
                ((success_count++))
            else
                ((fail_count++))
            fi
        fi
    done
    
    # 输出统计结果
    echo
    log_info "上传完成统计:"
    log_success "成功: $success_count 个配置文件"
    if [[ $fail_count -gt 0 ]]; then
        log_error "失败: $fail_count 个配置文件"
    fi
}

# 验证配置
verify_configs() {
    log_info "验证配置..."
    
    # 验证关键配置是否存在
    local configs=(
        "DEFAULT_GROUP:wit-gateway-core.yml"
        "DEFAULT_GROUP:wit-gateway-routes.yml"
        "DEFAULT_GROUP:wit-gateway-security.yml"
        "BUSINESS_GROUP:wit-user-core.yml"
        "BUSINESS_GROUP:wit-user-database.yml"
        "BUSINESS_GROUP:wit-user-redis.yml"
        "BUSINESS_GROUP:wit-user-security.yml"
        "COMMON_GROUP:common-database.yml"
        "COMMON_GROUP:common-redis.yml"
        "COMMON_GROUP:common-logging.yml"
        "COMMON_GROUP:common-mq.yml"
    )
    
    local verify_success=0
    local verify_fail=0
    
    for config in "${configs[@]}"; do
        IFS=':' read -r group data_id <<< "$config"
        
        local response=$(curl -s -X GET \
            "${NACOS_SERVER}/nacos/v1/cs/configs" \
            -H "Authorization: Bearer ${ACCESS_TOKEN}" \
            -G \
            -d "dataId=${data_id}" \
            -d "group=${group}" \
            -d "tenant=${NAMESPACE_ID}")
        
        if [[ -n "$response" && "$response" != "config data not exist" ]]; then
            log_success "配置验证成功: ${group}/${data_id}"
            ((verify_success++))
        else
            log_error "配置验证失败: ${group}/${data_id}"
            ((verify_fail++))
        fi
    done
    
    echo
    log_info "验证完成统计:"
    log_success "验证成功: $verify_success 个配置"
    if [[ $verify_fail -gt 0 ]]; then
        log_error "验证失败: $verify_fail 个配置"
    fi
}

# 显示帮助信息
show_help() {
    echo "Nacos 配置部署脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -s, --server URL     Nacos 服务器地址 (默认: http://localhost:8848)"
    echo "  -u, --username USER  Nacos 用户名 (默认: nacos)"
    echo "  -p, --password PASS  Nacos 密码 (默认: nacos)"
    echo "  -n, --namespace ID   命名空间ID (默认: 1f290fcd-60d0-48d1-893c-0d2ffb30625d)"
    echo "  -d, --dir DIR        配置文件目录 (默认: ./prod)"
    echo "  -v, --verify         仅验证配置，不上传"
    echo "  -h, --help           显示帮助信息"
    echo
    echo "示例:"
    echo "  $0                                    # 使用默认参数上传配置"
    echo "  $0 -s http://nacos:8848              # 指定服务器地址"
    echo "  $0 -u admin -p admin123              # 指定用户名密码"
    echo "  $0 -v                                # 仅验证配置"
}

# 主函数
main() {
    local verify_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--server)
                NACOS_SERVER="$2"
                shift 2
                ;;
            -u|--username)
                NACOS_USERNAME="$2"
                shift 2
                ;;
            -p|--password)
                NACOS_PASSWORD="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE_ID="$2"
                shift 2
                ;;
            -d|--dir)
                CONFIG_DIR="$2"
                shift 2
                ;;
            -v|--verify)
                verify_only=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示配置信息
    echo "Nacos 配置部署脚本"
    echo "=================="
    echo "服务器地址: $NACOS_SERVER"
    echo "用户名: $NACOS_USERNAME"
    echo "命名空间ID: $NAMESPACE_ID"
    echo "配置目录: $CONFIG_DIR"
    echo
    
    # 检查依赖
    check_dependencies
    
    # 获取访问令牌
    get_access_token
    
    if [[ "$verify_only" == true ]]; then
        # 仅验证配置
        verify_configs
    else
        # 上传配置
        batch_upload
        
        # 验证配置
        echo
        verify_configs
    fi
    
    log_success "脚本执行完成！"
}

# 执行主函数
main "$@"

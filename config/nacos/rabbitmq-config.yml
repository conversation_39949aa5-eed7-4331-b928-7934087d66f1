# RabbitMQ配置文件 - 在Nacos中创建
# Data ID: rabbitmq-config.yml
# Group: DEFAULT_GROUP

spring:
  rabbitmq:
    # RabbitMQ服务器地址
    host: localhost
    # RabbitMQ服务器端口
    port: 5672
    # 用户名
    username: admin
    # 密码
    password: admin123
    # 虚拟主机
    virtual-host: /
    # 连接超时时间
    connection-timeout: 15000
    # 发布确认
    publisher-confirm-type: correlated
    # 发布返回
    publisher-returns: true
    # 模板配置
    template:
      # 强制回调
      mandatory: true
      # 发送重试
      retry:
        enabled: true
        initial-interval: 1000ms
        max-attempts: 3
        max-interval: 10000ms
        multiplier: 1.0
    # 监听器配置
    listener:
      simple:
        # 手动确认
        acknowledge-mode: manual
        # 并发消费者数量
        concurrency: 5
        # 最大并发消费者数量
        max-concurrency: 10
        # 预取数量
        prefetch: 1
        # 重试配置
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000ms
          max-interval: 10000ms
          multiplier: 2.0
        # 默认重新排队
        default-requeue-rejected: true
      direct:
        acknowledge-mode: manual
        prefetch: 1
        retry:
          enabled: true
          max-attempts: 3

# 自定义RabbitMQ配置
rabbitmq:
  # 交换机配置
  exchanges:
    # 订单交换机
    order:
      name: wit.order.exchange
      type: topic
      durable: true
    # 支付交换机
    payment:
      name: wit.payment.exchange
      type: topic
      durable: true
    # 库存交换机
    inventory:
      name: wit.inventory.exchange
      type: topic
      durable: true
    # 通知交换机
    notification:
      name: wit.notification.exchange
      type: topic
      durable: true
  
  # 队列配置
  queues:
    # 订单队列
    order:
      create: wit.order.create.queue
      cancel: wit.order.cancel.queue
      timeout: wit.order.timeout.queue
    # 支付队列
    payment:
      success: wit.payment.success.queue
      fail: wit.payment.fail.queue
    # 库存队列
    inventory:
      lock: wit.inventory.lock.queue
      unlock: wit.inventory.unlock.queue
    # 通知队列
    notification:
      email: wit.notification.email.queue
      sms: wit.notification.sms.queue
      push: wit.notification.push.queue
  
  # 路由键配置
  routing-keys:
    order:
      create: order.create
      cancel: order.cancel
      timeout: order.timeout
    payment:
      success: payment.success
      fail: payment.fail
    inventory:
      lock: inventory.lock
      unlock: inventory.unlock
    notification:
      email: notification.email
      sms: notification.sms
      push: notification.push

# Wit Mall 用户服务 - 核心配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: BUSINESS_GROUP

server:
  port: 8082
  servlet:
    context-path: /

spring:
  application:
    name: wit-user

  cloud:
    nacos:
      discovery:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d  # wit 命名空间ID
        group: BUSINESS_GROUP
        cluster-name: wit-cluster
        metadata:
          version: 1.0.0
          zone: prod
          service-type: business
      config:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: BUSINESS_GROUP
        file-extension: yml
        refresh-enabled: true
        # 引用其他配置文件
        extension-configs:
          - data-id: wit-user-database.yml
            group: BUSINESS_GROUP
            refresh: true
          - data-id: wit-user-redis.yml
            group: BUSINESS_GROUP
            refresh: true
          - data-id: wit-user-security.yml
            group: BUSINESS_GROUP
            refresh: true
        # 引用公共配置
        shared-configs:
          - data-id: common-logging.yml
            group: COMMON_GROUP
            refresh: true
          - data-id: common-mq.yml
            group: COMMON_GROUP
            refresh: true

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 用户服务核心配置
wit:
  user:
    # 基础配置
    basic:
      service-name: wit-user
      version: 1.0.0
      description: Wit Mall User Service
      
    # 业务配置
    business:
      # 用户注册配置
      register:
        # 是否需要邮箱验证
        email-verification: true
        # 是否需要手机验证
        phone-verification: true
        # 默认用户状态
        default-status: ACTIVE
        # 默认用户角色
        default-role: USER
        # 注册赠送积分
        register-points: 100
        
      # 用户登录配置
      login:
        # 最大失败次数
        max-fail-count: 5
        # 锁定时间（分钟）
        lock-time: 30
        # 是否允许多设备登录
        multi-device: true
        # 单设备登录时是否踢出其他设备
        kick-out-other: false
        # 记住我功能过期时间（天）
        remember-me-days: 7
        
      # 用户头像配置
      avatar:
        # 默认头像URL
        default-url: https://wit-mall.oss-cn-hangzhou.aliyuncs.com/avatar/default.png
        # 允许的文件类型
        allowed-types: jpg,jpeg,png,gif
        # 最大文件大小（MB）
        max-size: 5
        # 图片压缩质量
        compress-quality: 0.8
        
      # 用户等级配置
      level:
        # 是否启用用户等级
        enabled: true
        # 等级计算方式（POINTS: 积分, CONSUMPTION: 消费金额）
        calculate-type: POINTS
        # 等级配置
        levels:
          - name: 青铜会员
            min-points: 0
            max-points: 999
            discount: 1.0
          - name: 白银会员
            min-points: 1000
            max-points: 4999
            discount: 0.98
          - name: 黄金会员
            min-points: 5000
            max-points: 19999
            discount: 0.95
          - name: 钻石会员
            min-points: 20000
            max-points: 99999
            discount: 0.92
          - name: 至尊会员
            min-points: 100000
            max-points: 999999
            discount: 0.90
            
    # 验证码配置
    captcha:
      enabled: true
      expire-time: 300  # 5分钟
      length: 4
      type: ARITHMETIC  # 算术验证码
      # 图形验证码配置
      image:
        width: 120
        height: 40
        font-size: 25
        
    # 短信配置
    sms:
      enabled: true
      provider: aliyun  # 阿里云短信
      template-id: ${SMS_TEMPLATE_ID:}
      sign-name: ${SMS_SIGN_NAME:}
      expire-time: 300  # 5分钟
      daily-limit: 10   # 每日发送限制
      # 短信模板配置
      templates:
        register: "您的注册验证码是：{code}，5分钟内有效。"
        login: "您的登录验证码是：{code}，5分钟内有效。"
        reset-password: "您的密码重置验证码是：{code}，5分钟内有效。"
        
    # 邮件配置
    email:
      enabled: true
      smtp:
        host: ${EMAIL_HOST:}
        port: ${EMAIL_PORT:587}
        username: ${EMAIL_USERNAME:}
        password: ${EMAIL_PASSWORD:}
        auth: true
        starttls: true
      from: ${EMAIL_FROM:<EMAIL>}
      templates:
        register: register-template
        reset-password: reset-password-template
        welcome: welcome-template
        
    # 第三方登录配置
    oauth:
      enabled: true
      providers:
        # 微信登录
        wechat:
          enabled: true
          app-id: ${WECHAT_APP_ID:}
          app-secret: ${WECHAT_APP_SECRET:}
        # QQ登录
        qq:
          enabled: true
          app-id: ${QQ_APP_ID:}
          app-key: ${QQ_APP_KEY:}
        # 微博登录
        weibo:
          enabled: true
          app-key: ${WEIBO_APP_KEY:}
          app-secret: ${WEIBO_APP_SECRET:}
          
    # 用户数据同步配置
    sync:
      enabled: true
      # 同步到其他系统
      targets:
        - system: crm
          enabled: true
          url: ${CRM_SYNC_URL:}
        - system: analytics
          enabled: true
          url: ${ANALYTICS_SYNC_URL:}
          
    # 用户导入导出配置
    import-export:
      # 批量导入配置
      import:
        enabled: true
        max-batch-size: 1000
        allowed-formats: xlsx,csv
        template-url: /templates/user-import-template.xlsx
      # 批量导出配置
      export:
        enabled: true
        max-export-size: 10000
        formats: xlsx,csv,pdf
        
    # 用户统计配置
    statistics:
      enabled: true
      # 统计维度
      dimensions:
        - daily    # 日统计
        - weekly   # 周统计
        - monthly  # 月统计
        - yearly   # 年统计
      # 统计指标
      metrics:
        - register-count     # 注册数量
        - login-count        # 登录数量
        - active-count       # 活跃用户数
        - retention-rate     # 留存率

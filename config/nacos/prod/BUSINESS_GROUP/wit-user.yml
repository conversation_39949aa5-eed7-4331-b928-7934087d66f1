# Wit Mall 用户服务 - 生产环境配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: BUSINESS_GROUP

server:
  port: 8082
  servlet:
    context-path: /

spring:
  application:
    name: wit-user
  
  # 数据源配置 - 生产环境
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:w2306673016}
    
    # Druid连接池配置
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 200
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: ${DRUID_PASSWORD:admin123}
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

  # Redis配置
  redis:
    host: wit-redis
    port: 6379
    timeout: 3000ms
    password: # 生产环境建议设置密码
    database: 1  # 用户服务使用数据库1
    lettuce:
      pool:
        max-active: 100
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms

  cloud:
    nacos:
      discovery:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d  # wit 命名空间ID
        group: BUSINESS_GROUP
        cluster-name: wit-cluster
        metadata:
          version: 1.0.0
          zone: prod
          service-type: business
      config:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: BUSINESS_GROUP
        file-extension: yml
        refresh-enabled: true
        # 共享配置
        shared-configs:
          - data-id: common-database.yml
            group: COMMON_GROUP
            refresh: true
          - data-id: common-redis.yml
            group: COMMON_GROUP
            refresh: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      update-strategy: NOT_NULL
      insert-strategy: NOT_NULL
      select-strategy: NOT_EMPTY
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.wit.user.entity

# 日志配置
logging:
  level:
    com.wit.user: INFO
    com.wit.user.mapper: DEBUG
    org.springframework.security: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
  file:
    name: logs/wit-user.log
    max-size: 100MB
    max-history: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 用户服务自定义配置
wit:
  user:
    # 密码配置
    password:
      # 密码加密强度
      strength: 12
      # 密码最小长度
      min-length: 8
      # 密码最大长度
      max-length: 20
      # 是否需要包含特殊字符
      require-special-char: true
      # 密码过期天数
      expire-days: 90

    # JWT配置
    jwt:
      secret: ${JWT_SECRET:wit-user-jwt-secret-key-2024-very-long-secret-for-production}
      expiration: 7200  # 2小时
      refresh-expiration: 604800  # 7天
      issuer: wit-mall-user-service

    # 验证码配置
    captcha:
      enabled: true
      expire-time: 300  # 5分钟
      length: 4
      type: ARITHMETIC  # 算术验证码

    # 短信配置
    sms:
      enabled: true
      provider: aliyun  # 阿里云短信
      template-id: ${SMS_TEMPLATE_ID:}
      sign-name: ${SMS_SIGN_NAME:}
      expire-time: 300  # 5分钟
      daily-limit: 10   # 每日发送限制

    # 邮件配置
    email:
      enabled: true
      smtp:
        host: ${EMAIL_HOST:}
        port: ${EMAIL_PORT:587}
        username: ${EMAIL_USERNAME:}
        password: ${EMAIL_PASSWORD:}
        auth: true
        starttls: true
      from: ${EMAIL_FROM:<EMAIL>}
      templates:
        register: register-template
        reset-password: reset-password-template

    # 用户注册配置
    register:
      # 是否需要邮箱验证
      email-verification: true
      # 是否需要手机验证
      phone-verification: true
      # 默认用户状态
      default-status: ACTIVE
      # 默认用户角色
      default-role: USER

    # 登录配置
    login:
      # 最大失败次数
      max-fail-count: 5
      # 锁定时间（分钟）
      lock-time: 30
      # 是否允许多设备登录
      multi-device: true
      # 单设备登录时是否踢出其他设备
      kick-out-other: false

    # 用户头像配置
    avatar:
      # 默认头像URL
      default-url: https://wit-mall.oss-cn-hangzhou.aliyuncs.com/avatar/default.png
      # 允许的文件类型
      allowed-types: jpg,jpeg,png,gif
      # 最大文件大小（MB）
      max-size: 5
      # 图片压缩质量
      compress-quality: 0.8

    # 缓存配置
    cache:
      # 用户信息缓存时间（秒）
      user-info-ttl: 3600
      # 用户权限缓存时间（秒）
      user-permission-ttl: 1800
      # 验证码缓存前缀
      captcha-prefix: "wit:user:captcha:"
      # 短信验证码缓存前缀
      sms-prefix: "wit:user:sms:"
      # 登录失败计数前缀
      login-fail-prefix: "wit:user:login:fail:"

    # 安全配置
    security:
      # 敏感信息脱敏
      desensitization:
        enabled: true
        phone-pattern: "###****####"
        email-pattern: "***@***.***"
      
      # 操作日志
      audit-log:
        enabled: true
        include-operations: 
          - LOGIN
          - LOGOUT
          - REGISTER
          - UPDATE_PASSWORD
          - UPDATE_PROFILE
        
      # IP限制
      ip-limit:
        enabled: true
        max-requests-per-minute: 100

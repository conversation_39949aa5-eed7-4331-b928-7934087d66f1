# Wit Mall 用户服务 - 数据库配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: BUSINESS_GROUP

spring:
  # 数据源配置 - 用户服务专用
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:w2306673016}
    
    # Druid连接池配置 - 用户服务优化
    druid:
      # 初始连接数
      initial-size: 5
      # 最小空闲连接数
      min-idle: 5
      # 最大活跃连接数（用户服务并发较高）
      max-active: 100
      # 获取连接等待超时时间
      max-wait: 60000
      # 间隔多久进行一次检测
      time-between-eviction-runs-millis: 60000
      # 连接最小生存时间
      min-evictable-idle-time-millis: 300000
      # 连接最大生存时间
      max-evictable-idle-time-millis: 900000
      # 验证查询
      validation-query: SELECT 1
      # 空闲时验证
      test-while-idle: true
      # 获取时验证
      test-on-borrow: false
      # 归还时验证
      test-on-return: false
      # 预编译语句缓存
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      
      # 连接属性
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=3000
      
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:admin123}
        reset-enable: false
      
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*,/actuator/*"
      
      # SQL监控
      filter:
        stat:
          enabled: true
          slow-sql-millis: 3000
          log-slow-sql: true
          merge-sql: true
        wall:
          enabled: true
          config:
            multi-statement-allow: false
            delete-allow: true
            update-allow: true

# MyBatis Plus配置 - 用户服务专用
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    default-statement-timeout: 30
    use-generated-keys: true
    default-executor-type: REUSE
    local-cache-scope: SESSION
    aggressive-lazy-loading: false
    multiple-result-sets-enabled: true
    use-column-label: true
    auto-mapping-behavior: PARTIAL
    auto-mapping-unknown-column-behavior: WARNING
    call-setters-on-nulls: false
    return-instance-for-empty-row: false

  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      update-strategy: NOT_NULL
      insert-strategy: NOT_NULL
      select-strategy: NOT_EMPTY
      # 用户表前缀
      table-prefix: user_
      
    banner: false
    sql-parser-cache: true

  # Mapper文件位置
  mapper-locations: classpath*:mapper/user/**/*Mapper.xml
  # 实体类包路径
  type-aliases-package: com.wit.user.entity

# 用户服务数据库配置
wit:
  user:
    database:
      # 数据库监控
      monitor:
        enabled: true
        slow-query-threshold: 3000
        log-slow-query: true
        
      # 读写分离配置
      read-write-split:
        enabled: false  # 暂时关闭，后续可开启
        master-datasource: master
        slave-datasources:
          - slave1
          - slave2
        load-balance-algorithm: round_robin
        
      # 分库分表配置
      sharding:
        enabled: false  # 暂时关闭，用户量大时可开启
        # 用户表分表策略
        tables:
          user:
            actual-data-nodes: ds0.user_$->{0..9}
            table-strategy:
              inline:
                sharding-column: user_id
                algorithm-expression: user_$->{user_id % 10}
          user_profile:
            actual-data-nodes: ds0.user_profile_$->{0..9}
            table-strategy:
              inline:
                sharding-column: user_id
                algorithm-expression: user_profile_$->{user_id % 10}
                
      # 数据备份配置
      backup:
        enabled: true
        # 备份策略
        strategy: incremental  # full, incremental
        # 备份时间
        schedule: "0 0 2 * * ?"  # 每天凌晨2点
        # 备份保留天数
        retention-days: 30
        # 备份路径
        backup-path: /data/backup/user
        
      # 数据归档配置
      archive:
        enabled: true
        # 归档策略
        rules:
          # 用户登录日志归档（保留3个月）
          - table: user_login_log
            condition: "created_time < DATE_SUB(NOW(), INTERVAL 3 MONTH)"
            archive-table: user_login_log_archive
          # 用户操作日志归档（保留6个月）
          - table: user_operation_log
            condition: "created_time < DATE_SUB(NOW(), INTERVAL 6 MONTH)"
            archive-table: user_operation_log_archive
            
      # 数据清理配置
      cleanup:
        enabled: true
        # 清理规则
        rules:
          # 清理过期验证码
          - table: user_verification_code
            condition: "expire_time < NOW()"
            schedule: "0 0 1 * * ?"  # 每天凌晨1点
          # 清理过期会话
          - table: user_session
            condition: "expire_time < NOW()"
            schedule: "0 */30 * * * ?"  # 每30分钟
            
      # 数据同步配置
      sync:
        enabled: true
        # 同步到数据仓库
        targets:
          - name: data-warehouse
            enabled: true
            type: mysql
            url: **********************************
            username: ${DW_USERNAME:dw_user}
            password: ${DW_PASSWORD:dw_password}
            # 同步表配置
            tables:
              - source: user
                target: dim_user
                sync-type: full  # full, incremental
                schedule: "0 0 3 * * ?"
              - source: user_profile
                target: dim_user_profile
                sync-type: incremental
                schedule: "0 */10 * * * ?"
                
      # 数据质量检查
      quality-check:
        enabled: true
        # 检查规则
        rules:
          # 用户数据完整性检查
          - name: user-data-integrity
            table: user
            checks:
              - column: username
                rule: not_null
              - column: email
                rule: email_format
              - column: phone
                rule: phone_format
            schedule: "0 0 4 * * ?"
          # 用户数据一致性检查
          - name: user-data-consistency
            tables:
              - user
              - user_profile
            foreign-key: user_id
            schedule: "0 0 5 * * ?"
            
      # 性能优化配置
      performance:
        # 索引优化
        index-optimization:
          enabled: true
          # 自动创建索引
          auto-create-index: false
          # 索引使用监控
          monitor-index-usage: true
          
        # 查询优化
        query-optimization:
          enabled: true
          # 慢查询优化建议
          slow-query-advice: true
          # 查询缓存
          query-cache: true
          
        # 连接池优化
        connection-pool:
          # 动态调整连接池大小
          dynamic-sizing: true
          # 连接池监控
          monitoring: true

# Wit Mall 用户服务 - 安全配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: BUSINESS_GROUP

# 用户服务安全配置
wit:
  user:
    security:
      # 使用统一JWT配置（从wit-jwt-config.yml继承）
      # 如需覆盖，可在此处配置
      jwt:
        # 继承统一配置，无需重复定义
        enabled: true
        
      # 密码配置
      password:
        # 密码加密器
        encoder: bcrypt
        # 密码加密强度
        strength: 12
        # 密码最小长度
        min-length: 8
        # 密码最大长度
        max-length: 20
        # 密码复杂度要求
        complexity:
          # 是否需要包含数字
          require-digit: true
          # 是否需要包含小写字母
          require-lowercase: true
          # 是否需要包含大写字母
          require-uppercase: true
          # 是否需要包含特殊字符
          require-special-char: true
          # 特殊字符集合
          special-chars: "!@#$%^&*()_+-=[]{}|;:,.<>?"
        # 密码历史记录（防止重复使用）
        history:
          enabled: true
          count: 5  # 记录最近5次密码
        # 密码过期配置
        expiration:
          enabled: true
          days: 90  # 90天过期
          warning-days: 7  # 过期前7天提醒
          
      # 账户锁定配置
      account-lock:
        enabled: true
        # 最大失败次数
        max-attempts: 5
        # 锁定时间（分钟）
        lock-duration: 30
        # 锁定类型（TIME: 时间锁定, MANUAL: 手动解锁）
        lock-type: TIME
        # 是否记录锁定日志
        log-enabled: true
        
      # 会话管理配置
      session:
        # 会话超时时间（秒）
        timeout: 7200  # 2小时
        # 最大并发会话数
        max-sessions: 5
        # 会话冲突处理策略（KICK_OUT: 踢出旧会话, REJECT: 拒绝新会话）
        conflict-strategy: KICK_OUT
        # 是否启用会话固定保护
        session-fixation-protection: true
        
      # 权限配置
      authorization:
        # 是否启用权限检查
        enabled: true
        # 权限缓存时间（秒）
        cache-ttl: 1800  # 30分钟
        # 权限检查策略（STRICT: 严格模式, LOOSE: 宽松模式）
        strategy: STRICT
        # 默认权限
        default-permissions:
          - "user:profile:read"
          - "user:profile:update"
          
      # 数据脱敏配置
      desensitization:
        enabled: true
        # 脱敏规则
        rules:
          # 手机号脱敏
          phone:
            pattern: "(\\d{3})\\d{4}(\\d{4})"
            replacement: "$1****$2"
          # 身份证脱敏
          id-card:
            pattern: "(\\d{6})\\d{8}(\\d{4})"
            replacement: "$1********$2"
          # 邮箱脱敏
          email:
            pattern: "(\\w{1,3})\\w*@(\\w+)"
            replacement: "$1***@$2"
          # 银行卡脱敏
          bank-card:
            pattern: "(\\d{4})\\d{8,12}(\\d{4})"
            replacement: "$1********$2"
          # 真实姓名脱敏
          real-name:
            pattern: "(.)(.+)(.)"
            replacement: "$1*$3"
            
      # 审计日志配置
      audit:
        enabled: true
        # 审计事件类型
        events:
          - LOGIN_SUCCESS      # 登录成功
          - LOGIN_FAILURE      # 登录失败
          - LOGOUT            # 登出
          - REGISTER          # 注册
          - PASSWORD_CHANGE   # 密码修改
          - PROFILE_UPDATE    # 资料更新
          - PERMISSION_CHANGE # 权限变更
          - ACCOUNT_LOCK      # 账户锁定
          - ACCOUNT_UNLOCK    # 账户解锁
          - DATA_EXPORT       # 数据导出
          - SENSITIVE_OPERATION # 敏感操作
        # 审计日志保留天数
        retention-days: 90
        # 是否异步记录
        async: true
        
      # 防护配置
      protection:
        # 防暴力破解
        brute-force:
          enabled: true
          # 检测窗口（分钟）
          window: 15
          # 最大尝试次数
          max-attempts: 10
          # 封禁时间（分钟）
          ban-duration: 60
          
        # 防重放攻击
        replay-attack:
          enabled: true
          # 时间窗口（秒）
          time-window: 300
          # 随机数缓存时间（秒）
          nonce-cache-ttl: 600
          
        # 防CSRF攻击
        csrf:
          enabled: true
          # CSRF token有效期（秒）
          token-validity: 3600
          # 是否在cookie中存储token
          cookie-http-only: true
          
        # 防XSS攻击
        xss:
          enabled: true
          # XSS过滤模式（BLOCK: 阻止, SANITIZE: 清理）
          mode: SANITIZE
          
      # IP访问控制
      ip-access:
        # 是否启用IP白名单
        whitelist-enabled: false
        # IP白名单
        whitelist: []
        # 是否启用IP黑名单
        blacklist-enabled: true
        # IP黑名单
        blacklist: []
        # 地理位置限制
        geo-restriction:
          enabled: false
          # 允许的国家代码
          allowed-countries: ["CN"]
          # 禁止的国家代码
          blocked-countries: []
          
      # 设备管理
      device:
        # 是否启用设备指纹
        fingerprint-enabled: true
        # 设备信息缓存时间（天）
        cache-days: 30
        # 新设备登录是否需要验证
        new-device-verification: true
        # 可信设备有效期（天）
        trusted-device-days: 30
        
      # 风险控制
      risk-control:
        enabled: true
        # 风险评分阈值
        risk-threshold: 80
        # 风险因子权重
        factors:
          login-frequency: 0.2      # 登录频率
          login-location: 0.3       # 登录地点
          login-device: 0.2         # 登录设备
          operation-pattern: 0.3    # 操作模式
        # 高风险操作
        high-risk-operations:
          - PASSWORD_CHANGE
          - EMAIL_CHANGE
          - PHONE_CHANGE
          - BANK_CARD_BIND
          - LARGE_TRANSFER
          
      # 数据加密配置
      encryption:
        # 敏感数据加密
        sensitive-data:
          enabled: true
          algorithm: AES-256-GCM
          key: ${DATA_ENCRYPTION_KEY:}
        # 传输加密
        transport:
          enabled: true
          protocol: TLS1.3
          
      # 合规配置
      compliance:
        # GDPR合规
        gdpr:
          enabled: true
          # 数据保留期（天）
          data-retention-days: 365
          # 是否支持数据导出
          data-export: true
          # 是否支持数据删除
          data-deletion: true
        # 等保合规
        classified-protection:
          enabled: true
          level: 3  # 等保三级
          
      # 安全监控
      monitoring:
        enabled: true
        # 监控指标
        metrics:
          - login-attempts        # 登录尝试次数
          - failed-logins        # 登录失败次数
          - account-locks        # 账户锁定次数
          - password-changes     # 密码修改次数
          - suspicious-activities # 可疑活动次数
        # 实时告警
        alerts:
          # 异常登录告警
          - name: abnormal-login
            condition: "failed_login_rate > 0.1"
            threshold: 0.1
            duration: 5m
          # 批量操作告警
          - name: batch-operation
            condition: "operation_count > 100"
            threshold: 100
            duration: 1m
          # 权限提升告警
          - name: privilege-escalation
            condition: "permission_change_count > 0"
            threshold: 0
            duration: 1m

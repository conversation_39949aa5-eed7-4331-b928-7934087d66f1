# Wit Mall 用户服务 - Redis配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: BUSINESS_GROUP

spring:
  # Redis配置 - 用户服务专用
  redis:
    host: wit-redis
    port: 6379
    timeout: 3000ms
    password: ${REDIS_PASSWORD:}
    database: 1  # 用户服务使用数据库1
    lettuce:
      pool:
        max-active: 50   # 用户服务连接池适中
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms
        time-between-eviction-runs: 30s

# 用户服务Redis配置
wit:
  user:
    redis:
      # 缓存配置
      cache:
        # 用户信息缓存
        user-info:
          key-prefix: "wit:user:info:"
          ttl: 3600  # 1小时
          max-size: 10000
          
        # 用户权限缓存
        user-permission:
          key-prefix: "wit:user:permission:"
          ttl: 1800  # 30分钟
          max-size: 5000
          
        # 用户会话缓存
        user-session:
          key-prefix: "wit:user:session:"
          ttl: 7200  # 2小时
          max-size: 20000
          
        # 用户等级缓存
        user-level:
          key-prefix: "wit:user:level:"
          ttl: 86400  # 24小时
          max-size: 1000
          
        # 用户积分缓存
        user-points:
          key-prefix: "wit:user:points:"
          ttl: 3600  # 1小时
          max-size: 10000
          
      # 验证码配置
      verification:
        # 图形验证码
        captcha:
          key-prefix: "wit:user:captcha:"
          ttl: 300  # 5分钟
          max-attempts: 5
          
        # 短信验证码
        sms:
          key-prefix: "wit:user:sms:"
          ttl: 300  # 5分钟
          max-attempts: 3
          daily-limit: 10
          
        # 邮箱验证码
        email:
          key-prefix: "wit:user:email:"
          ttl: 300  # 5分钟
          max-attempts: 3
          daily-limit: 5
          
      # 登录控制
      login:
        # 登录失败计数
        fail-count:
          key-prefix: "wit:user:login:fail:"
          ttl: 1800  # 30分钟
          max-attempts: 5
          
        # 账户锁定
        lock:
          key-prefix: "wit:user:login:lock:"
          ttl: 1800  # 30分钟锁定
          
        # 单点登录
        sso:
          key-prefix: "wit:user:sso:"
          ttl: 7200  # 2小时
          
        # 记住我功能
        remember-me:
          key-prefix: "wit:user:remember:"
          ttl: 604800  # 7天
          
      # 用户行为追踪
      behavior:
        # 用户访问记录
        access-log:
          key-prefix: "wit:user:access:"
          ttl: 86400  # 24小时
          max-records: 1000
          
        # 用户操作记录
        operation-log:
          key-prefix: "wit:user:operation:"
          ttl: 86400  # 24小时
          max-records: 500
          
        # 用户偏好设置
        preferences:
          key-prefix: "wit:user:preferences:"
          ttl: 86400  # 24小时
          
      # 分布式锁配置
      lock:
        # 用户注册锁
        register:
          key-prefix: "wit:user:lock:register:"
          ttl: 30000  # 30秒
          
        # 用户信息更新锁
        update:
          key-prefix: "wit:user:lock:update:"
          ttl: 10000  # 10秒
          
        # 密码重置锁
        reset-password:
          key-prefix: "wit:user:lock:reset:"
          ttl: 60000  # 60秒
          
      # 限流配置
      rate-limit:
        # 注册限流
        register:
          key-prefix: "wit:user:rate:register:"
          limit: 5  # 每小时5次
          window: 3600  # 1小时窗口
          
        # 登录限流
        login:
          key-prefix: "wit:user:rate:login:"
          limit: 100  # 每小时100次
          window: 3600
          
        # 发送验证码限流
        send-code:
          key-prefix: "wit:user:rate:code:"
          limit: 10  # 每小时10次
          window: 3600
          
        # 密码重置限流
        reset-password:
          key-prefix: "wit:user:rate:reset:"
          limit: 3  # 每小时3次
          window: 3600
          
      # 统计配置
      statistics:
        # 在线用户统计
        online-users:
          key: "wit:user:stats:online"
          ttl: 300  # 5分钟更新一次
          
        # 注册统计
        register-stats:
          key-prefix: "wit:user:stats:register:"
          ttl: 86400  # 24小时
          
        # 登录统计
        login-stats:
          key-prefix: "wit:user:stats:login:"
          ttl: 86400  # 24小时
          
        # 活跃用户统计
        active-users:
          key-prefix: "wit:user:stats:active:"
          ttl: 86400  # 24小时
          
      # 消息队列配置
      mq:
        # 用户注册消息
        user-register:
          queue: "wit:user:mq:register"
          ttl: 3600
          
        # 用户登录消息
        user-login:
          queue: "wit:user:mq:login"
          ttl: 3600
          
        # 用户信息更新消息
        user-update:
          queue: "wit:user:mq:update"
          ttl: 3600
          
        # 密码重置消息
        password-reset:
          queue: "wit:user:mq:password-reset"
          ttl: 3600
          
      # 数据同步配置
      sync:
        # 用户数据同步到其他服务
        user-data:
          key-prefix: "wit:user:sync:data:"
          ttl: 300  # 5分钟
          
        # 权限数据同步
        permission-data:
          key-prefix: "wit:user:sync:permission:"
          ttl: 600  # 10分钟
          
      # 缓存预热配置
      warm-up:
        enabled: true
        # 预热用户数据
        strategies:
          # 热点用户数据预热
          - name: hot-users
            key-pattern: "wit:user:info:*"
            cron: "0 0 6 * * ?"  # 每天早上6点预热
            batch-size: 1000
            
          # 用户权限数据预热
          - name: user-permissions
            key-pattern: "wit:user:permission:*"
            cron: "0 30 6 * * ?"  # 每天早上6点30分预热
            batch-size: 500
            
      # 监控配置
      monitor:
        enabled: true
        # 缓存命中率监控
        hit-rate:
          enabled: true
          threshold: 0.8  # 命中率低于80%告警
          
        # 内存使用监控
        memory-usage:
          enabled: true
          threshold: 0.8  # 内存使用超过80%告警
          
        # 连接数监控
        connection-count:
          enabled: true
          threshold: 40  # 连接数超过40告警
          
        # 慢查询监控
        slow-query:
          enabled: true
          threshold: 1000  # 超过1秒的查询
          
      # 数据清理配置
      cleanup:
        enabled: true
        # 清理策略
        strategies:
          # 清理过期验证码
          - name: expired-codes
            key-pattern: "wit:user:*:code:*"
            cron: "0 */10 * * * ?"  # 每10分钟清理一次
            
          # 清理过期会话
          - name: expired-sessions
            key-pattern: "wit:user:session:*"
            cron: "0 */30 * * * ?"  # 每30分钟清理一次
            
          # 清理过期锁
          - name: expired-locks
            key-pattern: "wit:user:lock:*"
            cron: "0 */5 * * * ?"  # 每5分钟清理一次

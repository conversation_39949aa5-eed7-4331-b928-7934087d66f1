# Wit Mall 网关服务 - 路由配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: DEFAULT_GROUP

spring:
  cloud:
    gateway:
      # 路由配置
      routes:
        # 认证服务路由
        - id: wit-auth-route
          uri: lb://wit-auth
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 100
                redis-rate-limiter.burstCapacity: 200
                key-resolver: "#{@ipKeyResolver}"

        # 用户服务路由
        - id: wit-user-route
          uri: lb://wit-user
          predicates:
            - Path=/api/user/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 200
                redis-rate-limiter.burstCapacity: 400
                key-resolver: "#{@ipKeyResolver}"

        # 商品服务路由
        - id: wit-product-route
          uri: lb://wit-product
          predicates:
            - Path=/api/product/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 300
                redis-rate-limiter.burstCapacity: 600
                key-resolver: "#{@ipKeyResolver}"

        # 订单服务路由
        - id: wit-order-route
          uri: lb://wit-order
          predicates:
            - Path=/api/order/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 150
                redis-rate-limiter.burstCapacity: 300
                key-resolver: "#{@ipKeyResolver}"

        # 购物车服务路由
        - id: wit-cart-route
          uri: lb://wit-cart
          predicates:
            - Path=/api/cart/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 200
                redis-rate-limiter.burstCapacity: 400
                key-resolver: "#{@ipKeyResolver}"

        # 支付服务路由
        - id: wit-payment-route
          uri: lb://wit-payment
          predicates:
            - Path=/api/payment/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 50
                redis-rate-limiter.burstCapacity: 100
                key-resolver: "#{@ipKeyResolver}"

        # 库存服务路由
        - id: wit-inventory-route
          uri: lb://wit-inventory
          predicates:
            - Path=/api/inventory/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 100
                redis-rate-limiter.burstCapacity: 200
                key-resolver: "#{@ipKeyResolver}"

        # 营销服务路由
        - id: wit-marketing-route
          uri: lb://wit-marketing
          predicates:
            - Path=/api/marketing/**
          filters:
            - StripPrefix=2

        # 搜索服务路由
        - id: wit-search-route
          uri: lb://wit-search
          predicates:
            - Path=/api/search/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 500
                redis-rate-limiter.burstCapacity: 1000
                key-resolver: "#{@ipKeyResolver}"

        # 推荐服务路由
        - id: wit-recommendation-route
          uri: lb://wit-recommendation
          predicates:
            - Path=/api/recommendation/**
          filters:
            - StripPrefix=2

        # 评价服务路由
        - id: wit-review-route
          uri: lb://wit-review
          predicates:
            - Path=/api/review/**
          filters:
            - StripPrefix=2

        # 通知服务路由
        - id: wit-notification-route
          uri: lb://wit-notification
          predicates:
            - Path=/api/notification/**
          filters:
            - StripPrefix=2

        # 文件服务路由
        - id: wit-file-route
          uri: lb://wit-file
          predicates:
            - Path=/api/file/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 100
                redis-rate-limiter.burstCapacity: 200
                key-resolver: "#{@ipKeyResolver}"

        # 系统管理服务路由
        - id: wit-system-route
          uri: lb://wit-system
          predicates:
            - Path=/api/system/**
          filters:
            - StripPrefix=2

        # 定时任务服务路由
        - id: wit-schedule-route
          uri: lb://wit-schedule
          predicates:
            - Path=/api/schedule/**
          filters:
            - StripPrefix=2

        # 数据分析服务路由
        - id: wit-analytics-route
          uri: lb://wit-analytics
          predicates:
            - Path=/api/analytics/**
          filters:
            - StripPrefix=2

        # 售后服务路由
        - id: wit-aftersales-route
          uri: lb://wit-aftersales
          predicates:
            - Path=/api/aftersales/**
          filters:
            - StripPrefix=2

        # 健康检查路由
        - id: health-check
          uri: http://localhost:8080
          predicates:
            - Path=/health
          filters:
            - SetStatus=200

        # Actuator监控路由
        - id: actuator-route
          uri: http://localhost:8080
          predicates:
            - Path=/actuator/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                key-resolver: "#{@ipKeyResolver}"

# 路由配置
wit:
  gateway:
    routes:
      # 路由刷新配置
      refresh:
        enabled: true
        interval: 30  # 秒
        
      # 路由缓存配置
      cache:
        enabled: true
        ttl: 300  # 秒
        
      # 路由监控配置
      monitor:
        enabled: true
        slow-route-threshold: 5000  # 毫秒
        
      # 动态路由配置
      dynamic:
        enabled: true
        source: nacos  # 路由配置来源

# Wit Mall 网关服务 - 生产环境配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: DEFAULT_GROUP

server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: wit-gateway
  
  # Redis配置 - 生产环境
  redis:
    host: wit-redis
    port: 6379
    timeout: 3000ms
    password: # 生产环境建议设置密码
    database: 0
    lettuce:
      pool:
        max-active: 200
        max-idle: 20
        min-idle: 5
        max-wait: 3000ms
        time-between-eviction-runs: 30s

  cloud:
    nacos:
      discovery:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d  # wit 命名空间ID
        group: DEFAULT_GROUP
        cluster-name: wit-cluster
        metadata:
          version: 1.0.0
          zone: prod
      config:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
    
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
          predicates:
            - name: Path
              args:
                pattern: "'/api/' + serviceId + '/**'"
          filters:
            - name: StripPrefix
              args:
                parts: 2
      
      # 全局过滤器配置
      default-filters:
        - name: Retry
          args:
            retries: 3
            methods: GET,POST,PUT,DELETE
            series: SERVER_ERROR
            exceptions: java.io.IOException,java.util.concurrent.TimeoutException
        - name: RequestTime
          args:
            enabled: true
        - name: AddRequestHeader
          args:
            name: X-Gateway-Version
            value: 1.0.0
      
      # 路由配置
      routes:
        # 认证服务路由
        - id: wit-auth-route
          uri: lb://wit-auth
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 100
                redis-rate-limiter.burstCapacity: 200
                key-resolver: "#{@ipKeyResolver}"

        # 用户服务路由
        - id: wit-user-route
          uri: lb://wit-user
          predicates:
            - Path=/api/user/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 200
                redis-rate-limiter.burstCapacity: 400
                key-resolver: "#{@ipKeyResolver}"

        # 商品服务路由
        - id: wit-product-route
          uri: lb://wit-product
          predicates:
            - Path=/api/product/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 300
                redis-rate-limiter.burstCapacity: 600
                key-resolver: "#{@ipKeyResolver}"

        # 订单服务路由
        - id: wit-order-route
          uri: lb://wit-order
          predicates:
            - Path=/api/order/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 150
                redis-rate-limiter.burstCapacity: 300
                key-resolver: "#{@ipKeyResolver}"

        # 购物车服务路由
        - id: wit-cart-route
          uri: lb://wit-cart
          predicates:
            - Path=/api/cart/**
          filters:
            - StripPrefix=2

        # 支付服务路由
        - id: wit-payment-route
          uri: lb://wit-payment
          predicates:
            - Path=/api/payment/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 50
                redis-rate-limiter.burstCapacity: 100
                key-resolver: "#{@ipKeyResolver}"

        # 文件服务路由
        - id: wit-file-route
          uri: lb://wit-file
          predicates:
            - Path=/api/file/**
          filters:
            - StripPrefix=2

        # 健康检查路由
        - id: health-check
          uri: http://localhost:8080
          predicates:
            - Path=/health
          filters:
            - SetStatus=200

      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600

# 日志配置
logging:
  level:
    com.wit.gateway: INFO
    org.springframework.cloud.gateway: INFO
    org.springframework.web.reactive: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 网关自定义配置
wit:
  gateway:
    # 多租户配置
    tenant:
      validation-enabled: true
      cache-ttl: 3600
      header-name: X-Tenant-Id

    # 限流配置
    rate-limit:
      enabled: true
      default-limit: 1000
      default-window: 60
      redis-key-prefix: "wit:gateway:rate_limit:"

    # 熔断器配置
    circuit-breaker:
      enabled: true
      failure-threshold: 0.6
      minimum-requests: 20
      sleep-window: 30
      timeout: 5000

    # 认证配置
    auth:
      enabled: true
      jwt-secret: ${JWT_SECRET:wit-mall-gateway-secret-key-2024-very-long-secret-for-production}
      token-expire: 7200
      exclude-paths:
        - /api/auth/login
        - /api/auth/register
        - /api/auth/refresh
        - /health
        - /actuator/**

    # 监控配置
    monitoring:
      enabled: true
      slow-request-threshold: 3000
      metrics-retention-hours: 24
      alert-enabled: true

    # 安全配置
    security:
      # IP白名单（生产环境根据实际情况配置）
      ip-whitelist:
        enabled: false
        allowed-ips: []
      
      # 请求大小限制
      max-request-size: 10MB
      
      # 请求头限制
      max-header-size: 8KB

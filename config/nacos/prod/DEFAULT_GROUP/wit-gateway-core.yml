# Wit Mall 网关服务 - 核心配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: DEFAULT_GROUP

server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: wit-gateway
  
  # Redis配置 - 网关专用
  redis:
    host: wit-redis
    port: 6379
    timeout: 3000ms
    password: ${REDIS_PASSWORD:}
    database: 0  # 网关使用数据库0
    lettuce:
      pool:
        max-active: 200
        max-idle: 20
        min-idle: 5
        max-wait: 3000ms
        time-between-eviction-runs: 30s

  cloud:
    nacos:
      discovery:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d  # wit 命名空间ID
        group: DEFAULT_GROUP
        cluster-name: wit-cluster
        metadata:
          version: 1.0.0
          zone: prod
          service-type: gateway
      config:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
        # 引用其他配置文件
        extension-configs:
          - data-id: wit-gateway-routes.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: wit-gateway-security.yml
            group: DEFAULT_GROUP
            refresh: true
        # 引用公共配置
        shared-configs:
          - data-id: common-logging.yml
            group: COMMON_GROUP
            refresh: true
    
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      
      # 全局过滤器配置
      default-filters:
        - name: Retry
          args:
            retries: 3
            methods: GET,POST,PUT,DELETE
            series: SERVER_ERROR
            exceptions: java.io.IOException,java.util.concurrent.TimeoutException
        - name: RequestTime
          args:
            enabled: true
        - name: AddRequestHeader
          args:
            name: X-Gateway-Version
            value: 1.0.0
      
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 网关核心配置
wit:
  gateway:
    # 基础配置
    basic:
      # 服务名称
      service-name: wit-gateway
      # 服务版本
      version: 1.0.0
      # 服务描述
      description: Wit Mall API Gateway Service
      
    # 监控配置
    monitoring:
      enabled: true
      slow-request-threshold: 3000
      metrics-retention-hours: 24
      alert-enabled: true
      
    # 健康检查配置
    health-check:
      enabled: true
      check-interval: 30
      timeout: 5000
      
    # 请求配置
    request:
      # 请求大小限制
      max-request-size: 10MB
      # 请求头限制
      max-header-size: 8KB
      # 请求超时时间
      timeout: 30000

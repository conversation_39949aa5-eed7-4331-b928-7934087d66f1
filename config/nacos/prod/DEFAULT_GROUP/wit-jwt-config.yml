# Wit Mall 统一JWT配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: DEFAULT_GROUP

# 统一JWT配置
wit:
  jwt:
    # JWT密钥（生产环境请使用环境变量）
    secret: ${JWT_SECRET:wit-mall-unified-secret-key-2024-very-long-secret-for-production-environment}
    
    # JWT过期时间（秒）
    expiration: 7200  # 2小时
    
    # 刷新token过期时间（秒）
    refresh-expiration: 604800  # 7天
    
    # JWT签发者
    issuer: wit-mall
    
    # JWT算法
    algorithm: HS256
    
    # 是否在响应头中返回token
    response-header: true
    
    # token请求头名称
    header-name: Authorization
    
    # token前缀
    token-prefix: "Bearer "
    
    # 租户头名称
    tenant-header-name: X-Tenant-Id

# 认证配置
auth:
  # 白名单路径（不需要认证）
  exclude-paths:
    - /*/auth/login
    - /*/auth/register
    - /*/auth/refresh
    - /*/auth/captcha
    - /*/product/list
    - /*/product/detail/**
    - /*/search/**
    - /health
    - /actuator/health
    - /actuator/info
    - /gateway/health
    - /gateway/info
    - /gateway/tenants
    - /gateway/test/**
    
  # 认证头配置
  header:
    token-name: Authorization
    token-prefix: "Bearer "
    tenant-name: X-Tenant-Id
    user-id-name: X-User-Id
    username-name: X-Username
    user-roles-name: X-User-Roles

# 安全配置
security:
  # 密码配置
  password:
    # 密码加密强度
    strength: 12
    # 密码最小长度
    min-length: 8
    # 密码最大长度
    max-length: 20
    # 是否需要包含特殊字符
    require-special-char: true
    # 密码过期天数
    expire-days: 90
    
  # 登录限制
  login:
    # 最大失败次数
    max-fail-count: 5
    # 锁定时间（分钟）
    lock-time: 30
    # 验证码触发失败次数
    captcha-trigger-count: 3
    
  # 会话配置
  session:
    # 是否允许多地登录
    allow-multiple-login: false
    # 会话超时时间（分钟）
    timeout: 120
    # 踢出之前登录的用户
    kick-out-previous: true

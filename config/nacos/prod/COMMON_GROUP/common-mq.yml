# 公共消息队列配置 - 生产环境
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: COMMON_GROUP
# 说明: 所有微服务共享的消息队列配置

spring:
  # RabbitMQ配置
  rabbitmq:
    host: wit-rabbitmq
    port: 5672
    username: ${RABBITMQ_USERNAME:admin}
    password: ${RABBITMQ_PASSWORD:admin123}
    virtual-host: /wit-mall
    
    # 连接配置
    connection-timeout: 15000
    # 发布确认
    publisher-confirm-type: correlated
    # 发布返回
    publisher-returns: true
    # 手动确认
    listener:
      simple:
        acknowledge-mode: manual
        # 并发消费者数量
        concurrency: 5
        # 最大并发消费者数量
        max-concurrency: 10
        # 预取数量
        prefetch: 10
        # 重试配置
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          max-interval: 10000
          multiplier: 2
        # 死信队列配置
        default-requeue-rejected: false
        
    # 模板配置
    template:
      # 消息确认回调
      mandatory: true
      # 重试配置
      retry:
        enabled: true
        initial-interval: 1000
        max-attempts: 3
        max-interval: 10000
        multiplier: 2

# Wit Mall 消息队列配置
wit:
  mq:
    # 基础配置
    basic:
      # 是否启用消息队列
      enabled: true
      # 消息序列化方式
      serializer: json  # json, protobuf, avro
      # 消息压缩
      compression: gzip
      # 消息加密
      encryption:
        enabled: false
        algorithm: AES
        key: ${MQ_ENCRYPTION_KEY:}
        
    # 交换机配置
    exchanges:
      # 用户相关交换机
      user:
        name: wit.user.exchange
        type: topic
        durable: true
        auto-delete: false
        
      # 订单相关交换机
      order:
        name: wit.order.exchange
        type: topic
        durable: true
        auto-delete: false
        
      # 商品相关交换机
      product:
        name: wit.product.exchange
        type: topic
        durable: true
        auto-delete: false
        
      # 支付相关交换机
      payment:
        name: wit.payment.exchange
        type: topic
        durable: true
        auto-delete: false
        
      # 库存相关交换机
      inventory:
        name: wit.inventory.exchange
        type: topic
        durable: true
        auto-delete: false
        
      # 通知相关交换机
      notification:
        name: wit.notification.exchange
        type: topic
        durable: true
        auto-delete: false
        
      # 死信交换机
      dead-letter:
        name: wit.dead.letter.exchange
        type: direct
        durable: true
        auto-delete: false
        
    # 队列配置
    queues:
      # 用户注册队列
      user-register:
        name: wit.user.register.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: user.register
        exchange: wit.user.exchange
        
      # 用户登录队列
      user-login:
        name: wit.user.login.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: user.login
        exchange: wit.user.exchange
        
      # 订单创建队列
      order-create:
        name: wit.order.create.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: order.create
        exchange: wit.order.exchange
        
      # 订单支付队列
      order-payment:
        name: wit.order.payment.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: order.payment
        exchange: wit.order.exchange
        
      # 订单取消队列
      order-cancel:
        name: wit.order.cancel.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: order.cancel
        exchange: wit.order.exchange
        
      # 库存扣减队列
      inventory-deduct:
        name: wit.inventory.deduct.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: inventory.deduct
        exchange: wit.inventory.exchange
        
      # 库存回滚队列
      inventory-rollback:
        name: wit.inventory.rollback.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: inventory.rollback
        exchange: wit.inventory.exchange
        
      # 支付成功队列
      payment-success:
        name: wit.payment.success.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: payment.success
        exchange: wit.payment.exchange
        
      # 支付失败队列
      payment-failure:
        name: wit.payment.failure.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: payment.failure
        exchange: wit.payment.exchange
        
      # 邮件通知队列
      notification-email:
        name: wit.notification.email.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: notification.email
        exchange: wit.notification.exchange
        
      # 短信通知队列
      notification-sms:
        name: wit.notification.sms.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: notification.sms
        exchange: wit.notification.exchange
        
      # 死信队列
      dead-letter:
        name: wit.dead.letter.queue
        durable: true
        exclusive: false
        auto-delete: false
        routing-key: dead.letter
        exchange: wit.dead.letter.exchange
        
    # 消息重试配置
    retry:
      # 最大重试次数
      max-attempts: 3
      # 初始重试间隔（毫秒）
      initial-interval: 1000
      # 最大重试间隔（毫秒）
      max-interval: 10000
      # 重试间隔倍数
      multiplier: 2.0
      # 是否启用指数退避
      exponential-backoff: true
      
    # 消息持久化配置
    persistence:
      # 是否启用消息持久化
      enabled: true
      # 持久化策略
      strategy: database  # database, file, memory
      # 数据库配置（当strategy为database时）
      database:
        table-name: mq_message_store
        cleanup-interval: 3600  # 清理间隔（秒）
        retention-days: 7       # 保留天数
        
    # 消息监控配置
    monitoring:
      enabled: true
      # 监控指标
      metrics:
        - message-count      # 消息数量
        - message-rate       # 消息速率
        - consumer-count     # 消费者数量
        - queue-depth        # 队列深度
        - processing-time    # 处理时间
        - error-rate         # 错误率
        
      # 告警配置
      alerts:
        # 队列积压告警
        - name: queue-backlog
          condition: "queue_depth > 1000"
          threshold: 1000
          duration: 5m
          
        # 消费失败率告警
        - name: consume-failure-rate
          condition: "error_rate > 0.05"
          threshold: 0.05
          duration: 5m
          
        # 消费者数量告警
        - name: consumer-count
          condition: "consumer_count < 1"
          threshold: 1
          duration: 2m
          
    # 消息路由配置
    routing:
      # 路由规则
      rules:
        # 用户相关消息路由
        - pattern: "user.*"
          exchange: wit.user.exchange
          
        # 订单相关消息路由
        - pattern: "order.*"
          exchange: wit.order.exchange
          
        # 商品相关消息路由
        - pattern: "product.*"
          exchange: wit.product.exchange
          
        # 支付相关消息路由
        - pattern: "payment.*"
          exchange: wit.payment.exchange
          
        # 库存相关消息路由
        - pattern: "inventory.*"
          exchange: wit.inventory.exchange
          
        # 通知相关消息路由
        - pattern: "notification.*"
          exchange: wit.notification.exchange
          
    # 消息过滤配置
    filter:
      enabled: true
      # 过滤规则
      rules:
        # 重复消息过滤
        - type: duplicate
          enabled: true
          window: 300  # 5分钟窗口
          
        # 消息大小过滤
        - type: size
          enabled: true
          max-size: 1MB
          
        # 消息频率过滤
        - type: rate
          enabled: true
          max-rate: 1000  # 每秒最大消息数
          
    # 消息事务配置
    transaction:
      enabled: true
      # 事务超时时间（秒）
      timeout: 30
      # 事务日志保留天数
      log-retention-days: 7
      
    # 消息安全配置
    security:
      # 消息签名
      signature:
        enabled: false
        algorithm: HMAC-SHA256
        secret: ${MQ_SIGNATURE_SECRET:}
        
      # 访问控制
      access-control:
        enabled: true
        # 允许的消费者
        allowed-consumers: []
        # 允许的生产者
        allowed-producers: []

# Redis配置文件 - 在Nacos中创建
# Data ID: redis-config.yml
# Group: DEFAULT_GROUP

spring:
  data:
    redis:
      # Redis服务器地址
      host: localhost
      # Redis服务器连接端口
      port: 6379
      # Redis服务器连接密码（默认为空）
      password: 
      # 连接超时时间
      timeout: 10000ms
      # 选择哪个数据库，默认是0
      database: 0
      # Lettuce连接池配置
      lettuce:
        pool:
          # 连接池最大连接数（使用负值表示没有限制）
          max-active: 8
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池中的最小空闲连接
          min-idle: 0
        # 关闭超时时间
        shutdown-timeout: 100ms
      # Redis缓存配置
      cache:
        # 缓存类型
        type: redis
        # 缓存时间配置
        redis:
          # 缓存过期时间
          time-to-live: 600000ms
          # 是否缓存空值
          cache-null-values: false
          # 键前缀
          key-prefix: "wit:cache:"
          # 是否使用键前缀
          use-key-prefix: true

# 自定义Redis配置
redis:
  # 默认过期时间（秒）
  default-expire-time: 3600
  # 锁的过期时间（秒）
  lock-expire-time: 30
  # 键前缀
  key-prefix: "wit:"
  # 分布式锁前缀
  lock-prefix: "wit:lock:"
  # 缓存前缀
  cache-prefix: "wit:cache:"
  # 会话前缀
  session-prefix: "wit:session:"
  # 验证码前缀
  captcha-prefix: "wit:captcha:"
  # 限流前缀
  rate-limit-prefix: "wit:rate-limit:"

# Dubbo配置文件 - 在Nacos中创建
# Data ID: dubbo-config.yml
# Group: DEFAULT_GROUP

dubbo:
  # 应用配置
  application:
    # 应用名称
    name: ${spring.application.name}
    # 应用版本
    version: 1.0.0
    # 应用负责人
    owner: wit
    # 应用组织
    organization: wit-mall
  
  # 注册中心配置
  registry:
    # 注册中心地址
    address: nacos://localhost:8848
    # 注册中心参数
    parameters:
      namespace: wit-mall
      group: DEFAULT_GROUP
    # 注册超时时间
    timeout: 5000
    # 会话超时时间
    session-timeout: 60000
    # 检查注册中心是否存在
    check: false
  
  # 协议配置
  protocol:
    # 协议名称
    name: dubbo
    # 协议端口（-1表示自动分配）
    port: -1
    # 线程池类型
    threadpool: fixed
    # 线程池大小
    threads: 200
    # IO线程池大小
    iothreads: 4
    # 请求及响应数据包大小限制
    payload: 8388608
    # 序列化方式
    serialization: hessian2
  
  # 提供者配置
  provider:
    # 超时时间
    timeout: 3000
    # 重试次数
    retries: 0
    # 负载均衡策略
    loadbalance: random
    # 集群容错策略
    cluster: failfast
    # 线程池
    threadpool: fixed
    # 线程数
    threads: 200
    # 最大请求数据包大小
    payload: 8388608
    # 缓存类型
    cache: false
    # 结果缓存
    validation: false
    # 是否异步
    async: false
    # 令牌验证
    token: false
    # 访问日志
    accesslog: false
    # 服务降级
    mock: false
  
  # 消费者配置
  consumer:
    # 超时时间
    timeout: 3000
    # 重试次数
    retries: 0
    # 负载均衡策略
    loadbalance: random
    # 集群容错策略
    cluster: failfast
    # 是否检查提供者存在
    check: false
    # 是否异步
    async: false
    # 缓存类型
    cache: false
    # 结果缓存
    validation: false
    # 令牌验证
    token: false
    # 服务降级
    mock: false
  
  # 监控配置
  monitor:
    protocol: registry
  
  # 配置中心
  config-center:
    address: nacos://localhost:8848
    parameters:
      namespace: wit-mall
      group: DEFAULT_GROUP
  
  # 元数据中心
  metadata-report:
    address: nacos://localhost:8848
    parameters:
      namespace: wit-mall
      group: DEFAULT_GROUP
  
  # 扫描包路径
  scan:
    base-packages: com.wit.*.service.impl

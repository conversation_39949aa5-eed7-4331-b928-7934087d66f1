# 📜 Wit Mall 脚本使用指南

## 📋 概述

本文档详细介绍 Wit Mall 项目中所有脚本和命令的作用、使用方法和注意事项。

## 🛠️ 项目脚本总览

| 脚本名称 | 类型 | 作用 | 使用场景 |
|---------|------|------|----------|
| `start-fullstack.bat` | 启动脚本 | 一键启动全栈项目 | 推荐的完整启动方式 |
| `start-frontend.bat` | 启动脚本 | 启动前端项目 | 前端开发调试 |
| `start-all.bat` | 启动脚本 | 启动后端服务 | 后端服务启动 |
| `stop-all.bat` | 停止脚本 | 停止所有服务 | Windows环境服务停止 |
| `check-project.bat` | 检查脚本 | 检查项目环境和依赖 | 项目环境验证 |
| `docker-compose.yml` | 编排文件 | 启动基础设施服务 | 开发环境搭建 |

## 🚀 启动脚本详解

### 1. start-all.bat

#### 功能说明
Windows环境下的一键启动脚本，按顺序启动基础设施和微服务。

#### 执行流程
```batch
1. 启动Docker Compose基础设施服务
2. 等待30秒让基础设施完全启动
3. 编译整个项目
4. 按顺序启动微服务：
   - wit-gateway (网关服务)
   - wit-auth (认证服务)  
   - wit-user (用户服务)
   - wit-product (商品服务)
   - wit-file (文件服务)
   - wit-schedule (定时任务服务)
```

#### 使用方法
```bash
# 方式1：双击执行
双击 start-all.bat 文件

# 方式2：命令行执行
start-all.bat

# 方式3：PowerShell执行
.\start-all.bat
```

#### 注意事项
- 确保Docker Desktop已启动
- 确保8080-8095端口未被占用
- 首次运行需要下载Docker镜像，耗时较长
- 每个服务启动间隔5-10秒，避免依赖冲突

#### 启动后验证
```bash
# 检查基础设施
docker-compose ps

# 检查服务状态
curl http://localhost:8080/actuator/health  # 网关
curl http://localhost:8082/actuator/health  # 用户服务
```

### 2. stop-all.bat

#### 功能说明
停止所有正在运行的微服务和基础设施。

#### 使用方法
```bash
# 执行停止脚本
stop-all.bat
```

#### 执行内容
```batch
1. 停止所有Java进程（微服务）
2. 停止Docker Compose服务
3. 清理临时文件
```

## 🏗️ 项目生成脚本

### 1. generate-services.ps1

#### 功能说明
PowerShell脚本，用于批量生成微服务模块的标准结构。

#### 生成的服务列表
```powershell
- wit-cart (购物车服务, 端口8085)
- wit-payment (支付服务, 端口8086)
- wit-inventory (库存服务, 端口8087)
- wit-marketing (营销服务, 端口8088)
- wit-search (搜索服务, 端口8089)
- wit-recommendation (推荐服务, 端口8090)
- wit-review (评价服务, 端口8091)
- wit-notification (通知服务, 端口8092)
- wit-system (系统服务, 端口8094)
- wit-analytics (分析服务, 端口8096)
- wit-aftersales (售后服务, 端口8097)
```

#### 生成的文件结构
```
wit-[service]/
├── pom.xml                    # Maven配置文件
├── src/
│   ├── main/
│   │   ├── java/com/wit/[service]/
│   │   │   └── [Service]Application.java  # 启动类
│   │   └── resources/         # 资源目录
│   └── test/java/            # 测试目录
```

#### 使用方法
```powershell
# 在PowerShell中执行
.\generate-services.ps1

# 或者右键选择"使用PowerShell运行"
```

#### ⚠️ 注意事项
- **此脚本已执行完成，无需再次运行**
- 如需添加新服务，可参考脚本内容手动创建
- 执行前会检查目录是否存在，避免覆盖

### 2. fix-applications.ps1

#### 功能说明
修复生成的启动类中的@MapperScan注解问题。

#### 修复内容
```powershell
1. 移除 import org.mybatis.spring.annotation.MapperScan;
2. 移除 @MapperScan("com.wit.[service].mapper")
3. 保持其他配置不变
```

#### 使用方法
```powershell
# 在PowerShell中执行
.\fix-applications.ps1
```

#### ⚠️ 注意事项
- **此脚本已执行完成，无需再次运行**
- 修复后的启动类更加简洁，避免mapper扫描错误

## ✅ 验证脚本

### verify-project.bat

#### 功能说明
验证项目结构的完整性，检查所有必要的文件和目录是否存在。

#### 检查项目
```batch
1. 父项目配置 (pom.xml)
2. 公共模块 (wit-common)
3. 18个微服务模块
4. Docker配置文件
5. Nacos配置模板
6. Nginx配置
7. 启动停止脚本
8. 项目文档
```

#### 使用方法
```bash
# 执行验证
verify-project.bat
```

#### 输出示例
```
========================================
   Wit Mall 项目结构验证
========================================

正在验证项目结构...

1. 检查父项目配置...
✓ 父项目 pom.xml 存在

2. 检查公共模块...
✓ wit-common 模块存在

3. 检查核心服务模块...
✓ wit-gateway 模块存在
✓ wit-auth 模块存在
✓ wit-user 模块存在
...

项目包含：
- 1个公共模块 (wit-common)
- 1个网关服务 (wit-gateway)  
- 17个业务服务模块
- 完整的Docker环境配置
- Nacos配置模板
- 启动停止脚本
- 详细的项目文档
```

## 🐳 Docker编排文件

### docker-compose.yml

#### 功能说明
定义和启动项目所需的所有基础设施服务。

#### 包含的服务
```yaml
- MySQL 8.0          # 主数据库
- Redis 7.0          # 缓存数据库
- Nacos 2.3.0        # 注册中心+配置中心
- Sentinel           # 流量控制
- RabbitMQ 3.12      # 消息队列
- Elasticsearch 8.11 # 搜索引擎
- Kibana 8.11        # 日志分析
- MinIO              # 对象存储
- XXL-Job            # 定时任务
- Nginx              # 反向代理
```

#### 使用方法
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs [service-name]

# 停止所有服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

#### 端口映射
| 服务 | 内部端口 | 外部端口 | 访问地址 |
|------|---------|---------|----------|
| MySQL | 3306 | 3306 | localhost:3306 |
| Redis | 6379 | 6379 | localhost:6379 |
| Nacos | 8848 | 8848 | http://localhost:8848/nacos |
| Sentinel | 8858 | 8858 | http://localhost:8858 |
| RabbitMQ | 5672/15672 | 5672/15672 | http://localhost:15672 |
| Elasticsearch | 9200 | 9200 | http://localhost:9200 |
| Kibana | 5601 | 5601 | http://localhost:5601 |
| MinIO | 9000/9001 | 9000/9001 | http://localhost:9001 |
| XXL-Job | 8080 | 8080 | http://localhost:8080/xxl-job-admin |
| Nginx | 80 | 80 | http://localhost |

## 🔧 Maven命令

### 常用编译命令
```bash
# 清理编译
mvn clean compile

# 跳过测试编译
mvn clean compile -DskipTests

# 编译并运行测试
mvn clean test

# 打包
mvn clean package

# 安装到本地仓库
mvn clean install

# 编译指定模块
mvn clean compile -pl wit-user

# 编译指定模块及其依赖
mvn clean compile -pl wit-user -am
```

### 服务启动命令
```bash
# 启动单个服务
cd wit-gateway
mvn spring-boot:run

# 指定配置文件启动
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 指定JVM参数启动
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xmx1g"
```

## 🚨 故障排查脚本

### 1. 端口检查脚本
```bash
#!/bin/bash
# check-ports.sh

PORTS=(3306 6379 8848 8858 5672 15672 9200 5601 9000 9001 8080 8081 8082)

echo "检查端口占用情况："
for port in "${PORTS[@]}"; do
    if netstat -tuln | grep ":$port " > /dev/null; then
        echo "✓ 端口 $port 已被占用"
    else
        echo "✗ 端口 $port 未被占用"
    fi
done
```

### 2. 服务健康检查脚本
```bash
#!/bin/bash
# health-check.sh

SERVICES=(
    "wit-gateway:8080"
    "wit-auth:8081" 
    "wit-user:8082"
    "wit-product:8083"
)

echo "检查服务健康状态："
for service in "${SERVICES[@]}"; do
    IFS=':' read -r name port <<< "$service"
    
    if curl -f -s "http://localhost:$port/actuator/health" > /dev/null; then
        echo "✓ $name 服务正常"
    else
        echo "✗ $name 服务异常"
    fi
done
```

## 📝 使用建议

### 1. 开发环境
```bash
# 推荐的启动顺序
1. docker-compose up -d          # 启动基础设施
2. 等待2-3分钟让服务完全启动
3. mvn clean compile -DskipTests  # 编译项目
4. 手动启动需要的服务进行开发调试
```

### 2. 测试环境
```bash
# 使用启动脚本
start-all.bat  # Windows
./start-all.sh # Linux/Mac
```

### 3. 生产环境
```bash
# 不建议使用脚本，推荐使用
- Docker Swarm
- Kubernetes
- 或其他容器编排工具
```

## ⚠️ 注意事项

### 1. 系统要求
- **内存**：至少8GB可用内存
- **磁盘**：至少10GB可用空间
- **网络**：确保Docker Hub访问正常

### 2. 常见问题
- **端口冲突**：检查端口占用情况
- **内存不足**：关闭不必要的应用程序
- **网络问题**：检查Docker网络配置
- **权限问题**：确保有足够的文件系统权限

### 3. 最佳实践
- 开发时只启动需要的服务
- 定期清理Docker镜像和容器
- 监控系统资源使用情况
- 及时查看日志排查问题

## 🎯 脚本定制

### 添加新服务到启动脚本
```batch
REM 在start-all.bat中添加
echo 启动新服务...
start "wit-newservice" cmd /k "cd wit-newservice && mvn spring-boot:run"
timeout /t 5 /nobreak
```

### 创建自定义启动脚本
```bash
#!/bin/bash
# custom-start.sh

# 只启动核心服务
services=("wit-gateway" "wit-user" "wit-product")

for service in "${services[@]}"; do
    echo "启动 $service..."
    cd $service
    mvn spring-boot:run &
    cd ..
    sleep 10
done
```

## 📚 相关文档

- [业务开发指南](BUSINESS_DEVELOPMENT_GUIDE.md)
- [部署指南](DEPLOYMENT_GUIDE.md)
- [运维监控指南](OPERATIONS_GUIDE.md)
- [项目总览](PROJECT_OVERVIEW.md)

祝您使用愉快！🚀

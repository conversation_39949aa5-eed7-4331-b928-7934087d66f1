package com.wit.recommendation;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Recommendation Service 启动类
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.wit.recommendation", "com.wit.common"})
@EnableDiscoveryClient
@EnableDubbo
public class RecommendationApplication {

    public static void main(String[] args) {
        SpringApplication.run(RecommendationApplication.class, args);
    }
}

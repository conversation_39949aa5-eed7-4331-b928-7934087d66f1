# 🔧 Wit Mall 运维监控指南

## 📋 概述

本文档详细介绍 Wit Mall 微服务项目的运维监控方案，包括系统监控、日志管理、性能调优和故障处理。

## 📊 监控体系架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│   Prometheus    │───▶│    Grafana      │
│   (Metrics)     │    │   (Collector)   │    │  (Dashboard)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│   Filebeat      │───▶│  Elasticsearch  │
│    (Logs)       │    │  (Collector)    │    │   (Storage)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                              ┌─────────────────┐
                                              │     Kibana      │
                                              │   (Analysis)    │
                                              └─────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│     Jaeger      │───▶│   Jaeger UI     │
│   (Traces)      │    │   (Collector)   │    │   (Tracing)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 监控指标

### 1. 系统级监控

#### CPU监控
```yaml
# Prometheus规则
groups:
- name: system.rules
  rules:
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is above 80% for more than 5 minutes"
```

#### 内存监控
```yaml
- alert: HighMemoryUsage
  expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "High memory usage detected"
    description: "Memory usage is above 85% for more than 5 minutes"
```

#### 磁盘监控
```yaml
- alert: DiskSpaceLow
  expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
  for: 5m
  labels:
    severity: critical
  annotations:
    summary: "Disk space is running low"
    description: "Disk space is below 10%"
```

### 2. 应用级监控

#### JVM监控
```java
// 在应用中暴露JVM指标
@Component
public class JvmMetrics {
    
    private final MeterRegistry meterRegistry;
    
    public JvmMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        new JvmGcMetrics().bindTo(meterRegistry);
        new JvmMemoryMetrics().bindTo(meterRegistry);
        new JvmThreadMetrics().bindTo(meterRegistry);
    }
}
```

#### 业务指标监控
```java
@Service
public class UserService {
    
    private final Counter userCreatedCounter;
    private final Timer userQueryTimer;
    
    public UserService(MeterRegistry meterRegistry) {
        this.userCreatedCounter = Counter.builder("user.created")
            .description("Number of users created")
            .register(meterRegistry);
            
        this.userQueryTimer = Timer.builder("user.query.duration")
            .description("User query duration")
            .register(meterRegistry);
    }
    
    public User createUser(UserCreateDTO dto) {
        // 业务逻辑
        userCreatedCounter.increment();
        return user;
    }
    
    public User getUserById(Long id) {
        return userQueryTimer.recordCallable(() -> {
            // 查询逻辑
            return userRepository.findById(id);
        });
    }
}
```

### 3. 数据库监控

#### MySQL监控
```yaml
# docker-compose.yml
services:
  mysql-exporter:
    image: prom/mysqld-exporter
    environment:
      - DATA_SOURCE_NAME=exporter:password@(mysql:3306)/
    ports:
      - "9104:9104"
    depends_on:
      - mysql
```

#### Redis监控
```yaml
  redis-exporter:
    image: oliver006/redis_exporter
    environment:
      - REDIS_ADDR=redis:6379
    ports:
      - "9121:9121"
    depends_on:
      - redis
```

## 📝 日志管理

### 1. 日志配置

#### Logback配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <springProfile name="prod">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
        
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/var/log/wit-mall/application.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/var/log/wit-mall/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>3GB</totalSizeCap>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

#### 链路追踪配置
```java
@Component
public class TraceFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) 
            throws IOException, ServletException {
        
        String traceId = UUID.randomUUID().toString().replace("-", "");
        MDC.put("traceId", traceId);
        
        try {
            chain.doFilter(request, response);
        } finally {
            MDC.clear();
        }
    }
}
```

### 2. ELK Stack配置

#### Elasticsearch配置
```yaml
# docker-compose.yml
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
```

#### Logstash配置
```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] {
    mutate {
      add_field => { "service_name" => "%{[fields][service]}" }
    }
  }
  
  if [message] =~ /^\{/ {
    json {
      source => "message"
    }
  }
  
  date {
    match => [ "timestamp", "ISO8601" ]
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "wit-mall-logs-%{+YYYY.MM.dd}"
  }
}
```

#### Filebeat配置
```yaml
# filebeat.yml
filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
    - add_docker_metadata:
        host: "unix:///var/run/docker.sock"
  fields:
    service: wit-mall
  fields_under_root: true

output.logstash:
  hosts: ["logstash:5044"]

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
```

## 🚨 告警配置

### 1. Prometheus告警规则

#### 服务可用性告警
```yaml
# alert-rules.yml
groups:
- name: service.rules
  rules:
  - alert: ServiceDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Service {{ $labels.instance }} is down"
      description: "Service {{ $labels.instance }} has been down for more than 1 minute"

  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is above 10% for more than 5 minutes"

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is above 1 second"
```

### 2. AlertManager配置
```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    subject: 'Wit Mall Alert: {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      {{ end }}
  
  webhook_configs:
  - url: 'http://webhook-server:9093/webhook'
    send_resolved: true
```

### 3. 钉钉告警集成
```python
# webhook-server.py
from flask import Flask, request
import requests
import json

app = Flask(__name__)

DINGTALK_WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN"

@app.route('/webhook', methods=['POST'])
def webhook():
    data = request.json
    
    for alert in data.get('alerts', []):
        message = {
            "msgtype": "text",
            "text": {
                "content": f"🚨 Wit Mall 告警\n"
                          f"服务: {alert.get('labels', {}).get('instance', 'Unknown')}\n"
                          f"告警: {alert.get('annotations', {}).get('summary', 'No summary')}\n"
                          f"描述: {alert.get('annotations', {}).get('description', 'No description')}\n"
                          f"状态: {alert.get('status', 'Unknown')}"
            }
        }
        
        requests.post(DINGTALK_WEBHOOK, json=message)
    
    return "OK"

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9093)
```

## 📈 Grafana仪表板

### 1. 系统监控仪表板
```json
{
  "dashboard": {
    "title": "Wit Mall System Monitoring",
    "panels": [
      {
        "title": "CPU Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg by(instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100",
            "legendFormat": "{{instance}}"
          }
        ]
      }
    ]
  }
}
```

### 2. 应用监控仪表板
```json
{
  "dashboard": {
    "title": "Wit Mall Application Monitoring",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{service}} - {{method}} {{uri}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      }
    ]
  }
}
```

## 🔍 故障排查

### 1. 常用排查命令

#### 系统资源检查
```bash
# CPU使用率
top -p $(pgrep -d',' java)

# 内存使用情况
free -h
ps aux --sort=-%mem | head

# 磁盘使用情况
df -h
du -sh /var/log/*

# 网络连接
netstat -tulpn | grep :8080
ss -tulpn | grep :8080
```

#### 应用状态检查
```bash
# 检查服务状态
curl -s http://localhost:8080/actuator/health | jq
curl -s http://localhost:8082/actuator/health | jq

# 检查JVM状态
curl -s http://localhost:8080/actuator/metrics/jvm.memory.used | jq
curl -s http://localhost:8080/actuator/metrics/jvm.gc.pause | jq

# 检查线程状态
curl -s http://localhost:8080/actuator/threaddump > threaddump.json
```

#### 日志分析
```bash
# 查看实时日志
tail -f /var/log/wit-mall/application.log

# 搜索错误日志
grep -i error /var/log/wit-mall/application.log | tail -20

# 统计错误数量
grep -c "ERROR" /var/log/wit-mall/application.log

# 分析访问日志
awk '{print $1}' access.log | sort | uniq -c | sort -nr | head -10
```

### 2. 性能分析

#### JVM性能分析
```bash
# 生成堆转储
jcmd <pid> GC.run_finalization
jcmd <pid> VM.gc
jmap -dump:format=b,file=heapdump.hprof <pid>

# 分析GC日志
jstat -gc <pid> 1s 10

# 查看JVM参数
jcmd <pid> VM.flags
```

#### 数据库性能分析
```sql
-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看连接数
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';

-- 查看锁等待
SELECT * FROM information_schema.INNODB_LOCKS;
SELECT * FROM information_schema.INNODB_LOCK_WAITS;
```

## 🛠️ 运维脚本

### 1. 健康检查脚本
```bash
#!/bin/bash
# health-check.sh

SERVICES=("wit-gateway:8080" "wit-user:8082" "wit-product:8083")
FAILED_SERVICES=()

for service in "${SERVICES[@]}"; do
    IFS=':' read -r name port <<< "$service"
    
    if ! curl -f -s "http://localhost:$port/actuator/health" > /dev/null; then
        FAILED_SERVICES+=("$name")
    fi
done

if [ ${#FAILED_SERVICES[@]} -eq 0 ]; then
    echo "All services are healthy"
    exit 0
else
    echo "Failed services: ${FAILED_SERVICES[*]}"
    exit 1
fi
```

### 2. 日志清理脚本
```bash
#!/bin/bash
# log-cleanup.sh

LOG_DIR="/var/log/wit-mall"
RETENTION_DAYS=30

find $LOG_DIR -name "*.log" -type f -mtime +$RETENTION_DAYS -delete
find $LOG_DIR -name "*.log.gz" -type f -mtime +$RETENTION_DAYS -delete

echo "Log cleanup completed. Removed files older than $RETENTION_DAYS days."
```

### 3. 备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/wit-mall"
DATE=$(date +%Y%m%d_%H%M%S)

# 数据库备份
mysqldump -h mysql -u root -p$MYSQL_ROOT_PASSWORD --all-databases > $BACKUP_DIR/mysql_$DATE.sql

# 配置备份
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /opt/wit-mall/config

# 清理旧备份
find $BACKUP_DIR -name "*.sql" -type f -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -type f -mtime +7 -delete

echo "Backup completed: $DATE"
```

## 📋 运维检查清单

### 日常检查
- [ ] 服务健康状态检查
- [ ] 系统资源使用率检查
- [ ] 错误日志检查
- [ ] 数据库连接池状态检查
- [ ] 缓存命中率检查

### 周期性检查
- [ ] 磁盘空间清理
- [ ] 日志文件轮转
- [ ] 数据库性能分析
- [ ] 安全补丁更新
- [ ] 备份数据验证

### 应急响应
- [ ] 告警响应流程
- [ ] 服务降级方案
- [ ] 数据恢复流程
- [ ] 联系人信息更新

## 🎯 性能优化建议

### 1. JVM调优
```bash
# 生产环境JVM参数
JAVA_OPTS="-Xms4g -Xmx4g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=/var/log/wit-mall/ \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -Xloggc:/var/log/wit-mall/gc.log"
```

### 2. 数据库优化
```sql
-- 索引优化
ANALYZE TABLE user;
OPTIMIZE TABLE user;

-- 连接池配置
SET GLOBAL max_connections = 500;
SET GLOBAL innodb_buffer_pool_size = 2147483648;
```

### 3. 缓存优化
```yaml
spring:
  redis:
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
    timeout: 2000ms
```

祝您运维顺利！🎉

@echo off
chcp 65001 >nul
echo ========================================
echo    Wit Mall 前端项目启动脚本
echo ========================================
echo.

:: 检查Node.js是否安装
echo 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Node.js，请先安装Node.js 16+
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)

:: 显示Node.js版本
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

:: 检查npm是否可用
echo 检查npm环境...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：npm不可用
    pause
    exit /b 1
)

:: 显示npm版本
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: %NPM_VERSION%

echo.
echo ========================================
echo    开始启动前端项目
echo ========================================

:: 进入前端项目目录
cd /d "%~dp0wit-mall-web"
if errorlevel 1 (
    echo ❌ 错误：前端项目目录不存在
    pause
    exit /b 1
)

:: 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 检测到未安装依赖，开始安装...
    echo.
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
    echo.
) else (
    echo ✅ 依赖已安装
)

:: 启动开发服务器
echo 🚀 启动前端开发服务器...
echo.
echo 前端地址: http://localhost:3000
echo 按 Ctrl+C 停止服务器
echo.

npm run dev

pause

@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Wit Mall 多租户网关部署脚本 (Windows)
:: 作者: Wit
:: 版本: 1.0.0

echo 🚀 开始部署 Wit Mall 多租户网关...
echo ==================================

:: 检查环境
echo.
echo 🔍 检查部署环境...

:: 检查Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)
echo ✅ Docker 已安装

:: 检查Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)
echo ✅ Docker Compose 已安装

:: 检查Java
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Java 未安装，请先安装 JDK 17+
    pause
    exit /b 1
)
echo ✅ Java 已安装

:: 检查Maven
mvn -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Maven 未安装，请先安装 Maven 3.6+
    pause
    exit /b 1
)
echo ✅ Maven 已安装

echo ✅ 环境检查通过

:: 启动基础设施
echo.
echo 🏗️ 启动基础设施服务...
cd docker

:: 启动基础服务
docker-compose up -d mysql redis nacos
if errorlevel 1 (
    echo ❌ 基础服务启动失败
    pause
    exit /b 1
)

echo ⏳ 等待基础服务启动...
timeout /t 30 /nobreak >nul

echo ✅ 基础设施服务启动成功
cd ..

:: 编译项目
echo.
echo 🔨 编译项目...
call mvn clean compile -DskipTests -q
if errorlevel 1 (
    echo ❌ 项目编译失败
    pause
    exit /b 1
)
echo ✅ 项目编译成功

:: 创建日志目录
if not exist "logs" mkdir logs

:: 启动网关服务
echo.
echo 🚀 启动网关服务...
cd wit-gateway

:: 后台启动网关
start /b cmd /c "mvn spring-boot:run > ..\logs\gateway.log 2>&1"

echo ⏳ 等待网关服务启动...
timeout /t 20 /nobreak >nul

:: 检查服务是否启动成功
curl -s http://localhost:8080/gateway/health >nul 2>&1
if errorlevel 1 (
    echo ❌ 网关服务启动失败，请查看日志: logs\gateway.log
    pause
    exit /b 1
)

echo ✅ 网关服务启动成功
cd ..

:: 启动Nginx
echo.
echo 🌐 启动Nginx...
cd docker
docker-compose up -d nginx
if errorlevel 1 (
    echo ❌ Nginx启动失败
    pause
    exit /b 1
)
echo ✅ Nginx启动成功
cd ..

:: 验证部署
echo.
echo 🧪 验证部署...

:: 测试网关健康检查
echo 测试网关健康检查...
curl -s http://localhost:8080/gateway/health | findstr "UP" >nul
if errorlevel 1 (
    echo ❌ 网关健康检查失败
    pause
    exit /b 1
)
echo ✅ 网关健康检查通过

:: 测试多租户路由
echo 测试多租户路由...
curl -s "http://localhost:8080/gateway/test/route?tenantId=3921&service=user" | findstr "路由测试成功" >nul
if errorlevel 1 (
    echo ❌ 多租户路由测试失败
    pause
    exit /b 1
)
echo ✅ 多租户路由测试通过

:: 测试Nginx代理
echo 测试Nginx代理...
curl -s http://localhost/gateway/health | findstr "UP" >nul
if errorlevel 1 (
    echo ⚠️  Nginx代理测试失败，请检查配置
) else (
    echo ✅ Nginx代理测试通过
)

:: 显示部署信息
echo.
echo 🎉 Wit Mall 多租户网关部署成功！
echo =====================================
echo.
echo 📋 服务信息:
echo   - 网关服务: http://localhost:8080
echo   - 健康检查: http://localhost:8080/gateway/health
echo   - 网关信息: http://localhost:8080/gateway/info
echo   - Nginx代理: http://localhost
echo.
echo 🏢 多租户路由格式:
echo   - 直接访问: http://localhost:8080/{tenantId}/{service}/**
echo   - Nginx代理: http://localhost/{tenantId}/{service}/**
echo   - 示例: http://localhost/3921/user/api/v1/users
echo.
echo 🔧 管理接口:
echo   - 租户列表: http://localhost:8080/gateway/tenants
echo   - 租户验证: http://localhost:8080/gateway/tenants/{tenantId}/validate
echo   - 路由测试: http://localhost:8080/gateway/test/route
echo.
echo 📊 监控地址:
echo   - Nacos控制台: http://localhost:8848/nacos (nacos/nacos)
echo   - 网关日志: logs\gateway.log
echo.
echo ✅ 部署完成！

pause

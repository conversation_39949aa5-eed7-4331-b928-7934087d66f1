package com.wit.inventory;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Inventory Service 启动类
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.wit.inventory", "com.wit.common"})
@EnableDiscoveryClient
@EnableDubbo
public class InventoryApplication {

    public static void main(String[] args) {
        SpringApplication.run(InventoryApplication.class, args);
    }
}

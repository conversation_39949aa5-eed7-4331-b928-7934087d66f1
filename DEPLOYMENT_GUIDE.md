# 🚀 Wit Mall 微服务部署指南

## 📋 概述

本文档详细介绍 Wit Mall 微服务项目的部署方案，包括本地开发环境、测试环境和生产环境的部署配置。

## 🏗️ 部署架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │     Gateway     │    │   Microservices │
│     (Nginx)     │───▶│  (wit-gateway)  │───▶│   (wit-user)    │
└─────────────────┘    └─────────────────┘    │   (wit-product) │
                                              │      ...        │
┌─────────────────┐    ┌─────────────────┐    └─────────────────┘
│   Config Center │    │   Registry      │    
│     (Nacos)     │    │    (Nacos)      │    ┌─────────────────┐
└─────────────────┘    └─────────────────┘    │   Middleware    │
                                              │    (MySQL)      │
┌─────────────────┐    ┌─────────────────┐    │    (Redis)      │
│   Monitoring    │    │     Logging     │    │   (RabbitMQ)    │
│  (Prometheus)   │    │      (ELK)      │    │    (MinIO)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ 环境要求

### 基础环境
- **操作系统**：Linux (CentOS 7+/Ubuntu 18+) 或 Windows Server
- **Java**：JDK 17+
- **Maven**：3.6+
- **Docker**：20.10+
- **Docker Compose**：1.29+

### 硬件要求

#### 开发环境
- **CPU**：2核心
- **内存**：8GB
- **磁盘**：50GB

#### 生产环境
- **CPU**：8核心+
- **内存**：32GB+
- **磁盘**：200GB+ SSD

## 🏠 本地开发环境部署

### 1. 环境准备
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查Docker版本
docker --version
docker-compose --version
```

### 2. 启动基础设施
```bash
# 克隆项目
git clone <your-repository-url>
cd wit-mall

# 启动基础设施服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 3. 编译项目
```bash
# 清理并编译
./mvnw clean compile -DskipTests

# 或使用Maven
mvn clean compile -DskipTests
```

### 4. 启动微服务
```bash
# 方式1：使用启动脚本（Windows）
start-all.bat

# 方式2：使用启动脚本（Linux/Mac）
chmod +x start-all.sh
./start-all.sh

# 方式3：手动启动（推荐开发调试）
cd wit-gateway && mvn spring-boot:run &
cd wit-auth && mvn spring-boot:run &
cd wit-user && mvn spring-boot:run &
# ... 其他服务
```

### 5. 验证部署
```bash
# 检查服务健康状态
curl http://localhost:8080/actuator/health
curl http://localhost:8082/actuator/health

# 访问管理控制台
# Nacos: http://localhost:8848/nacos (nacos/nacos)
# Sentinel: http://localhost:8858 (sentinel/sentinel)
```

## 🧪 测试环境部署

### 1. 服务器准备
```bash
# 安装Docker
curl -fsSL https://get.docker.com | bash -s docker
systemctl start docker
systemctl enable docker

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```

### 2. 项目部署
```bash
# 创建部署目录
mkdir -p /opt/wit-mall
cd /opt/wit-mall

# 上传项目文件
# 可以使用git clone或scp上传

# 构建镜像
./mvnw clean package -DskipTests
docker-compose -f docker-compose.test.yml build

# 启动服务
docker-compose -f docker-compose.test.yml up -d
```

### 3. 测试环境配置
```yaml
# docker-compose.test.yml
version: '3.8'
services:
  wit-gateway:
    build: ./wit-gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=test
      - NACOS_SERVER_ADDR=nacos:8848
    depends_on:
      - nacos
      - redis
      - mysql

  wit-user:
    build: ./wit-user
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=test
      - NACOS_SERVER_ADDR=nacos:8848
    depends_on:
      - nacos
      - redis
      - mysql

  # 其他服务配置...
```

## 🏭 生产环境部署

### 1. Kubernetes部署（推荐）

#### 准备Kubernetes集群
```bash
# 安装kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
chmod +x kubectl
sudo mv kubectl /usr/local/bin/

# 验证集群连接
kubectl cluster-info
```

#### 创建命名空间
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: wit-mall
```

#### 部署配置中心
```yaml
# nacos-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nacos
  namespace: wit-mall
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nacos
  template:
    metadata:
      labels:
        app: nacos
    spec:
      containers:
      - name: nacos
        image: nacos/nacos-server:v2.3.0
        ports:
        - containerPort: 8848
        env:
        - name: MODE
          value: "cluster"
        - name: NACOS_SERVERS
          value: "nacos-0.nacos:8848 nacos-1.nacos:8848 nacos-2.nacos:8848"
        - name: MYSQL_SERVICE_HOST
          value: "mysql"
        - name: MYSQL_SERVICE_DB_NAME
          value: "nacos"
        - name: MYSQL_SERVICE_USER
          value: "nacos"
        - name: MYSQL_SERVICE_PASSWORD
          value: "nacos123"
---
apiVersion: v1
kind: Service
metadata:
  name: nacos
  namespace: wit-mall
spec:
  selector:
    app: nacos
  ports:
  - port: 8848
    targetPort: 8848
  type: ClusterIP
```

#### 部署微服务
```yaml
# wit-gateway-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wit-gateway
  namespace: wit-mall
spec:
  replicas: 2
  selector:
    matchLabels:
      app: wit-gateway
  template:
    metadata:
      labels:
        app: wit-gateway
    spec:
      containers:
      - name: wit-gateway
        image: wit-mall/wit-gateway:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: NACOS_SERVER_ADDR
          value: "nacos:8848"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: wit-gateway
  namespace: wit-mall
spec:
  selector:
    app: wit-gateway
  ports:
  - port: 8080
    targetPort: 8080
  type: LoadBalancer
```

### 2. Docker Swarm部署

#### 初始化Swarm集群
```bash
# 在管理节点执行
docker swarm init --advertise-addr <MANAGER-IP>

# 在工作节点执行（使用上面命令输出的token）
docker swarm join --token <TOKEN> <MANAGER-IP>:2377
```

#### 创建Docker Stack
```yaml
# docker-stack.yml
version: '3.8'
services:
  wit-gateway:
    image: wit-mall/wit-gateway:latest
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    networks:
      - wit-mall-network

  wit-user:
    image: wit-mall/wit-user:latest
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
    deploy:
      replicas: 2
    networks:
      - wit-mall-network

networks:
  wit-mall-network:
    driver: overlay
```

#### 部署Stack
```bash
docker stack deploy -c docker-stack.yml wit-mall
```

## 🔧 配置管理

### 1. Nacos配置
```bash
# 导入配置到Nacos
curl -X POST "http://nacos:8848/nacos/v1/cs/configs" \
  -d "dataId=wit-gateway-prod.yml" \
  -d "group=DEFAULT_GROUP" \
  -d "content=$(cat config/nacos/gateway-prod.yml)"
```

### 2. 环境变量配置
```bash
# 生产环境变量
export SPRING_PROFILES_ACTIVE=prod
export NACOS_SERVER_ADDR=nacos-cluster:8848
export MYSQL_HOST=mysql-cluster:3306
export REDIS_HOST=redis-cluster:6379
export RABBITMQ_HOST=rabbitmq-cluster:5672
```

## 📊 监控和日志

### 1. Prometheus监控
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'wit-mall'
    static_configs:
      - targets: ['wit-gateway:8080', 'wit-user:8082']
    metrics_path: '/actuator/prometheus'
```

### 2. 日志收集
```yaml
# filebeat.yml
filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
    - add_docker_metadata: ~

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
```

## 🔒 安全配置

### 1. 网络安全
```bash
# 配置防火墙
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8080/tcp
ufw enable
```

### 2. SSL证书配置
```nginx
# nginx.conf
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /etc/ssl/certs/your-cert.pem;
    ssl_certificate_key /etc/ssl/private/your-key.pem;
    
    location / {
        proxy_pass http://wit-gateway:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🚀 CI/CD流水线

### 1. Jenkins Pipeline
```groovy
pipeline {
    agent any
    
    stages {
        stage('Checkout') {
            steps {
                git 'https://github.com/your-repo/wit-mall.git'
            }
        }
        
        stage('Build') {
            steps {
                sh './mvnw clean package -DskipTests'
            }
        }
        
        stage('Test') {
            steps {
                sh './mvnw test'
            }
        }
        
        stage('Docker Build') {
            steps {
                sh 'docker build -t wit-mall/wit-gateway:${BUILD_NUMBER} ./wit-gateway'
                sh 'docker build -t wit-mall/wit-user:${BUILD_NUMBER} ./wit-user'
            }
        }
        
        stage('Deploy') {
            steps {
                sh 'kubectl set image deployment/wit-gateway wit-gateway=wit-mall/wit-gateway:${BUILD_NUMBER}'
                sh 'kubectl set image deployment/wit-user wit-user=wit-mall/wit-user:${BUILD_NUMBER}'
            }
        }
    }
}
```

### 2. GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up JDK 17
      uses: actions/setup-java@v2
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Build with Maven
      run: ./mvnw clean package -DskipTests
    
    - name: Build Docker images
      run: |
        docker build -t wit-mall/wit-gateway:${{ github.sha }} ./wit-gateway
        docker build -t wit-mall/wit-user:${{ github.sha }} ./wit-user
    
    - name: Deploy to Kubernetes
      run: |
        kubectl set image deployment/wit-gateway wit-gateway=wit-mall/wit-gateway:${{ github.sha }}
        kubectl set image deployment/wit-user wit-user=wit-mall/wit-user:${{ github.sha }}
```

## 🔍 故障排查

### 1. 常见问题
```bash
# 查看服务状态
kubectl get pods -n wit-mall
docker service ls

# 查看日志
kubectl logs -f deployment/wit-gateway -n wit-mall
docker service logs wit-mall_wit-gateway

# 查看资源使用
kubectl top pods -n wit-mall
docker stats
```

### 2. 健康检查
```bash
# 检查服务健康状态
curl http://wit-gateway:8080/actuator/health
curl http://wit-user:8082/actuator/health

# 检查服务注册
curl http://nacos:8848/nacos/v1/ns/instance/list?serviceName=wit-gateway
```

## 📝 部署清单

### 部署前检查
- [ ] 环境要求满足
- [ ] 配置文件准备完成
- [ ] 数据库初始化完成
- [ ] 镜像构建完成
- [ ] 网络配置正确

### 部署后验证
- [ ] 所有服务启动正常
- [ ] 服务注册成功
- [ ] 健康检查通过
- [ ] API接口可访问
- [ ] 监控指标正常

## 🎯 性能优化

### 1. JVM参数优化
```bash
# 生产环境JVM参数
JAVA_OPTS="-Xms2g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError"
```

### 2. 数据库连接池优化
```yaml
spring:
  datasource:
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 60000
```

## 🚀 扩容策略

### 1. 水平扩容
```bash
# Kubernetes扩容
kubectl scale deployment wit-gateway --replicas=5 -n wit-mall

# Docker Swarm扩容
docker service scale wit-mall_wit-gateway=5
```

### 2. 垂直扩容
```yaml
# 增加资源限制
resources:
  requests:
    memory: "1Gi"
    cpu: "1000m"
  limits:
    memory: "2Gi"
    cpu: "2000m"
```

祝您部署顺利！🎉

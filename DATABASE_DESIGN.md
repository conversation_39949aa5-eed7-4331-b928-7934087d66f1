# 🗄️ Wit Mall 数据库设计文档

## 📋 概述

Wit Mall 采用微服务架构，按照业务领域进行数据库分库设计，支持B2B2C多商家电商平台模式，包含完整的用户体系、商家体系、代理商体系和营销体系。

## 🏗️ 数据库架构

### 分库策略
- **按业务领域分库**：每个微服务对应一个独立的数据库
- **数据隔离**：不同服务之间通过API调用进行数据交互
- **事务一致性**：使用分布式事务（Seata）保证数据一致性

### 数据库列表

| 数据库名 | 服务名 | 功能描述 | 主要表数量 |
|---------|--------|----------|-----------|
| `wit_user` | 用户服务 | 用户管理、角色权限、商家管理、代理商体系 | 12张 |
| `wit_product` | 商品服务 | 商品管理、分类管理、品牌管理、规格管理 | 15张 |
| `wit_inventory` | 库存服务 | 库存管理、仓库管理、出入库管理 | 18张 |
| `wit_order` | 订单服务 | 订单管理、物流配送、售后服务 | 12张 |
| `wit_payment` | 支付服务 | 支付管理、退款管理、账户管理、佣金结算 | 12张 |
| `wit_cart` | 购物车服务 | 购物车管理 | 1张 |
| `wit_marketing` | 营销服务 | 优惠券、营销活动、促销管理 | 3张 |
| `wit_review` | 评价服务 | 商品评价、评论管理 | 1张 |
| `wit_notification` | 通知服务 | 消息通知、模板管理 | 2张 |
| `wit_file` | 文件服务 | 文件上传、存储管理 | 1张 |
| `wit_system` | 系统服务 | 系统配置、操作日志 | 2张 |
| `wit_schedule` | 定时任务服务 | 定时任务管理 | - |
| `wit_analytics` | 数据分析服务 | 数据统计、报表分析 | - |
| `wit_aftersales` | 售后服务 | 售后处理、退换货 | - |
| `wit_search` | 搜索服务 | 商品搜索、索引管理 | - |
| `wit_recommendation` | 推荐服务 | 商品推荐、用户画像 | - |

## 📊 核心表结构设计

### 1. 用户服务 (wit_user)

#### 核心表
- **user** - 用户基础信息表
- **user_level** - 用户等级表
- **user_address** - 用户地址表
- **merchant** - 商家信息表
- **merchant_shop** - 商家店铺表
- **agent** - 代理商表
- **agent_relation** - 代理商关系表
- **role** - 角色表
- **permission** - 权限表
- **user_role** - 用户角色关联表
- **role_permission** - 角色权限关联表

#### 设计特点
- 支持多种用户类型：普通用户、商家、代理商、管理员
- 完整的RBAC权限模型
- 多级代理商体系支持
- 用户等级和积分体系

### 2. 商品服务 (wit_product)

#### 核心表
- **category** - 商品分类表（支持多级分类）
- **category_attribute** - 分类属性表
- **brand** - 品牌表
- **category_brand** - 分类品牌关联表
- **product** - 商品主表
- **product_spec** - 商品规格表
- **product_sku** - 商品SKU表
- **product_attribute_value** - 商品属性值表
- **product_service** - 商品服务表
- **freight_template** - 运费模板表
- **freight_template_region** - 运费模板区域表

#### 设计特点
- 支持多级商品分类
- 灵活的商品规格和属性系统
- 完整的SKU管理
- 运费模板配置

### 3. 库存服务 (wit_inventory)

#### 核心表
- **warehouse** - 仓库表
- **warehouse_area** - 库区表
- **product_stock** - 商品库存表
- **stock_record** - 库存变动记录表
- **stock_in** - 入库单表
- **stock_in_detail** - 入库单明细表
- **stock_out** - 出库单表
- **stock_out_detail** - 出库单明细表
- **stock_transfer** - 调拨单表
- **stock_transfer_detail** - 调拨单明细表
- **stock_check** - 盘点单表
- **stock_check_detail** - 盘点单明细表

#### 设计特点
- 多仓库管理支持
- 完整的出入库流程
- 库存变动全程跟踪
- 支持库存调拨和盘点

### 4. 订单服务 (wit_order)

#### 核心表
- **order_info** - 订单主表
- **order_item** - 订单项表
- **order_address** - 订单收货地址表
- **logistics_company** - 物流公司表
- **order_logistics** - 订单物流表
- **logistics_track** - 物流轨迹表
- **after_sale** - 售后申请表
- **after_sale_log** - 售后日志表
- **order_status_log** - 订单状态日志表

#### 设计特点
- 支持多种订单类型
- 完整的物流跟踪
- 售后服务流程
- 订单状态变更记录

### 5. 支付服务 (wit_payment)

#### 核心表
- **payment_method** - 支付方式表
- **payment_record** - 支付记录表
- **payment_notify** - 支付通知记录表
- **refund_record** - 退款记录表
- **user_account** - 用户账户表
- **account_record** - 账户变动记录表
- **commission_record** - 佣金记录表
- **settlement_order** - 结算单表
- **settlement_detail** - 结算明细表

#### 设计特点
- 多种支付方式支持
- 完整的退款流程
- 用户账户体系
- 佣金结算系统

## 🔗 表关系设计

### 主要外键关系
```sql
-- 用户相关
user.level_id -> user_level.id
user_address.user_id -> user.id
merchant.user_id -> user.id
agent.user_id -> user.id

-- 商品相关
product.category_id -> category.id
product.brand_id -> brand.id
product.merchant_id -> merchant.id
product_sku.product_id -> product.id

-- 订单相关
order_info.user_id -> user.id
order_info.merchant_id -> merchant.id
order_item.order_id -> order_info.id
order_item.product_id -> product.id
order_item.sku_id -> product_sku.id

-- 库存相关
product_stock.product_id -> product.id
product_stock.sku_id -> product_sku.id
product_stock.warehouse_id -> warehouse.id
```

## 📈 索引设计原则

### 1. 主键索引
- 所有表都使用 `BIGINT` 类型的自增主键
- 使用雪花算法生成分布式ID（通过MyBatis Plus配置）

### 2. 唯一索引
- 业务唯一字段：用户名、邮箱、手机号、商品编码、订单号等
- 复合唯一索引：用户SKU购物车、用户角色关联等

### 3. 普通索引
- 外键字段：user_id、product_id、order_id等
- 查询频繁字段：status、create_time、type等
- 复合索引：根据查询场景设计

### 4. 索引命名规范
- 主键：`PRIMARY`
- 唯一索引：`uk_字段名` 或 `uk_表名_字段名`
- 普通索引：`idx_字段名` 或 `idx_表名_字段名`

## 🛡️ 数据安全设计

### 1. 敏感数据处理
- **密码**：使用BCrypt加密存储
- **身份证号**：脱敏显示，只显示前4位和后4位
- **银行卡号**：脱敏显示，只显示前4位和后4位
- **手机号**：脱敏显示，中间4位用*替代

### 2. 逻辑删除
- 所有业务表都支持逻辑删除
- 使用 `deleted` 字段标记删除状态
- MyBatis Plus自动处理逻辑删除

### 3. 乐观锁
- 重要业务表使用 `version` 字段实现乐观锁
- 防止并发更新数据冲突

### 4. 审计字段
- `create_time` - 创建时间
- `update_time` - 更新时间
- `create_by` - 创建人
- `update_by` - 更新人

## 🚀 性能优化策略

### 1. 分库分表
- 按业务领域垂直分库
- 大表可考虑水平分表（如订单表按时间分表）

### 2. 读写分离
- 主库负责写操作
- 从库负责读操作
- 使用ShardingSphere实现

### 3. 缓存策略
- Redis缓存热点数据
- 商品信息、用户信息等缓存
- 设置合理的过期时间

### 4. 查询优化
- 避免全表扫描
- 合理使用索引
- 分页查询优化

## 📝 数据字典

### 通用字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID，自增 |
| create_time | DATETIME | 创建时间，默认当前时间 |
| update_time | DATETIME | 更新时间，自动更新 |
| create_by | BIGINT | 创建人ID |
| update_by | BIGINT | 更新人ID |
| deleted | TINYINT | 逻辑删除：0-未删除，1-已删除 |
| version | INT | 乐观锁版本号 |
| status | TINYINT | 状态字段，具体含义见各表说明 |

### 状态码定义

#### 用户状态 (user.status)
- 1：正常
- 0：禁用
- -1：删除

#### 订单状态 (order_info.order_status)
- 1：待付款
- 2：待发货
- 3：待收货
- 4：待评价
- 5：已完成
- 6：已取消
- 7：已关闭

#### 支付状态 (order_info.pay_status)
- 0：未支付
- 1：已支付
- 2：部分支付
- 3：已退款
- 4：部分退款

## 🔧 初始化说明

### 执行顺序
1. 运行 `init-database.bat` 自动初始化所有数据库
2. 或者手动执行SQL文件：
   - `00_init_all_databases.sql` - 创建数据库和基础表
   - `01_wit_user.sql` - 用户服务表
   - `02_wit_product.sql` - 商品服务表
   - `03_wit_inventory.sql` - 库存服务表
   - `04_wit_order.sql` - 订单服务表
   - `05_wit_payment.sql` - 支付服务表
   - `99_init_data.sql` - 初始化数据

### 默认数据
- 管理员账号：admin / 123456
- 测试用户：testuser / 123456
- 基础角色和权限数据
- 商品分类和品牌数据
- 系统配置数据

## 📚 扩展说明

### 1. 新增微服务数据库
1. 在对应的SQL文件中创建数据库和表结构
2. 更新 `00_init_all_databases.sql` 添加数据库创建语句
3. 更新 `init-database.bat` 添加执行步骤

### 2. 表结构变更
1. 创建数据库迁移脚本
2. 使用Flyway或Liquibase管理版本
3. 更新相应的实体类和Mapper

### 3. 性能监控
1. 使用慢查询日志监控SQL性能
2. 定期分析表结构和索引使用情况
3. 根据业务增长调整分库分表策略

---

📞 **技术支持**：如有数据库设计相关问题，请联系开发团队。

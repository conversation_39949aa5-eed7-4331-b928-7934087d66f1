package com.wit.review;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Review Service 启动类
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.wit.review", "com.wit.common"})
@EnableDiscoveryClient
@EnableDubbo
public class ReviewApplication {

    public static void main(String[] args) {
        SpringApplication.run(ReviewApplication.class, args);
    }
}

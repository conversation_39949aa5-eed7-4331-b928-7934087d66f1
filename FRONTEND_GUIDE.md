# 🎨 Wit Mall 前端开发指南

## 📋 概述

本文档详细介绍 Wit Mall 前端项目的开发规范、最佳实践和常用功能实现。

## 🛠️ 技术栈详解

### 核心技术
- **Vue 3.3+** - 采用 Composition API，提供更好的逻辑复用和类型推导
- **Vite 5.0+** - 极速的构建工具，支持热模块替换(HMR)
- **Element Plus 2.4+** - 基于 Vue 3 的企业级 UI 组件库
- **Vue Router 4.x** - 官方路由管理器，支持动态路由和路由守卫
- **Pinia** - 新一代状态管理库，替代 Vuex

### 开发工具
- **ESLint + Prettier** - 代码质量检查和格式化
- **Sass/SCSS** - CSS 预处理器，支持变量、嵌套、混入等特性
- **unplugin-auto-import** - 自动导入 Vue API 和组件
- **unplugin-vue-components** - 按需自动导入组件

## 🚀 快速开始

### 1. 环境准备
```bash
# 检查 Node.js 版本（需要 16+）
node --version

# 检查 npm 版本
npm --version

# 或者使用 yarn
yarn --version
```

### 2. 安装依赖
```bash
cd wit-mall-web
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm（推荐）
pnpm install
```

### 3. 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000 查看应用

### 4. 项目构建
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 📁 项目结构详解

```
wit-mall-web/
├── public/                     # 静态资源目录
│   ├── favicon.ico            # 网站图标
│   ├── logo.png               # 应用Logo
│   └── manifest.json          # PWA配置文件
├── src/                       # 源代码目录
│   ├── api/                   # API接口层
│   │   ├── auth.js           # 认证相关接口
│   │   ├── request.js        # Axios请求配置
│   │   └── system.js         # 系统相关接口
│   ├── assets/               # 资源文件
│   │   ├── images/           # 图片资源
│   │   └── styles/           # 样式文件
│   │       ├── index.scss    # 全局样式入口
│   │       └── variables.scss # SCSS变量定义
│   ├── components/           # 公共组件
│   │   ├── common/           # 通用组件
│   │   ├── business/         # 业务组件
│   │   └── layout/           # 布局组件
│   ├── layouts/              # 页面布局
│   │   ├── DefaultLayout.vue # 默认布局
│   │   ├── AdminLayout.vue   # 管理后台布局
│   │   └── AuthLayout.vue    # 认证页面布局
│   ├── router/               # 路由配置
│   │   ├── index.js         # 路由主配置
│   │   ├── modules/         # 路由模块
│   │   └── guards.js        # 路由守卫
│   ├── stores/               # 状态管理
│   │   ├── app.js           # 应用全局状态
│   │   ├── user.js          # 用户状态
│   │   └── modules/         # 其他状态模块
│   ├── utils/                # 工具函数
│   │   ├── auth.js          # 认证工具
│   │   ├── format.js        # 格式化工具
│   │   ├── request.js       # 请求工具
│   │   └── validate.js      # 验证工具
│   ├── views/                # 页面组件
│   │   ├── auth/            # 认证相关页面
│   │   ├── dashboard/       # 控制台页面
│   │   ├── product/         # 商品相关页面
│   │   ├── order/           # 订单相关页面
│   │   └── admin/           # 管理后台页面
│   ├── App.vue               # 根组件
│   └── main.js               # 应用入口
├── .env                      # 环境变量（所有环境）
├── .env.development          # 开发环境变量
├── .env.production           # 生产环境变量
├── .eslintrc.cjs            # ESLint配置
├── .prettierrc              # Prettier配置
├── index.html               # HTML模板
├── package.json             # 项目配置
├── vite.config.js           # Vite配置
└── README.md                # 项目说明
```

## 🎯 开发规范

### 1. 命名规范

#### 文件命名
- **组件文件**：使用 PascalCase，如 `UserProfile.vue`
- **页面文件**：使用 PascalCase，如 `ProductList.vue`
- **工具文件**：使用 camelCase，如 `formatUtils.js`
- **样式文件**：使用 kebab-case，如 `user-profile.scss`

#### 变量命名
```javascript
// 常量使用 UPPER_SNAKE_CASE
const API_BASE_URL = '/api'

// 变量和函数使用 camelCase
const userName = 'john'
const getUserInfo = () => {}

// 组件名使用 PascalCase
const UserProfile = defineComponent({})
```

### 2. 组件开发规范

#### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 组件逻辑
export default {
  name: 'ComponentName', // 必须提供组件名
  // ... 其他选项
}
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

#### Composition API 使用
```javascript
import { ref, computed, onMounted } from 'vue'

export default {
  name: 'ExampleComponent',
  
  setup() {
    // 响应式数据
    const count = ref(0)
    
    // 计算属性
    const doubleCount = computed(() => count.value * 2)
    
    // 方法
    const increment = () => {
      count.value++
    }
    
    // 生命周期
    onMounted(() => {
      console.log('组件已挂载')
    })
    
    // 返回模板需要的数据和方法
    return {
      count,
      doubleCount,
      increment
    }
  }
}
```

### 3. API 调用规范

#### API 文件组织
```javascript
// src/api/user.js
import request from './request'

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 用户列表
 */
export const getUserList = (params) => {
  return request({
    url: '/user/list',
    method: 'get',
    params
  })
}

/**
 * 创建用户
 * @param {Object} data - 用户数据
 * @returns {Promise} 创建结果
 */
export const createUser = (data) => {
  return request({
    url: '/user',
    method: 'post',
    data
  })
}
```

#### 在组件中使用 API
```javascript
import { ref, onMounted } from 'vue'
import { getUserList, createUser } from '@/api/user'
import { ElMessage } from 'element-plus'

export default {
  setup() {
    const users = ref([])
    const loading = ref(false)
    
    // 获取用户列表
    const fetchUsers = async () => {
      try {
        loading.value = true
        const response = await getUserList()
        users.value = response.data
      } catch (error) {
        ElMessage.error('获取用户列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 创建用户
    const handleCreateUser = async (userData) => {
      try {
        await createUser(userData)
        ElMessage.success('用户创建成功')
        fetchUsers() // 刷新列表
      } catch (error) {
        ElMessage.error('用户创建失败')
      }
    }
    
    onMounted(() => {
      fetchUsers()
    })
    
    return {
      users,
      loading,
      handleCreateUser
    }
  }
}
```

### 4. 状态管理规范

#### Store 定义
```javascript
// src/stores/user.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token'))
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => user.value?.name || '未知用户')
  
  // 方法
  const login = async (credentials) => {
    // 登录逻辑
  }
  
  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
  }
  
  return {
    // 状态
    user,
    token,
    
    // 计算属性
    isLoggedIn,
    userName,
    
    // 方法
    login,
    logout
  }
})
```

#### 在组件中使用 Store
```javascript
import { useUserStore } from '@/stores/user'

export default {
  setup() {
    const userStore = useUserStore()
    
    // 直接使用 store 的状态和方法
    const handleLogin = async (credentials) => {
      await userStore.login(credentials)
    }
    
    return {
      userStore,
      handleLogin
    }
  }
}
```

### 5. 路由配置规范

#### 路由定义
```javascript
// src/router/modules/user.js
export default [
  {
    path: '/user',
    name: 'User',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true,
      roles: ['admin']
    },
    children: [
      {
        path: '',
        name: 'UserList',
        component: () => import('@/views/user/UserList.vue'),
        meta: {
          title: '用户列表'
        }
      },
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/views/user/UserProfile.vue'),
        meta: {
          title: '个人资料'
        }
      }
    ]
  }
]
```

#### 路由守卫
```javascript
// src/router/guards.js
import { useUserStore } from '@/stores/user'

export const setupRouterGuards = (router) => {
  // 全局前置守卫
  router.beforeEach((to, from, next) => {
    const userStore = useUserStore()
    
    // 检查认证
    if (to.meta.requiresAuth && !userStore.isLoggedIn) {
      next('/login')
      return
    }
    
    // 检查权限
    if (to.meta.roles && !userStore.hasRole(to.meta.roles)) {
      next('/403')
      return
    }
    
    next()
  })
}
```

## 🎨 样式开发规范

### 1. SCSS 变量使用
```scss
// 使用预定义的变量
.user-card {
  padding: $spacing-md;
  border-radius: $border-radius-base;
  background: $background-color-base;
  
  .user-name {
    color: $text-color-primary;
    font-size: $font-size-large;
  }
}
```

### 2. 响应式设计
```scss
.user-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-lg;
  
  // 平板
  @media (max-width: $breakpoint-md) {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-md;
  }
  
  // 手机
  @media (max-width: $breakpoint-sm) {
    grid-template-columns: 1fr;
    gap: $spacing-sm;
  }
}
```

### 3. 组件样式隔离
```vue
<style lang="scss" scoped>
// scoped 样式只影响当前组件
.user-profile {
  .avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
  }
}

// 深度选择器，影响子组件
:deep(.el-button) {
  margin-right: 10px;
}
</style>
```

## 🧪 测试规范

### 1. 单元测试
```javascript
// tests/components/UserProfile.test.js
import { mount } from '@vue/test-utils'
import UserProfile from '@/components/UserProfile.vue'

describe('UserProfile', () => {
  it('renders user name correctly', () => {
    const wrapper = mount(UserProfile, {
      props: {
        user: {
          name: 'John Doe',
          email: '<EMAIL>'
        }
      }
    })
    
    expect(wrapper.text()).toContain('John Doe')
  })
})
```

### 2. E2E 测试
```javascript
// cypress/integration/user.spec.js
describe('User Management', () => {
  it('should create a new user', () => {
    cy.visit('/user')
    cy.get('[data-cy=create-user-btn]').click()
    cy.get('[data-cy=user-name-input]').type('New User')
    cy.get('[data-cy=submit-btn]').click()
    cy.contains('用户创建成功')
  })
})
```

## 🚀 性能优化

### 1. 路由懒加载
```javascript
const routes = [
  {
    path: '/user',
    component: () => import('@/views/user/UserList.vue') // 懒加载
  }
]
```

### 2. 组件懒加载
```vue
<template>
  <div>
    <!-- 使用 Suspense 包装异步组件 -->
    <Suspense>
      <template #default>
        <AsyncComponent />
      </template>
      <template #fallback>
        <div>加载中...</div>
      </template>
    </Suspense>
  </div>
</template>

<script>
import { defineAsyncComponent } from 'vue'

export default {
  components: {
    AsyncComponent: defineAsyncComponent(() => import('./AsyncComponent.vue'))
  }
}
</script>
```

### 3. 图片优化
```vue
<template>
  <!-- 使用 Element Plus 的图片组件，支持懒加载 -->
  <el-image
    :src="imageUrl"
    :lazy="true"
    fit="cover"
    :preview-src-list="[imageUrl]"
  />
</template>
```

## 📱 移动端适配

### 1. 响应式断点
```scss
// 移动端优先的响应式设计
.container {
  padding: $spacing-sm;
  
  @media (min-width: $breakpoint-md) {
    padding: $spacing-lg;
  }
  
  @media (min-width: $breakpoint-lg) {
    padding: $spacing-xl;
  }
}
```

### 2. 触摸友好
```scss
.button {
  min-height: 44px; // 最小触摸目标
  padding: $spacing-sm $spacing-md;
  
  @media (hover: hover) {
    &:hover {
      background-color: $primary-light;
    }
  }
}
```

## 🔧 调试技巧

### 1. Vue DevTools
安装 Vue DevTools 浏览器扩展，可以：
- 查看组件树和状态
- 调试 Pinia store
- 分析性能

### 2. 控制台调试
```javascript
// 在组件中添加调试信息
export default {
  setup() {
    const debugInfo = () => {
      console.log('组件状态:', {
        user: user.value,
        loading: loading.value
      })
    }
    
    // 开发环境下暴露到全局
    if (import.meta.env.DEV) {
      window.debugComponent = debugInfo
    }
    
    return { debugInfo }
  }
}
```

## 📚 常用代码片段

### 1. 表格组件
```vue
<template>
  <div class="table-container">
    <el-table
      :data="tableData"
      :loading="loading"
      stripe
      border
    >
      <el-table-column prop="name" label="姓名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
```

### 2. 表单组件
```vue
<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item label="用户名" prop="username">
      <el-input v-model="form.username" />
    </el-form-item>
    
    <el-form-item label="邮箱" prop="email">
      <el-input v-model="form.email" type="email" />
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="handleSubmit">
        提交
      </el-button>
      <el-button @click="handleReset">
        重置
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  setup() {
    const formRef = ref()
    
    const form = reactive({
      username: '',
      email: ''
    })
    
    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ]
    }
    
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        // 提交表单
      } catch (error) {
        console.log('表单验证失败')
      }
    }
    
    const handleReset = () => {
      formRef.value.resetFields()
    }
    
    return {
      formRef,
      form,
      rules,
      handleSubmit,
      handleReset
    }
  }
}
</script>
```

## 🎉 总结

遵循以上开发规范和最佳实践，可以确保前端项目的代码质量、可维护性和团队协作效率。建议定期回顾和更新这些规范，以适应技术发展和项目需求的变化。

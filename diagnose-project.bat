@echo off
echo ========================================
echo Wit Mall 项目诊断脚本
echo ========================================
echo.

echo 1. 检查 Java 版本...
java -version
echo.

echo 2. 检查 Maven 版本...
mvn -version
echo.

echo 3. 检查项目结构...
echo 根目录 pom.xml:
if exist pom.xml (
    echo ✅ 存在
) else (
    echo ❌ 不存在
)

echo.
echo 检查子模块:

echo 检查 wit-common...
if exist wit-common\pom.xml (echo ✅ wit-common - pom.xml 存在) else (echo ❌ wit-common - pom.xml 不存在)
if exist wit-common\src\main\java (echo   ✅ wit-common - src/main/java 存在) else (echo   ❌ wit-common - src/main/java 不存在)

echo 检查 wit-gateway...
if exist wit-gateway\pom.xml (echo ✅ wit-gateway - pom.xml 存在) else (echo ❌ wit-gateway - pom.xml 不存在)
if exist wit-gateway\src\main\java (echo   ✅ wit-gateway - src/main/java 存在) else (echo   ❌ wit-gateway - src/main/java 不存在)

echo 检查 wit-auth...
if exist wit-auth\pom.xml (echo ✅ wit-auth - pom.xml 存在) else (echo ❌ wit-auth - pom.xml 不存在)
if exist wit-auth\src\main\java (echo   ✅ wit-auth - src/main/java 存在) else (echo   ❌ wit-auth - src/main/java 不存在)

echo 检查 wit-user...
if exist wit-user\pom.xml (echo ✅ wit-user - pom.xml 存在) else (echo ❌ wit-user - pom.xml 不存在)
if exist wit-user\src\main\java (echo   ✅ wit-user - src/main/java 存在) else (echo   ❌ wit-user - src/main/java 不存在)

echo 检查 wit-product...
if exist wit-product\pom.xml (echo ✅ wit-product - pom.xml 存在) else (echo ❌ wit-product - pom.xml 不存在)
if exist wit-product\src\main\java (echo   ✅ wit-product - src/main/java 存在) else (echo   ❌ wit-product - src/main/java 不存在)

echo 检查 wit-order...
if exist wit-order\pom.xml (echo ✅ wit-order - pom.xml 存在) else (echo ❌ wit-order - pom.xml 不存在)
if exist wit-order\src\main\java (echo   ✅ wit-order - src/main/java 存在) else (echo   ❌ wit-order - src/main/java 不存在)

echo.
echo 4. 检查 Maven 依赖...
echo 正在检查依赖解析...
mvn dependency:resolve -q
if %errorlevel% equ 0 (
    echo ✅ Maven 依赖解析成功
) else (
    echo ❌ Maven 依赖解析失败
)

echo.
echo 5. 检查编译状态...
echo 正在编译项目...
mvn compile -q
if %errorlevel% equ 0 (
    echo ✅ 项目编译成功
) else (
    echo ❌ 项目编译失败
)

echo.
echo 6. 生成 IDE 配置文件...
echo 正在生成 IntelliJ IDEA 配置...
mvn idea:idea -q
if %errorlevel% equ 0 (
    echo ✅ IDEA 配置生成成功
) else (
    echo ❌ IDEA 配置生成失败
)

echo.
echo 正在生成 Eclipse 配置...
mvn eclipse:eclipse -q
if %errorlevel% equ 0 (
    echo ✅ Eclipse 配置生成成功
) else (
    echo ❌ Eclipse 配置生成失败
)

echo.
echo ========================================
echo 诊断完成！
echo ========================================
echo.
echo 建议操作:
echo 1. 如果看到编译错误，请先运行: mvn clean install
echo 2. 如果使用 IntelliJ IDEA，请重新导入项目
echo 3. 如果使用 Eclipse，请刷新项目并重新导入
echo.
pause

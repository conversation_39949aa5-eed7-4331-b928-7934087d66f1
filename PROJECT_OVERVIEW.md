# Wit Mall 微服务商城项目 - 框架总览

## 🎯 项目概述

这是一个完整的微服务商城学习项目，基于 Spring Cloud Alibaba 生态构建，包含了18个微服务模块和完整的基础设施支撑。项目采用了当前最主流的微服务技术栈，是学习微服务架构的绝佳模板。

## 📁 项目结构

```
wit-mall/
├── wit-common/                 # 公共模块 - 通用工具类、实体类、异常处理
├── wit-gateway/               # 网关服务 - API网关，统一入口 (8080)
├── wit-auth/                  # 认证授权服务 - OAuth2/JWT (8081)
├── wit-user/                  # 用户服务 - 用户管理 (8082)
├── wit-product/               # 商品服务 - 商品管理 (8083)
├── wit-order/                 # 订单服务 - 订单管理 (8084)
├── wit-cart/                  # 购物车服务 - 购物车功能 (8085)
├── wit-payment/               # 支付服务 - 支付处理 (8086)
├── wit-inventory/             # 库存服务 - 库存管理 (8087)
├── wit-marketing/             # 营销服务 - 营销活动 (8088)
├── wit-search/                # 搜索服务 - 商品搜索 (8089)
├── wit-recommendation/        # 推荐服务 - 商品推荐 (8090)
├── wit-review/                # 评价服务 - 商品评价 (8091)
├── wit-notification/          # 消息通知服务 - 消息推送 (8092)
├── wit-file/                  # 文件服务 - 文件上传下载 (8093)
├── wit-system/                # 系统管理服务 - 系统配置 (8094)
├── wit-schedule/              # 定时任务服务 - 定时任务处理 (8095)
├── wit-analytics/             # 统计报表服务 - 数据统计 (8096)
├── wit-aftersales/            # 售后服务 - 售后处理 (8097)
├── config/                    # 配置文件模板
├── docker/                    # Docker配置
├── docker-compose.yml         # 基础设施编排
├── start-all.bat             # 项目启动脚本(一键启动所有服务)
├── stop-all.bat              # 项目停止脚本(停止所有服务)
└── generate-services.ps1     # 服务生成脚本(生成微服务模块,用于创建服务模块)
```

## 🛠️ 技术栈详情

### 核心框架
- **Spring Boot** 3.2.1 - 微服务基础框架
- **Spring Cloud** 2023.0.0 - 微服务生态
- **Spring Cloud Alibaba** 2022.0.0.0 - 阿里微服务组件

### 服务治理
- **Nacos** - 服务注册发现 + 配置管理
- **Spring Cloud Gateway** - API网关
- **Dubbo** 3.2.8 - 高性能RPC框架
- **Sentinel** - 流量控制、熔断降级
- **Seata** - 分布式事务解决方案

### 数据存储
- **MySQL** 8.0 - 主数据库
- **MyBatis Plus** 3.5.5 - ORM框架
- **Druid** 1.2.20 - 数据库连接池
- **Redis** - 缓存 + 分布式锁
- **Elasticsearch** - 全文搜索

### 消息中间件
- **RabbitMQ** - 异步消息处理

### 对象存储
- **MinIO** - 分布式对象存储

### 任务调度
- **XXL-Job** - 分布式定时任务

### 安全认证
- **Spring Security** - 安全框架
- **Spring Authorization Server** - OAuth2授权服务器
- **JWT** - 无状态Token认证

### 开发工具
- **Knife4j** - API文档生成
- **Hutool** - Java工具类库
- **FastJSON2** - JSON处理
- **Lombok** - 代码简化

### 容器化
- **Docker** - 容器化部署
- **Docker Compose** - 本地开发环境
- **Nginx** - 反向代理

## 🚀 快速启动

### 1. 环境准备
- JDK 17+
- Maven 3.6+
- Docker & Docker Compose

### 2. 启动基础设施
```bash
docker-compose up -d
```

### 3. 启动微服务
```bash
# Windows
start-all.bat

# Linux/Mac
./start-all.sh
```

## 🌐 服务访问地址

| 服务名称 | 访问地址 | 用户名/密码 | 说明 |
|---------|---------|-------------|------|
| API网关 | http://localhost:8080 | - | 统一API入口 |
| Nacos控制台 | http://localhost:8848/nacos | nacos/nacos | 服务注册与配置 |
| Sentinel控制台 | http://localhost:8858 | sentinel/sentinel | 流量控制 |
| RabbitMQ控制台 | http://localhost:15672 | admin/admin123 | 消息队列 |
| MinIO控制台 | http://localhost:9001 | minioadmin/minioadmin | 对象存储 |
| Elasticsearch | http://localhost:9200 | - | 搜索引擎 |
| Kibana | http://localhost:5601 | - | 日志分析 |
| XXL-Job控制台 | http://localhost:8080/xxl-job-admin | admin/123456 | 定时任务 |

## 📋 已实现功能

### 基础设施
- ✅ 多模块Maven项目结构
- ✅ 统一依赖管理
- ✅ 公共模块封装
- ✅ 统一异常处理
- ✅ 统一返回格式
- ✅ MyBatis Plus配置
- ✅ Redis配置
- ✅ Docker环境

### 网关服务
- ✅ 路由配置
- ✅ 服务发现集成
- ✅ 负载均衡

### 认证授权
- ✅ Spring Security配置
- ✅ OAuth2授权服务器
- ✅ JWT Token支持

### 文件服务
- ✅ MinIO集成配置

### 定时任务
- ✅ XXL-Job集成配置

### 配置管理
- ✅ Nacos配置模板
- ✅ 环境配置分离

## 🎯 学习目标

通过这个项目，您可以学习到：

1. **微服务架构设计**
   - 服务拆分原则
   - 服务间通信
   - 数据一致性

2. **Spring Cloud Alibaba生态**
   - Nacos服务治理
   - Sentinel流量控制
   - Seata分布式事务

3. **分布式系统核心概念**
   - 服务注册发现
   - 配置管理
   - 负载均衡
   - 熔断降级

4. **中间件集成**
   - 消息队列使用
   - 缓存设计
   - 搜索引擎
   - 对象存储

5. **DevOps实践**
   - 容器化部署
   - 监控告警
   - 日志收集

## 📝 下一步计划

1. **业务功能开发**
   - 用户管理功能
   - 商品管理功能
   - 订单流程
   - 支付集成

2. **高级特性**
   - 分布式事务
   - 服务限流
   - 链路追踪
   - 监控告警

3. **前端集成**
   - Vue3前端项目
   - 管理后台
   - 移动端适配

4. **部署优化**
   - Kubernetes部署
   - CI/CD流水线
   - 生产环境配置

## 💡 使用建议

1. **学习顺序**
   - 先理解整体架构
   - 逐个服务深入学习
   - 实践业务功能开发

2. **开发实践**
   - 保持代码规范
   - 编写单元测试
   - 完善API文档

3. **问题排查**
   - 查看服务日志
   - 使用监控工具
   - 理解调用链路

这个项目是一个完整的微服务学习模板，您可以基于此框架进行业务功能的开发和扩展！

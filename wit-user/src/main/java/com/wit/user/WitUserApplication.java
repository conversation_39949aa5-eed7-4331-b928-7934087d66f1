package com.wit.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 用户服务启动类
 * 
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.wit.user.mapper")
public class WitUserApplication {

    public static void main(String[] args) {
        SpringApplication.run(WitUserApplication.class, args);
        System.out.println("🚀 Wit Mall User Service 启动成功！");
        System.out.println("📖 服务端口: 8082");
    }
}

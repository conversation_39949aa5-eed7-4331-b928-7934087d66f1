server:
  port: 8082

spring:
  application:
    name: wit-user
  
# 注意：用户服务的具体配置（数据库、Redis、安全等）现在都在 Nacos 中管理
# 本地只保留最基本的配置，其他配置从 Nacos 动态加载
  
  cloud:
    nacos:
      discovery:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d  # wit 命名空间ID
        group: BUSINESS_GROUP
        cluster-name: wit-cluster
        metadata:
          version: 1.0.0
          zone: prod
          service-type: business
      config:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: BUSINESS_GROUP
        file-extension: yml
        refresh-enabled: true
        # 引用用户服务的配置文件
        extension-configs:
          - data-id: wit-user-core.yml
            group: BUSINESS_GROUP
            refresh: true
          - data-id: wit-user-database.yml
            group: BUSINESS_GROUP
            refresh: true
          - data-id: wit-user-redis.yml
            group: BUSINESS_GROUP
            refresh: true
        # 引用公共配置
        shared-configs:
          - data-id: common-logging.yml
            group: COMMON_GROUP
            refresh: true

# MyBatis Plus配置
# 基本监控配置（详细配置在 Nacos 中）
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# 基本日志配置（详细配置在 Nacos 中）
logging:
  level:
    root: INFO
    com.wit: INFO

@echo off
echo ========================================
echo    Wit Mall 项目结构验证
echo ========================================

echo.
echo 正在验证项目结构...

echo.
echo 1. 检查父项目配置...
if exist "pom.xml" (
    echo ✓ 父项目 pom.xml 存在
) else (
    echo ✗ 父项目 pom.xml 不存在
)

echo.
echo 2. 检查公共模块...
if exist "wit-common\pom.xml" (
    echo ✓ wit-common 模块存在
) else (
    echo ✗ wit-common 模块不存在
)

echo.
echo 3. 检查核心服务模块...
set services=wit-gateway wit-auth wit-user wit-product wit-order wit-cart wit-payment wit-inventory wit-marketing wit-search wit-recommendation wit-review wit-notification wit-file wit-system wit-schedule wit-analytics wit-aftersales

for %%s in (%services%) do (
    if exist "%%s\pom.xml" (
        echo ✓ %%s 模块存在
    ) else (
        echo ✗ %%s 模块不存在
    )
)

echo.
echo 4. 检查配置文件...
if exist "docker-compose.yml" (
    echo ✓ Docker Compose 配置存在
) else (
    echo ✗ Docker Compose 配置不存在
)

if exist "config\nacos" (
    echo ✓ Nacos 配置模板存在
) else (
    echo ✗ Nacos 配置模板不存在
)

if exist "docker\nginx" (
    echo ✓ Nginx 配置存在
) else (
    echo ✗ Nginx 配置不存在
)

echo.
echo 5. 检查启动脚本...
if exist "start-all.bat" (
    echo ✓ 启动脚本存在
) else (
    echo ✗ 启动脚本不存在
)

if exist "stop-all.bat" (
    echo ✓ 停止脚本存在
) else (
    echo ✗ 停止脚本不存在
)

echo.
echo 6. 检查文档...
if exist "README.md" (
    echo ✓ README 文档存在
) else (
    echo ✗ README 文档不存在
)

if exist "PROJECT_OVERVIEW.md" (
    echo ✓ 项目总览文档存在
) else (
    echo ✗ 项目总览文档不存在
)

echo.
echo ========================================
echo 项目结构验证完成！
echo ========================================
echo.
echo 项目包含：
echo - 1个公共模块 (wit-common)
echo - 1个网关服务 (wit-gateway)
echo - 17个业务服务模块
echo - 完整的Docker环境配置
echo - Nacos配置模板
echo - 启动停止脚本
echo - 详细的项目文档
echo.
echo 下一步：
echo 1. 运行 'docker-compose up -d' 启动基础设施
echo 2. 运行 'start-all.bat' 启动所有服务
echo 3. 访问 http://localhost:8080 查看网关
echo 4. 访问 http://localhost:8848/nacos 查看Nacos控制台
echo.
pause

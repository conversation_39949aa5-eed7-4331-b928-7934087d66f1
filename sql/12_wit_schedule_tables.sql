-- =============================================
-- WitMall v2.0 定时任务服务表结构
-- 数据库: wit_schedule
-- 表数量: 3张
-- 功能: 定时任务管理、执行日志、任务锁
-- =============================================

USE `wit_schedule`;

-- =============================================
-- 1. 定时任务表
-- =============================================
CREATE TABLE IF NOT EXISTS `schedule_job` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `job_name` VARCHAR(100) NOT NULL COMMENT '任务名称',
    `job_group` VARCHAR(50) DEFAULT 'DEFAULT' COMMENT '任务分组',
    `job_class` VARCHAR(255) NOT NULL COMMENT '任务类名',
    `job_method` VARCHAR(100) COMMENT '任务方法名',
    `job_params` TEXT COMMENT '任务参数',
    `cron_expression` VARCHAR(100) NOT NULL COMMENT 'Cron表达式',
    `misfire_policy` TINYINT DEFAULT 1 COMMENT '计划执行错误策略：1-立即执行，2-执行一次，3-放弃执行',
    `concurrent` TINYINT DEFAULT 0 COMMENT '是否并发执行：1-允许，0-禁止',
    `description` VARCHAR(500) COMMENT '任务描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-暂停',
    `next_run_time` DATETIME COMMENT '下次执行时间',
    `last_run_time` DATETIME COMMENT '上次执行时间',
    `run_count` INT DEFAULT 0 COMMENT '执行次数',
    `success_count` INT DEFAULT 0 COMMENT '成功次数',
    `fail_count` INT DEFAULT 0 COMMENT '失败次数',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_job_name_group` (`job_name`, `job_group`),
    KEY `idx_job_group` (`job_group`),
    KEY `idx_status` (`status`),
    KEY `idx_next_run_time` (`next_run_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时任务表';

-- =============================================
-- 2. 任务执行日志表
-- =============================================
CREATE TABLE IF NOT EXISTS `schedule_job_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `job_id` BIGINT NOT NULL COMMENT '任务ID',
    `job_name` VARCHAR(100) NOT NULL COMMENT '任务名称',
    `job_group` VARCHAR(50) NOT NULL COMMENT '任务分组',
    `job_class` VARCHAR(255) NOT NULL COMMENT '任务类名',
    `job_method` VARCHAR(100) COMMENT '任务方法名',
    `job_params` TEXT COMMENT '任务参数',
    `cron_expression` VARCHAR(100) COMMENT 'Cron表达式',
    `start_time` DATETIME NOT NULL COMMENT '开始时间',
    `end_time` DATETIME COMMENT '结束时间',
    `execution_time` INT COMMENT '执行时长（毫秒）',
    `status` TINYINT NOT NULL COMMENT '执行状态：1-成功，0-失败',
    `result_message` TEXT COMMENT '执行结果信息',
    `error_message` TEXT COMMENT '错误信息',
    `server_ip` VARCHAR(50) COMMENT '执行服务器IP',
    `server_name` VARCHAR(100) COMMENT '执行服务器名称',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_job_name` (`job_name`),
    KEY `idx_job_group` (`job_group`),
    KEY `idx_status` (`status`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务执行日志表';

-- =============================================
-- 3. 任务锁表
-- =============================================
CREATE TABLE IF NOT EXISTS `schedule_job_lock` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `lock_key` VARCHAR(100) NOT NULL COMMENT '锁键',
    `lock_value` VARCHAR(100) NOT NULL COMMENT '锁值',
    `job_id` BIGINT NOT NULL COMMENT '任务ID',
    `job_name` VARCHAR(100) NOT NULL COMMENT '任务名称',
    `server_ip` VARCHAR(50) NOT NULL COMMENT '服务器IP',
    `server_name` VARCHAR(100) COMMENT '服务器名称',
    `thread_id` VARCHAR(100) COMMENT '线程ID',
    `lock_time` DATETIME NOT NULL COMMENT '加锁时间',
    `expire_time` DATETIME NOT NULL COMMENT '过期时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-已锁定，0-已释放',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_lock_key` (`lock_key`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_server_ip` (`server_ip`),
    KEY `idx_expire_time` (`expire_time`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务锁表';

-- 定时任务服务表结构创建完成 - 共3张表

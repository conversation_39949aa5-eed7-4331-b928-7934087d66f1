-- =============================================
-- WitMall v2.0 商品服务表结构
-- 数据库: wit_product
-- 表数量: 15张
-- 功能: 商品管理、分类管理、品牌管理、规格管理
-- =============================================

USE `wit_product`;

-- =============================================
-- 1. 商品分类表
-- =============================================
CREATE TABLE IF NOT EXISTS `category` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `category_name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    `category_code` VARCHAR(50) NOT NULL COMMENT '分类编码',
    `parent_id` BIGINT DEFAULT 0 COMMENT '父分类ID，0表示顶级分类',
    `level` TINYINT DEFAULT 1 COMMENT '分类层级',
    `path` VARCHAR(500) COMMENT '分类路径，用逗号分隔',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `icon` VARCHAR(255) COMMENT '分类图标',
    `image` VARCHAR(255) COMMENT '分类图片',
    `banner` VARCHAR(255) COMMENT '分类横幅',
    `description` VARCHAR(500) COMMENT '分类描述',
    `keywords` VARCHAR(200) COMMENT '关键词',
    `is_show` TINYINT DEFAULT 1 COMMENT '是否显示：1-显示，0-隐藏',
    `is_nav` TINYINT DEFAULT 0 COMMENT '是否导航：1-是，0-否',
    `commission_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '佣金比例',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_category_code` (`category_code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_level` (`level`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_is_show` (`is_show`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- =============================================
-- 2. 分类属性表
-- =============================================
CREATE TABLE IF NOT EXISTS `category_attribute` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `category_id` BIGINT NOT NULL COMMENT '分类ID',
    `attribute_name` VARCHAR(100) NOT NULL COMMENT '属性名称',
    `attribute_type` TINYINT DEFAULT 1 COMMENT '属性类型：1-规格，2-参数',
    `input_type` TINYINT DEFAULT 1 COMMENT '录入类型：1-手工录入，2-从列表中选择，3-多选',
    `attribute_values` TEXT COMMENT '可选值列表，用逗号分隔',
    `is_required` TINYINT DEFAULT 0 COMMENT '是否必填：1-是，0-否',
    `is_search` TINYINT DEFAULT 0 COMMENT '是否用于搜索：1-是，0-否',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_attribute_type` (`attribute_type`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类属性表';

-- =============================================
-- 3. 品牌表
-- =============================================
CREATE TABLE IF NOT EXISTS `brand` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `brand_name` VARCHAR(100) NOT NULL COMMENT '品牌名称',
    `brand_code` VARCHAR(50) NOT NULL COMMENT '品牌编码',
    `brand_name_en` VARCHAR(100) COMMENT '品牌英文名',
    `logo` VARCHAR(255) COMMENT '品牌Logo',
    `description` TEXT COMMENT '品牌描述',
    `website` VARCHAR(200) COMMENT '官方网站',
    `country` VARCHAR(50) COMMENT '品牌国家',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `is_hot` TINYINT DEFAULT 0 COMMENT '是否热门：1-是，0-否',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_brand_code` (`brand_code`),
    KEY `idx_brand_name` (`brand_name`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_is_hot` (`is_hot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='品牌表';

-- =============================================
-- 4. 分类品牌关联表
-- =============================================
CREATE TABLE IF NOT EXISTS `category_brand` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `category_id` BIGINT NOT NULL COMMENT '分类ID',
    `brand_id` BIGINT NOT NULL COMMENT '品牌ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_category_brand` (`category_id`, `brand_id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_brand_id` (`brand_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类品牌关联表';

-- =============================================
-- 5. 商品表
-- =============================================
CREATE TABLE IF NOT EXISTS `product` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `product_code` VARCHAR(100) NOT NULL COMMENT '商品编码',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `shop_id` BIGINT NOT NULL COMMENT '店铺ID',
    `category_id` BIGINT NOT NULL COMMENT '分类ID',
    `brand_id` BIGINT COMMENT '品牌ID',
    `product_type` TINYINT DEFAULT 1 COMMENT '商品类型：1-实物商品，2-虚拟商品，3-服务商品',
    `price` DECIMAL(10,2) NOT NULL COMMENT '销售价格',
    `original_price` DECIMAL(10,2) COMMENT '原价',
    `cost_price` DECIMAL(10,2) COMMENT '成本价',
    `market_price` DECIMAL(10,2) COMMENT '市场价',
    `weight` DECIMAL(8,2) DEFAULT 0.00 COMMENT '重量(kg)',
    `volume` DECIMAL(8,2) DEFAULT 0.00 COMMENT '体积(立方米)',
    `unit` VARCHAR(20) DEFAULT '件' COMMENT '单位',
    `min_buy` INT DEFAULT 1 COMMENT '最小购买数量',
    `max_buy` INT DEFAULT 0 COMMENT '最大购买数量，0表示不限制',
    `stock_warning` INT DEFAULT 10 COMMENT '库存预警数量',
    `main_image` VARCHAR(255) COMMENT '主图',
    `images` JSON COMMENT '商品图片JSON数组',
    `video` VARCHAR(255) COMMENT '商品视频',
    `detail` LONGTEXT COMMENT '商品详情',
    `keywords` VARCHAR(200) COMMENT '关键词',
    `tags` VARCHAR(200) COMMENT '标签',
    `seo_title` VARCHAR(200) COMMENT 'SEO标题',
    `seo_keywords` VARCHAR(200) COMMENT 'SEO关键词',
    `seo_description` VARCHAR(500) COMMENT 'SEO描述',
    `is_virtual` TINYINT DEFAULT 0 COMMENT '是否虚拟商品：1-是，0-否',
    `is_hot` TINYINT DEFAULT 0 COMMENT '是否热门：1-是，0-否',
    `is_new` TINYINT DEFAULT 0 COMMENT '是否新品：1-是，0-否',
    `is_recommend` TINYINT DEFAULT 0 COMMENT '是否推荐：1-是，0-否',
    `is_gift` TINYINT DEFAULT 0 COMMENT '是否赠品：1-是，0-否',
    `gift_point` INT DEFAULT 0 COMMENT '赠送积分',
    `service_ids` VARCHAR(200) COMMENT '服务ID列表，逗号分隔',
    `freight_template_id` BIGINT COMMENT '运费模板ID',
    `status` TINYINT DEFAULT 0 COMMENT '状态：0-待审核，1-上架，2-下架，-1-审核拒绝',
    `audit_time` DATETIME COMMENT '审核时间',
    `audit_by` BIGINT COMMENT '审核人',
    `audit_remark` VARCHAR(500) COMMENT '审核备注',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product_code` (`product_code`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_brand_id` (`brand_id`),
    KEY `idx_status` (`status`),
    KEY `idx_price` (`price`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_is_hot` (`is_hot`),
    KEY `idx_is_new` (`is_new`),
    KEY `idx_is_recommend` (`is_recommend`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- =============================================
-- 6. 商品规格表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_spec` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `spec_name` VARCHAR(100) NOT NULL COMMENT '规格名称',
    `spec_values` TEXT NOT NULL COMMENT '规格值列表，JSON格式',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品规格表';

-- =============================================
-- 7. 商品规格值表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_spec_value` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `spec_id` BIGINT NOT NULL COMMENT '规格ID',
    `spec_value` VARCHAR(100) NOT NULL COMMENT '规格值',
    `spec_image` VARCHAR(255) COMMENT '规格图片',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `idx_spec_id` (`spec_id`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品规格值表';

-- =============================================
-- 8. 商品SKU表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_sku` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_code` VARCHAR(100) NOT NULL COMMENT 'SKU编码',
    `sku_name` VARCHAR(200) COMMENT 'SKU名称',
    `spec_values` JSON COMMENT '规格值JSON对象',
    `price` DECIMAL(10,2) NOT NULL COMMENT '价格',
    `original_price` DECIMAL(10,2) COMMENT '原价',
    `cost_price` DECIMAL(10,2) COMMENT '成本价',
    `weight` DECIMAL(8,2) DEFAULT 0.00 COMMENT '重量(kg)',
    `volume` DECIMAL(8,2) DEFAULT 0.00 COMMENT '体积(立方米)',
    `image` VARCHAR(255) COMMENT 'SKU图片',
    `barcode` VARCHAR(50) COMMENT '条形码',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_sku_code` (`sku_code`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_status` (`status`),
    KEY `idx_price` (`price`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品SKU表';

-- =============================================
-- 9. 商品属性值表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_attribute_value` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `attribute_id` BIGINT NOT NULL COMMENT '属性ID',
    `attribute_value` VARCHAR(500) NOT NULL COMMENT '属性值',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product_attribute` (`product_id`, `attribute_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_attribute_id` (`attribute_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品属性值表';

-- =============================================
-- 10. 商品服务表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_service` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `service_name` VARCHAR(100) NOT NULL COMMENT '服务名称',
    `service_code` VARCHAR(50) NOT NULL COMMENT '服务编码',
    `service_icon` VARCHAR(255) COMMENT '服务图标',
    `description` VARCHAR(500) COMMENT '服务描述',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_service_code` (`service_code`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品服务表';

-- =============================================
-- 11. 商品服务关联表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_service_relation` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `service_id` BIGINT NOT NULL COMMENT '服务ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product_service` (`product_id`, `service_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_service_id` (`service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品服务关联表';

-- =============================================
-- 12. 运费模板表
-- =============================================
CREATE TABLE IF NOT EXISTS `freight_template` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `charge_type` TINYINT DEFAULT 1 COMMENT '计费方式：1-按件数，2-按重量，3-按体积',
    `first_amount` DECIMAL(8,2) DEFAULT 1.00 COMMENT '首件数量/首重/首体积',
    `first_fee` DECIMAL(8,2) DEFAULT 0.00 COMMENT '首费',
    `continue_amount` DECIMAL(8,2) DEFAULT 1.00 COMMENT '续件数量/续重/续体积',
    `continue_fee` DECIMAL(8,2) DEFAULT 0.00 COMMENT '续费',
    `free_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '包邮金额，0表示不包邮',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运费模板表';

-- =============================================
-- 13. 运费模板区域表
-- =============================================
CREATE TABLE IF NOT EXISTS `freight_template_region` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `template_id` BIGINT NOT NULL COMMENT '模板ID',
    `region_name` VARCHAR(200) NOT NULL COMMENT '区域名称',
    `province_codes` TEXT NOT NULL COMMENT '省份编码列表，逗号分隔',
    `first_amount` DECIMAL(8,2) DEFAULT 1.00 COMMENT '首件数量/首重/首体积',
    `first_fee` DECIMAL(8,2) DEFAULT 0.00 COMMENT '首费',
    `continue_amount` DECIMAL(8,2) DEFAULT 1.00 COMMENT '续件数量/续重/续体积',
    `continue_fee` DECIMAL(8,2) DEFAULT 0.00 COMMENT '续费',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `idx_template_id` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运费模板区域表';

-- =============================================
-- 14. 商品统计表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_statistics` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `view_count` INT DEFAULT 0 COMMENT '浏览次数',
    `collect_count` INT DEFAULT 0 COMMENT '收藏次数',
    `cart_count` INT DEFAULT 0 COMMENT '加购次数',
    `order_count` INT DEFAULT 0 COMMENT '下单次数',
    `sale_count` INT DEFAULT 0 COMMENT '销售数量',
    `comment_count` INT DEFAULT 0 COMMENT '评论数量',
    `good_comment_count` INT DEFAULT 0 COMMENT '好评数量',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品统计表';

-- =============================================
-- 15. 商品收藏表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_collect` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_product` (`user_id`, `product_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品收藏表';

-- 商品服务表结构创建完成 - 共15张表

-- =============================================
-- WitMall v2.0 通知服务表结构
-- 数据库: wit_notification
-- 表数量: 3张
-- 功能: 消息通知、模板管理
-- =============================================

USE `wit_notification`;

-- =============================================
-- 1. 消息模板表
-- =============================================
CREATE TABLE IF NOT EXISTS `message_template` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `template_code` VARCHAR(50) NOT NULL COMMENT '模板编码',
    `template_type` TINYINT NOT NULL COMMENT '模板类型：1-站内信，2-短信，3-邮件，4-推送',
    `title` VARCHAR(200) COMMENT '消息标题',
    `content` TEXT NOT NULL COMMENT '消息内容模板',
    `variables` JSON COMMENT '变量列表JSON',
    `third_party_template_id` VARCHAR(100) COMMENT '第三方模板ID',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_template_code` (`template_code`),
    KEY `idx_template_type` (`template_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息模板表';

-- =============================================
-- 2. 消息记录表
-- =============================================
CREATE TABLE IF NOT EXISTS `message_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `message_no` VARCHAR(50) NOT NULL COMMENT '消息编号',
    `template_id` BIGINT COMMENT '模板ID',
    `template_code` VARCHAR(50) COMMENT '模板编码',
    `message_type` TINYINT NOT NULL COMMENT '消息类型：1-站内信，2-短信，3-邮件，4-推送',
    `receiver_type` TINYINT NOT NULL COMMENT '接收者类型：1-用户，2-商家，3-管理员',
    `receiver_id` BIGINT NOT NULL COMMENT '接收者ID',
    `receiver_info` VARCHAR(200) NOT NULL COMMENT '接收者信息（手机号、邮箱等）',
    `title` VARCHAR(200) COMMENT '消息标题',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `send_status` TINYINT DEFAULT 1 COMMENT '发送状态：1-待发送，2-发送成功，3-发送失败',
    `read_status` TINYINT DEFAULT 0 COMMENT '阅读状态：0-未读，1-已读',
    `send_time` DATETIME COMMENT '发送时间',
    `read_time` DATETIME COMMENT '阅读时间',
    `third_party_id` VARCHAR(100) COMMENT '第三方消息ID',
    `error_message` VARCHAR(500) COMMENT '错误信息',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_message_no` (`message_no`),
    KEY `idx_template_id` (`template_id`),
    KEY `idx_message_type` (`message_type`),
    KEY `idx_receiver_id` (`receiver_id`),
    KEY `idx_send_status` (`send_status`),
    KEY `idx_read_status` (`read_status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息记录表';

-- =============================================
-- 3. 短信记录表
-- =============================================
CREATE TABLE IF NOT EXISTS `sms_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
    `sms_type` TINYINT NOT NULL COMMENT '短信类型：1-验证码，2-通知，3-营销',
    `template_code` VARCHAR(50) COMMENT '模板编码',
    `content` VARCHAR(500) NOT NULL COMMENT '短信内容',
    `send_status` TINYINT DEFAULT 1 COMMENT '发送状态：1-待发送，2-发送成功，3-发送失败',
    `third_party_id` VARCHAR(100) COMMENT '第三方消息ID',
    `error_message` VARCHAR(500) COMMENT '错误信息',
    `send_time` DATETIME COMMENT '发送时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_phone` (`phone`),
    KEY `idx_sms_type` (`sms_type`),
    KEY `idx_send_status` (`send_status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信记录表';

-- 通知服务表结构创建完成 - 共3张表

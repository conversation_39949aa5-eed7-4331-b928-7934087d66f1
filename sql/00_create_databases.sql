-- =============================================
-- WitMall v2.0 数据库创建脚本
-- 创建所有微服务数据库
-- MySQL 8.0 兼容版本
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- =============================================
-- 创建所有微服务数据库
-- =============================================

-- 用户服务数据库
CREATE DATABASE IF NOT EXISTS `wit_user` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 商品服务数据库
CREATE DATABASE IF NOT EXISTS `wit_product` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 库存服务数据库
CREATE DATABASE IF NOT EXISTS `wit_inventory` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 订单服务数据库
CREATE DATABASE IF NOT EXISTS `wit_order` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 支付服务数据库
CREATE DATABASE IF NOT EXISTS `wit_payment` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 购物车服务数据库
CREATE DATABASE IF NOT EXISTS `wit_cart` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 营销服务数据库
CREATE DATABASE IF NOT EXISTS `wit_marketing` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 搜索服务数据库
CREATE DATABASE IF NOT EXISTS `wit_search` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 推荐服务数据库
CREATE DATABASE IF NOT EXISTS `wit_recommendation` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 评价服务数据库
CREATE DATABASE IF NOT EXISTS `wit_review` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 通知服务数据库
CREATE DATABASE IF NOT EXISTS `wit_notification` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 文件服务数据库
CREATE DATABASE IF NOT EXISTS `wit_file` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 系统服务数据库
CREATE DATABASE IF NOT EXISTS `wit_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 定时任务服务数据库
CREATE DATABASE IF NOT EXISTS `wit_schedule` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 数据分析服务数据库
CREATE DATABASE IF NOT EXISTS `wit_analytics` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 售后服务数据库
CREATE DATABASE IF NOT EXISTS `wit_aftersales` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Nacos配置数据库
CREATE DATABASE IF NOT EXISTS `nacos_config` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建结果
SELECT '🎉 所有数据库创建完成！' as message;
SELECT '📊 已创建17个微服务数据库' as info;

-- =============================================
-- WitMall v2.0 营销服务表结构
-- 数据库: wit_marketing
-- 表数量: 8张
-- 功能: 优惠券、营销活动、秒杀、拼团
-- =============================================

USE `wit_marketing`;

-- =============================================
-- 1. 优惠券表
-- =============================================
CREATE TABLE IF NOT EXISTS `coupon` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `coupon_name` VARCHAR(100) NOT NULL COMMENT '优惠券名称',
    `coupon_code` VARCHAR(50) NOT NULL COMMENT '优惠券编码',
    `coupon_type` TINYINT NOT NULL COMMENT '优惠券类型：1-满减券，2-折扣券，3-免邮券',
    `discount_type` TINYINT NOT NULL COMMENT '优惠类型：1-固定金额，2-百分比折扣',
    `discount_value` DECIMAL(10,2) NOT NULL COMMENT '优惠值',
    `min_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '最小使用金额',
    `max_discount` DECIMAL(10,2) COMMENT '最大优惠金额（折扣券用）',
    `total_quantity` INT NOT NULL COMMENT '发行总量',
    `used_quantity` INT DEFAULT 0 COMMENT '已使用数量',
    `per_limit` INT DEFAULT 1 COMMENT '每人限领数量',
    `use_scope` TINYINT DEFAULT 1 COMMENT '使用范围：1-全场通用，2-指定分类，3-指定商品',
    `scope_ids` TEXT COMMENT '范围ID列表，逗号分隔',
    `start_time` DATETIME NOT NULL COMMENT '开始时间',
    `end_time` DATETIME NOT NULL COMMENT '结束时间',
    `receive_start_time` DATETIME COMMENT '领取开始时间',
    `receive_end_time` DATETIME COMMENT '领取结束时间',
    `use_days` INT DEFAULT 0 COMMENT '有效天数，0表示固定时间',
    `description` VARCHAR(500) COMMENT '使用说明',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_coupon_code` (`coupon_code`),
    KEY `idx_coupon_type` (`coupon_type`),
    KEY `idx_status` (`status`),
    KEY `idx_start_end_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠券表';

-- =============================================
-- 2. 用户优惠券表
-- =============================================
CREATE TABLE IF NOT EXISTS `user_coupon` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `coupon_id` BIGINT NOT NULL COMMENT '优惠券ID',
    `coupon_code` VARCHAR(50) NOT NULL COMMENT '优惠券编码',
    `coupon_name` VARCHAR(100) NOT NULL COMMENT '优惠券名称',
    `coupon_type` TINYINT NOT NULL COMMENT '优惠券类型：1-满减券，2-折扣券，3-免邮券',
    `discount_type` TINYINT NOT NULL COMMENT '优惠类型：1-固定金额，2-百分比折扣',
    `discount_value` DECIMAL(10,2) NOT NULL COMMENT '优惠值',
    `min_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '最小使用金额',
    `max_discount` DECIMAL(10,2) COMMENT '最大优惠金额',
    `use_scope` TINYINT DEFAULT 1 COMMENT '使用范围：1-全场通用，2-指定分类，3-指定商品',
    `scope_ids` TEXT COMMENT '范围ID列表，逗号分隔',
    `receive_type` TINYINT DEFAULT 1 COMMENT '获得方式：1-主动领取，2-系统发放，3-活动赠送',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-未使用，2-已使用，3-已过期',
    `start_time` DATETIME NOT NULL COMMENT '开始时间',
    `end_time` DATETIME NOT NULL COMMENT '结束时间',
    `use_time` DATETIME COMMENT '使用时间',
    `order_no` VARCHAR(50) COMMENT '使用的订单号',
    `receive_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_coupon_id` (`coupon_id`),
    KEY `idx_status` (`status`),
    KEY `idx_start_end_time` (`start_time`, `end_time`),
    KEY `idx_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户优惠券表';

-- =============================================
-- 3. 营销活动表
-- =============================================
CREATE TABLE IF NOT EXISTS `marketing_activity` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `activity_name` VARCHAR(100) NOT NULL COMMENT '活动名称',
    `activity_code` VARCHAR(50) NOT NULL COMMENT '活动编码',
    `activity_type` TINYINT NOT NULL COMMENT '活动类型：1-满减活动，2-满折活动，3-满赠活动，4-限时折扣',
    `activity_desc` TEXT COMMENT '活动描述',
    `activity_rule` JSON COMMENT '活动规则JSON',
    `start_time` DATETIME NOT NULL COMMENT '开始时间',
    `end_time` DATETIME NOT NULL COMMENT '结束时间',
    `participate_limit` INT DEFAULT 0 COMMENT '参与限制，0表示不限制',
    `total_budget` DECIMAL(12,2) DEFAULT 0.00 COMMENT '活动预算',
    `used_budget` DECIMAL(12,2) DEFAULT 0.00 COMMENT '已使用预算',
    `priority` INT DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_activity_code` (`activity_code`),
    KEY `idx_activity_type` (`activity_type`),
    KEY `idx_status` (`status`),
    KEY `idx_start_end_time` (`start_time`, `end_time`),
    KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='营销活动表';

-- =============================================
-- 4. 活动商品表
-- =============================================
CREATE TABLE IF NOT EXISTS `activity_product` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `activity_id` BIGINT NOT NULL COMMENT '活动ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT COMMENT 'SKU ID，为空表示整个商品参与',
    `original_price` DECIMAL(10,2) NOT NULL COMMENT '原价',
    `activity_price` DECIMAL(10,2) NOT NULL COMMENT '活动价',
    `discount_amount` DECIMAL(10,2) NOT NULL COMMENT '优惠金额',
    `stock_limit` INT DEFAULT 0 COMMENT '活动库存限制，0表示不限制',
    `sold_quantity` INT DEFAULT 0 COMMENT '已售数量',
    `per_limit` INT DEFAULT 0 COMMENT '每人限购数量，0表示不限制',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_activity_product_sku` (`activity_id`, `product_id`, `sku_id`),
    KEY `idx_activity_id` (`activity_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动商品表';

-- =============================================
-- 5. 秒杀活动表
-- =============================================
CREATE TABLE IF NOT EXISTS `seckill_activity` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `activity_name` VARCHAR(100) NOT NULL COMMENT '秒杀活动名称',
    `activity_date` DATE NOT NULL COMMENT '活动日期',
    `start_time` TIME NOT NULL COMMENT '开始时间',
    `end_time` TIME NOT NULL COMMENT '结束时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-待开始，2-进行中，3-已结束，4-已取消',
    `description` VARCHAR(500) COMMENT '活动描述',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_activity_date` (`activity_date`),
    KEY `idx_status` (`status`),
    KEY `idx_start_end_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='秒杀活动表';

-- =============================================
-- 6. 秒杀商品表
-- =============================================
CREATE TABLE IF NOT EXISTS `seckill_product` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `activity_id` BIGINT NOT NULL COMMENT '秒杀活动ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `product_image` VARCHAR(255) COMMENT '商品图片',
    `original_price` DECIMAL(10,2) NOT NULL COMMENT '原价',
    `seckill_price` DECIMAL(10,2) NOT NULL COMMENT '秒杀价',
    `seckill_stock` INT NOT NULL COMMENT '秒杀库存',
    `sold_quantity` INT DEFAULT 0 COMMENT '已售数量',
    `per_limit` INT DEFAULT 1 COMMENT '每人限购数量',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_activity_sku` (`activity_id`, `sku_id`),
    KEY `idx_activity_id` (`activity_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='秒杀商品表';

-- =============================================
-- 7. 拼团活动表
-- =============================================
CREATE TABLE IF NOT EXISTS `group_buy_activity` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `activity_name` VARCHAR(100) NOT NULL COMMENT '拼团活动名称',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `product_image` VARCHAR(255) COMMENT '商品图片',
    `original_price` DECIMAL(10,2) NOT NULL COMMENT '原价',
    `group_price` DECIMAL(10,2) NOT NULL COMMENT '拼团价',
    `min_people` INT NOT NULL COMMENT '最少成团人数',
    `max_people` INT NOT NULL COMMENT '最多成团人数',
    `group_time_limit` INT NOT NULL COMMENT '成团时间限制（小时）',
    `per_limit` INT DEFAULT 1 COMMENT '每人限购数量',
    `start_time` DATETIME NOT NULL COMMENT '开始时间',
    `end_time` DATETIME NOT NULL COMMENT '结束时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `description` VARCHAR(500) COMMENT '活动描述',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_status` (`status`),
    KEY `idx_start_end_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拼团活动表';

-- =============================================
-- 8. 拼团记录表
-- =============================================
CREATE TABLE IF NOT EXISTS `group_buy_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `group_no` VARCHAR(50) NOT NULL COMMENT '拼团编号',
    `activity_id` BIGINT NOT NULL COMMENT '拼团活动ID',
    `leader_user_id` BIGINT NOT NULL COMMENT '团长用户ID',
    `leader_order_no` VARCHAR(50) NOT NULL COMMENT '团长订单号',
    `current_people` INT DEFAULT 1 COMMENT '当前人数',
    `target_people` INT NOT NULL COMMENT '目标人数',
    `group_status` TINYINT DEFAULT 1 COMMENT '拼团状态：1-拼团中，2-拼团成功，3-拼团失败',
    `start_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '开团时间',
    `end_time` DATETIME NOT NULL COMMENT '结束时间',
    `success_time` DATETIME COMMENT '成团时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_no` (`group_no`),
    KEY `idx_activity_id` (`activity_id`),
    KEY `idx_leader_user_id` (`leader_user_id`),
    KEY `idx_leader_order_no` (`leader_order_no`),
    KEY `idx_group_status` (`group_status`),
    KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拼团记录表';

-- 营销服务表结构创建完成 - 共8张表

# 🗄️ WitMall v2.0 初始化数据详细说明

## 📋 概述

`99_init_data.sql` 文件包含了WitMall v2.0电商平台的完整初始化数据，涵盖所有15个微服务数据库的基础数据和测试数据。

## 📊 数据统计

- **涉及数据库**: 15个微服务数据库
- **涉及表数量**: 102张表中的核心表
- **数据记录总数**: 约500+条记录
- **数据类型**: 基础配置数据 + 测试业务数据

## 🔧 各服务初始化数据详情

### 1. 用户服务 (wit_user)

#### 基础配置数据
- **用户等级**: 4个等级（普通会员、银牌会员、金牌会员、钻石会员）
- **角色数据**: 8个角色（超级管理员、系统管理员、运营人员、客服人员、商家管理员、商家员工、代理商、普通用户）
- **权限数据**: 14个权限节点（系统管理、商品管理、订单管理、营销管理等）

#### 测试用户数据
- **管理员账户**: admin / 123456（超级管理员）
- **运营账户**: operator / 123456（运营人员）
- **客服账户**: service / 123456（客服人员）
- **测试用户**: testuser / 123456（普通用户）

#### 商家数据
- **苹果官方旗舰店**: 电子产品销售
- **华为官方店**: 通信设备销售
- **小米专营店**: 智能设备销售

#### 代理商数据
- **华北区总代理**: 一级代理商
- **华东区总代理**: 一级代理商
- **北京分代理**: 二级代理商
- **上海分代理**: 二级代理商

### 2. 商品服务 (wit_product)

#### 分类数据
- **一级分类**: 电子产品、服装鞋帽、家居用品、图书音像
- **二级分类**: 手机数码、电脑办公、家用电器、男装、女装、童装

#### 品牌数据
- **国际品牌**: 苹果、三星
- **国产品牌**: 华为、小米、联想

#### 测试商品
- **iPhone 15 Pro**: 2个SKU（128GB、256GB）
- **MacBook Pro 14英寸**: 1个SKU（512GB）
- **华为Mate 60 Pro**: 1个SKU（512GB）

#### 商品服务
- 7天无理由退货、正品保证、全国联保、免费配送、24小时发货

### 3. 库存服务 (wit_inventory)

#### 仓库数据
- **北京总仓**: 主要仓库，包含2个库区
- **上海分仓**: 分仓，包含2个库区（含冷藏区）

#### 供应商数据
- **苹果供应链**: 一级供应商
- **华为供应商**: 一级供应商
- **小米生态链**: 二级供应商

#### 库存数据
- 所有测试商品的库存信息
- 库存批次信息
- 成本价格信息

### 4. 订单服务 (wit_order)

#### 物流公司
- 顺丰速运、中通快递、圆通速递、申通快递、韵达速递

#### 测试订单
- **订单1**: testuser购买iPhone 15 Pro（已完成）
- **订单2**: testuser购买华为Mate 60 Pro（待发货）

### 5. 支付服务 (wit_payment)

#### 支付方式
- 微信支付、支付宝、余额支付、银联支付

#### 用户账户
- 管理员和测试用户的余额账户、积分账户

### 6. 购物车服务 (wit_cart)

#### 测试数据
- testuser的购物车中有MacBook Pro

### 7. 营销服务 (wit_marketing)

#### 优惠券
- **新用户专享券**: 满100减50
- **满减优惠券**: 满500减100
- **折扣优惠券**: 满200享8折

#### 营销活动
- **春节大促**: 满减活动
- **618购物节**: 折扣活动

#### 秒杀活动
- 每日10点场、20点场秒杀

### 8. 评价服务 (wit_review)

#### 测试评价
- iPhone 15 Pro的5星好评
- 商家回复

### 9. 通知服务 (wit_notification)

#### 消息模板
- 订单支付成功通知
- 订单发货通知
- 验证码短信模板

### 10. 文件服务 (wit_file)

#### 文件分类
- 商品图片、用户头像、系统图标、营销素材

### 11. 系统服务 (wit_system)

#### 系统配置
- 网站基本信息（名称、Logo、关键词等）
- 业务配置（订单取消时间、自动收货时间等）
- 功能配置（短信限制、文件上传限制等）

#### 字典数据
- 用户性别、订单状态、支付状态、物流状态等字典

### 12. 定时任务服务 (wit_schedule)

#### 定时任务
- 订单自动取消任务（每5分钟）
- 订单自动收货任务（每天凌晨2点）
- 数据统计任务（每天凌晨1点30分）

### 13. 数据分析服务 (wit_analytics)

#### 统计数据
- 3天的销售统计数据
- 3天的用户统计数据

### 14. 搜索服务 (wit_search)

#### 搜索数据
- 热门搜索关键词（iPhone、华为、小米、MacBook）
- 搜索统计数据

### 15. 推荐服务 (wit_recommendation)

#### 用户偏好
- testuser的商品偏好数据
- 品牌偏好、价格偏好等

## 🎯 使用说明

### 执行顺序
1. 先执行表结构创建脚本（00-15号文件）
2. 最后执行初始化数据脚本（99_init_data.sql）

### 注意事项
1. **密码说明**: 所有用户密码都是 `123456`，使用BCrypt加密
2. **测试数据**: 包含的商品、订单等都是测试数据
3. **生产使用**: 生产环境请修改默认密码和敏感配置

### 验证方法
执行完成后可以验证：
```sql
-- 验证用户数据
SELECT * FROM wit_user.user;

-- 验证商品数据  
SELECT * FROM wit_product.product;

-- 验证订单数据
SELECT * FROM wit_order.order_info;
```

## 🔐 安全提醒

⚠️ **重要**: 生产环境使用前请务必：
1. 修改所有默认密码
2. 删除测试数据
3. 配置正确的系统参数
4. 设置合适的权限控制

---

📝 **说明**: 这是一个完整的企业级电商平台初始化数据，包含了所有必要的基础数据和丰富的测试数据，可以直接用于开发和测试环境。

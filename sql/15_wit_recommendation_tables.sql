-- =============================================
-- WitMall v2.0 推荐服务表结构
-- 数据库: wit_recommendation
-- 表数量: 2张
-- 功能: 商品推荐、用户画像
-- =============================================

USE `wit_recommendation`;

-- =============================================
-- 1. 用户偏好表
-- =============================================
CREATE TABLE IF NOT EXISTS `user_preference` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `preference_type` TINYINT NOT NULL COMMENT '偏好类型：1-分类偏好，2-品牌偏好，3-价格偏好，4-属性偏好',
    `preference_key` VARCHAR(100) NOT NULL COMMENT '偏好键（分类ID、品牌ID等）',
    `preference_value` VARCHAR(200) NOT NULL COMMENT '偏好值',
    `preference_score` DECIMAL(8,4) DEFAULT 0.0000 COMMENT '偏好分数',
    `behavior_count` INT DEFAULT 0 COMMENT '行为次数',
    `last_behavior_time` DATETIME COMMENT '最后行为时间',
    `weight` DECIMAL(5,4) DEFAULT 1.0000 COMMENT '权重',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-有效，0-无效',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_type_key` (`user_id`, `preference_type`, `preference_key`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_preference_type` (`preference_type`),
    KEY `idx_preference_score` (`preference_score`),
    KEY `idx_last_behavior_time` (`last_behavior_time`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户偏好表';

-- =============================================
-- 2. 推荐日志表
-- =============================================
CREATE TABLE IF NOT EXISTS `recommend_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT COMMENT '用户ID，未登录用户为空',
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID',
    `recommend_type` TINYINT NOT NULL COMMENT '推荐类型：1-首页推荐，2-商品详情页推荐，3-购物车推荐，4-搜索推荐',
    `recommend_scene` VARCHAR(100) NOT NULL COMMENT '推荐场景',
    `algorithm_type` TINYINT NOT NULL COMMENT '算法类型：1-协同过滤，2-内容推荐，3-热门推荐，4-个性化推荐',
    `algorithm_version` VARCHAR(50) COMMENT '算法版本',
    `recommend_products` JSON NOT NULL COMMENT '推荐商品列表JSON',
    `product_count` INT DEFAULT 0 COMMENT '推荐商品数量',
    `click_products` JSON COMMENT '点击商品列表JSON',
    `click_count` INT DEFAULT 0 COMMENT '点击数量',
    `order_products` JSON COMMENT '下单商品列表JSON',
    `order_count` INT DEFAULT 0 COMMENT '下单数量',
    `click_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '点击率',
    `conversion_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '转化率',
    `recommend_time` DATETIME NOT NULL COMMENT '推荐时间',
    `device_type` TINYINT COMMENT '设备类型：1-PC，2-移动端，3-平板',
    `client_ip` VARCHAR(50) COMMENT '客户端IP',
    `location` VARCHAR(200) COMMENT '地理位置',
    `user_agent` VARCHAR(500) COMMENT '用户代理',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_recommend_type` (`recommend_type`),
    KEY `idx_recommend_scene` (`recommend_scene`),
    KEY `idx_algorithm_type` (`algorithm_type`),
    KEY `idx_recommend_time` (`recommend_time`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐日志表';

-- 推荐服务表结构创建完成 - 共2张表

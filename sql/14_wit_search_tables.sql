-- =============================================
-- WitMall v2.0 搜索服务表结构
-- 数据库: wit_search
-- 表数量: 3张
-- 功能: 商品搜索、索引管理、搜索统计
-- =============================================

USE `wit_search`;

-- =============================================
-- 1. 搜索关键词表
-- =============================================
CREATE TABLE IF NOT EXISTS `search_keyword` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `keyword` VARCHAR(200) NOT NULL COMMENT '搜索关键词',
    `keyword_type` TINYINT DEFAULT 1 COMMENT '关键词类型：1-用户搜索，2-热门推荐，3-系统推荐',
    `search_count` INT DEFAULT 0 COMMENT '搜索次数',
    `result_count` INT DEFAULT 0 COMMENT '搜索结果数',
    `click_count` INT DEFAULT 0 COMMENT '点击次数',
    `conversion_count` INT DEFAULT 0 COMMENT '转化次数',
    `click_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '点击率',
    `conversion_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '转化率',
    `is_hot` TINYINT DEFAULT 0 COMMENT '是否热门：1-是，0-否',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `first_search_time` DATETIME COMMENT '首次搜索时间',
    `last_search_time` DATETIME COMMENT '最后搜索时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_keyword` (`keyword`),
    KEY `idx_keyword_type` (`keyword_type`),
    KEY `idx_search_count` (`search_count`),
    KEY `idx_is_hot` (`is_hot`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_status` (`status`),
    KEY `idx_last_search_time` (`last_search_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索关键词表';

-- =============================================
-- 2. 搜索日志表
-- =============================================
CREATE TABLE IF NOT EXISTS `search_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT COMMENT '用户ID，未登录用户为空',
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID',
    `keyword` VARCHAR(200) NOT NULL COMMENT '搜索关键词',
    `search_type` TINYINT DEFAULT 1 COMMENT '搜索类型：1-商品搜索，2-店铺搜索，3-品牌搜索',
    `category_id` BIGINT COMMENT '搜索分类ID',
    `brand_id` BIGINT COMMENT '搜索品牌ID',
    `price_min` DECIMAL(10,2) COMMENT '最低价格',
    `price_max` DECIMAL(10,2) COMMENT '最高价格',
    `sort_type` TINYINT COMMENT '排序类型：1-综合，2-价格升序，3-价格降序，4-销量，5-评价',
    `result_count` INT DEFAULT 0 COMMENT '搜索结果数',
    `page_no` INT DEFAULT 1 COMMENT '页码',
    `page_size` INT DEFAULT 20 COMMENT '每页大小',
    `search_time` DATETIME NOT NULL COMMENT '搜索时间',
    `response_time` INT COMMENT '响应时间（毫秒）',
    `device_type` TINYINT COMMENT '设备类型：1-PC，2-移动端，3-平板',
    `client_ip` VARCHAR(50) COMMENT '客户端IP',
    `location` VARCHAR(200) COMMENT '地理位置',
    `user_agent` VARCHAR(500) COMMENT '用户代理',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_keyword` (`keyword`),
    KEY `idx_search_type` (`search_type`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_brand_id` (`brand_id`),
    KEY `idx_search_time` (`search_time`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索日志表';

-- =============================================
-- 3. 热门搜索表
-- =============================================
CREATE TABLE IF NOT EXISTS `hot_search` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `keyword` VARCHAR(200) NOT NULL COMMENT '搜索关键词',
    `search_count` INT DEFAULT 0 COMMENT '搜索次数',
    `trend_type` TINYINT DEFAULT 1 COMMENT '趋势类型：1-上升，2-下降，3-持平，4-新增',
    `rank_position` INT DEFAULT 0 COMMENT '排名位置',
    `last_rank_position` INT DEFAULT 0 COMMENT '上次排名位置',
    `rank_change` INT DEFAULT 0 COMMENT '排名变化',
    `heat_score` DECIMAL(10,2) DEFAULT 0.00 COMMENT '热度分数',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `is_manual` TINYINT DEFAULT 0 COMMENT '是否人工设置：1-是，0-否',
    `manual_sort` INT DEFAULT 0 COMMENT '人工排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_keyword_date` (`keyword`, `stat_date`),
    KEY `idx_search_count` (`search_count`),
    KEY `idx_trend_type` (`trend_type`),
    KEY `idx_rank_position` (`rank_position`),
    KEY `idx_heat_score` (`heat_score`),
    KEY `idx_stat_date` (`stat_date`),
    KEY `idx_is_manual` (`is_manual`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='热门搜索表';

-- 搜索服务表结构创建完成 - 共3张表

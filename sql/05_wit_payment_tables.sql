-- =============================================
-- WitMall v2.0 支付服务表结构
-- 数据库: wit_payment
-- 表数量: 12张
-- 功能: 支付管理、退款管理、账户管理、佣金结算
-- =============================================

USE `wit_payment`;

-- =============================================
-- 1. 支付方式表
-- =============================================
CREATE TABLE IF NOT EXISTS `payment_method` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `method_name` VARCHAR(100) NOT NULL COMMENT '支付方式名称',
    `method_code` VARCHAR(50) NOT NULL COMMENT '支付方式编码',
    `method_type` TINYINT NOT NULL COMMENT '支付类型：1-在线支付，2-线下支付，3-余额支付，4-积分支付',
    `channel_code` VARCHAR(50) COMMENT '支付渠道编码',
    `icon` VARCHAR(255) COMMENT '支付图标',
    `description` VARCHAR(500) COMMENT '支付描述',
    `config` JSON COMMENT '支付配置JSON',
    `fee_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '手续费率',
    `min_amount` DECIMAL(10,2) DEFAULT 0.01 COMMENT '最小支付金额',
    `max_amount` DECIMAL(10,2) DEFAULT 999999.99 COMMENT '最大支付金额',
    `is_enabled` TINYINT DEFAULT 1 COMMENT '是否启用：1-是，0-否',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_method_code` (`method_code`),
    KEY `idx_method_type` (`method_type`),
    KEY `idx_channel_code` (`channel_code`),
    KEY `idx_is_enabled` (`is_enabled`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付方式表';

-- =============================================
-- 2. 支付记录表
-- =============================================
CREATE TABLE IF NOT EXISTS `payment_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `payment_no` VARCHAR(50) NOT NULL COMMENT '支付单号',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `merchant_id` BIGINT COMMENT '商家ID',
    `payment_method_id` BIGINT NOT NULL COMMENT '支付方式ID',
    `payment_method_code` VARCHAR(50) NOT NULL COMMENT '支付方式编码',
    `payment_method_name` VARCHAR(100) NOT NULL COMMENT '支付方式名称',
    `payment_type` TINYINT NOT NULL COMMENT '支付类型：1-订单支付，2-充值，3-提现，4-退款',
    `payment_amount` DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    `fee_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '手续费',
    `actual_amount` DECIMAL(10,2) NOT NULL COMMENT '实际支付金额',
    `currency` VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    `exchange_rate` DECIMAL(10,4) DEFAULT 1.0000 COMMENT '汇率',
    `payment_status` TINYINT DEFAULT 1 COMMENT '支付状态：1-待支付，2-支付中，3-支付成功，4-支付失败，5-已取消',
    `third_party_no` VARCHAR(100) COMMENT '第三方支付单号',
    `third_party_response` TEXT COMMENT '第三方响应数据',
    `notify_url` VARCHAR(255) COMMENT '异步通知地址',
    `return_url` VARCHAR(255) COMMENT '同步返回地址',
    `client_ip` VARCHAR(50) COMMENT '客户端IP',
    `user_agent` VARCHAR(500) COMMENT '用户代理',
    `payment_time` DATETIME COMMENT '支付时间',
    `expire_time` DATETIME COMMENT '过期时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_payment_no` (`payment_no`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_payment_method_id` (`payment_method_id`),
    KEY `idx_payment_status` (`payment_status`),
    KEY `idx_third_party_no` (`third_party_no`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';

-- =============================================
-- 3. 支付通知记录表
-- =============================================
CREATE TABLE IF NOT EXISTS `payment_notify` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `payment_id` BIGINT NOT NULL COMMENT '支付记录ID',
    `payment_no` VARCHAR(50) NOT NULL COMMENT '支付单号',
    `notify_type` TINYINT NOT NULL COMMENT '通知类型：1-支付通知，2-退款通知',
    `notify_source` VARCHAR(50) NOT NULL COMMENT '通知来源',
    `notify_data` TEXT NOT NULL COMMENT '通知数据',
    `notify_time` DATETIME NOT NULL COMMENT '通知时间',
    `process_status` TINYINT DEFAULT 1 COMMENT '处理状态：1-待处理，2-处理成功，3-处理失败',
    `process_time` DATETIME COMMENT '处理时间',
    `process_result` TEXT COMMENT '处理结果',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_payment_id` (`payment_id`),
    KEY `idx_payment_no` (`payment_no`),
    KEY `idx_notify_type` (`notify_type`),
    KEY `idx_process_status` (`process_status`),
    KEY `idx_notify_time` (`notify_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付通知记录表';

-- =============================================
-- 4. 退款记录表
-- =============================================
CREATE TABLE IF NOT EXISTS `refund_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `refund_no` VARCHAR(50) NOT NULL COMMENT '退款单号',
    `payment_id` BIGINT NOT NULL COMMENT '原支付记录ID',
    `payment_no` VARCHAR(50) NOT NULL COMMENT '原支付单号',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `merchant_id` BIGINT COMMENT '商家ID',
    `refund_type` TINYINT NOT NULL COMMENT '退款类型：1-全额退款，2-部分退款',
    `refund_reason` VARCHAR(200) NOT NULL COMMENT '退款原因',
    `refund_amount` DECIMAL(10,2) NOT NULL COMMENT '退款金额',
    `fee_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款手续费',
    `actual_amount` DECIMAL(10,2) NOT NULL COMMENT '实际退款金额',
    `refund_method` TINYINT NOT NULL COMMENT '退款方式：1-原路退回，2-余额，3-银行卡',
    `refund_account` VARCHAR(100) COMMENT '退款账户',
    `refund_status` TINYINT DEFAULT 1 COMMENT '退款状态：1-待退款，2-退款中，3-退款成功，4-退款失败',
    `third_party_no` VARCHAR(100) COMMENT '第三方退款单号',
    `third_party_response` TEXT COMMENT '第三方响应数据',
    `refund_time` DATETIME COMMENT '退款时间',
    `success_time` DATETIME COMMENT '退款成功时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_no` (`refund_no`),
    KEY `idx_payment_id` (`payment_id`),
    KEY `idx_payment_no` (`payment_no`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_refund_status` (`refund_status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';

-- =============================================
-- 5. 用户账户表
-- =============================================
CREATE TABLE IF NOT EXISTS `user_account` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `account_type` TINYINT NOT NULL COMMENT '账户类型：1-余额账户，2-积分账户，3-佣金账户',
    `balance` DECIMAL(12,2) DEFAULT 0.00 COMMENT '账户余额',
    `frozen_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '冻结金额',
    `available_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '可用金额',
    `total_income` DECIMAL(12,2) DEFAULT 0.00 COMMENT '总收入',
    `total_expense` DECIMAL(12,2) DEFAULT 0.00 COMMENT '总支出',
    `password` VARCHAR(100) COMMENT '支付密码',
    `salt` VARCHAR(32) COMMENT '密码盐值',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-冻结',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_account_type` (`user_id`, `account_type`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_account_type` (`account_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表';

-- =============================================
-- 6. 账户变动记录表
-- =============================================
CREATE TABLE IF NOT EXISTS `account_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `record_no` VARCHAR(50) NOT NULL COMMENT '记录编号',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `account_id` BIGINT NOT NULL COMMENT '账户ID',
    `account_type` TINYINT NOT NULL COMMENT '账户类型：1-余额账户，2-积分账户，3-佣金账户',
    `change_type` TINYINT NOT NULL COMMENT '变动类型：1-收入，2-支出，3-冻结，4-解冻',
    `change_amount` DECIMAL(12,2) NOT NULL COMMENT '变动金额',
    `before_balance` DECIMAL(12,2) NOT NULL COMMENT '变动前余额',
    `after_balance` DECIMAL(12,2) NOT NULL COMMENT '变动后余额',
    `business_type` TINYINT NOT NULL COMMENT '业务类型：1-充值，2-消费，3-退款，4-提现，5-佣金，6-奖励',
    `business_no` VARCHAR(50) COMMENT '业务单号',
    `description` VARCHAR(500) NOT NULL COMMENT '变动描述',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_record_no` (`record_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_account_id` (`account_id`),
    KEY `idx_account_type` (`account_type`),
    KEY `idx_change_type` (`change_type`),
    KEY `idx_business_type` (`business_type`),
    KEY `idx_business_no` (`business_no`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户变动记录表';

-- =============================================
-- 7. 佣金记录表
-- =============================================
CREATE TABLE IF NOT EXISTS `commission_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `commission_no` VARCHAR(50) NOT NULL COMMENT '佣金单号',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `user_id` BIGINT NOT NULL COMMENT '获得佣金的用户ID',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `agent_id` BIGINT COMMENT '代理商ID',
    `commission_type` TINYINT NOT NULL COMMENT '佣金类型：1-销售佣金，2-推广佣金，3-代理佣金',
    `commission_level` TINYINT DEFAULT 1 COMMENT '佣金层级',
    `order_amount` DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    `commission_rate` DECIMAL(5,4) NOT NULL COMMENT '佣金比例',
    `commission_amount` DECIMAL(10,2) NOT NULL COMMENT '佣金金额',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-待结算，2-已结算，3-已取消',
    `settle_time` DATETIME COMMENT '结算时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_commission_no` (`commission_no`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_agent_id` (`agent_id`),
    KEY `idx_commission_type` (`commission_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='佣金记录表';

-- =============================================
-- 8. 结算单表
-- =============================================
CREATE TABLE IF NOT EXISTS `settlement_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `settlement_no` VARCHAR(50) NOT NULL COMMENT '结算单号',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `settlement_type` TINYINT NOT NULL COMMENT '结算类型：1-订单结算，2-佣金结算，3-退款结算',
    `settlement_period` VARCHAR(20) NOT NULL COMMENT '结算周期',
    `start_time` DATETIME NOT NULL COMMENT '开始时间',
    `end_time` DATETIME NOT NULL COMMENT '结束时间',
    `order_count` INT DEFAULT 0 COMMENT '订单数量',
    `order_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '订单金额',
    `commission_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '佣金金额',
    `refund_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '退款金额',
    `fee_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '手续费',
    `settlement_amount` DECIMAL(12,2) NOT NULL COMMENT '结算金额',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-待结算，2-已结算，3-已取消',
    `settlement_time` DATETIME COMMENT '结算时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_settlement_no` (`settlement_no`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_settlement_type` (`settlement_type`),
    KEY `idx_settlement_period` (`settlement_period`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='结算单表';

-- =============================================
-- 9. 结算明细表
-- =============================================
CREATE TABLE IF NOT EXISTS `settlement_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `settlement_id` BIGINT NOT NULL COMMENT '结算单ID',
    `settlement_no` VARCHAR(50) NOT NULL COMMENT '结算单号',
    `business_type` TINYINT NOT NULL COMMENT '业务类型：1-订单，2-佣金，3-退款',
    `business_no` VARCHAR(50) NOT NULL COMMENT '业务单号',
    `business_amount` DECIMAL(10,2) NOT NULL COMMENT '业务金额',
    `commission_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '佣金金额',
    `fee_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '手续费',
    `settlement_amount` DECIMAL(10,2) NOT NULL COMMENT '结算金额',
    `business_time` DATETIME NOT NULL COMMENT '业务时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_settlement_id` (`settlement_id`),
    KEY `idx_settlement_no` (`settlement_no`),
    KEY `idx_business_type` (`business_type`),
    KEY `idx_business_no` (`business_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='结算明细表';

-- =============================================
-- 10. 支付渠道表
-- =============================================
CREATE TABLE IF NOT EXISTS `payment_channel` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `channel_name` VARCHAR(100) NOT NULL COMMENT '渠道名称',
    `channel_code` VARCHAR(50) NOT NULL COMMENT '渠道编码',
    `channel_type` TINYINT NOT NULL COMMENT '渠道类型：1-第三方支付，2-银行直连，3-钱包支付',
    `provider` VARCHAR(100) NOT NULL COMMENT '服务提供商',
    `api_url` VARCHAR(200) COMMENT 'API接口地址',
    `app_id` VARCHAR(100) COMMENT '应用ID',
    `app_secret` VARCHAR(200) COMMENT '应用密钥',
    `public_key` TEXT COMMENT '公钥',
    `private_key` TEXT COMMENT '私钥',
    `config` JSON COMMENT '其他配置JSON',
    `fee_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '手续费率',
    `min_amount` DECIMAL(10,2) DEFAULT 0.01 COMMENT '最小金额',
    `max_amount` DECIMAL(10,2) DEFAULT 999999.99 COMMENT '最大金额',
    `is_enabled` TINYINT DEFAULT 1 COMMENT '是否启用：1-是，0-否',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_channel_code` (`channel_code`),
    KEY `idx_channel_type` (`channel_type`),
    KEY `idx_provider` (`provider`),
    KEY `idx_is_enabled` (`is_enabled`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付渠道表';

-- =============================================
-- 11. 支付配置表
-- =============================================
CREATE TABLE IF NOT EXISTS `payment_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT COMMENT '配置值',
    `config_type` TINYINT DEFAULT 1 COMMENT '配置类型：1-字符串，2-数字，3-布尔，4-JSON',
    `description` VARCHAR(500) COMMENT '配置描述',
    `is_encrypted` TINYINT DEFAULT 0 COMMENT '是否加密：1-是，0-否',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`),
    KEY `idx_config_type` (`config_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付配置表';

-- =============================================
-- 12. 提现记录表
-- =============================================
CREATE TABLE IF NOT EXISTS `withdraw_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `withdraw_no` VARCHAR(50) NOT NULL COMMENT '提现单号',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `account_id` BIGINT NOT NULL COMMENT '账户ID',
    `account_type` TINYINT NOT NULL COMMENT '账户类型：1-余额账户，3-佣金账户',
    `withdraw_amount` DECIMAL(10,2) NOT NULL COMMENT '提现金额',
    `fee_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '手续费',
    `actual_amount` DECIMAL(10,2) NOT NULL COMMENT '实际到账金额',
    `withdraw_type` TINYINT NOT NULL COMMENT '提现方式：1-银行卡，2-支付宝，3-微信',
    `account_name` VARCHAR(100) NOT NULL COMMENT '账户名称',
    `account_no` VARCHAR(100) NOT NULL COMMENT '账户号码',
    `bank_name` VARCHAR(100) COMMENT '银行名称',
    `bank_branch` VARCHAR(200) COMMENT '开户行',
    `withdraw_status` TINYINT DEFAULT 1 COMMENT '提现状态：1-待审核，2-审核通过，3-审核拒绝，4-处理中，5-提现成功，6-提现失败',
    `audit_time` DATETIME COMMENT '审核时间',
    `audit_by` BIGINT COMMENT '审核人',
    `audit_remark` VARCHAR(500) COMMENT '审核备注',
    `process_time` DATETIME COMMENT '处理时间',
    `success_time` DATETIME COMMENT '成功时间',
    `third_party_no` VARCHAR(100) COMMENT '第三方单号',
    `failure_reason` VARCHAR(500) COMMENT '失败原因',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_withdraw_no` (`withdraw_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_account_id` (`account_id`),
    KEY `idx_account_type` (`account_type`),
    KEY `idx_withdraw_status` (`withdraw_status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现记录表';

-- 支付服务表结构创建完成 - 共12张表

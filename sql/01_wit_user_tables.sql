-- =============================================
-- WitMall v2.0 用户服务表结构
-- 数据库: wit_user
-- 表数量: 12张
-- 功能: 用户管理、商家管理、代理商体系、权限控制
-- =============================================

USE `wit_user`;

-- =============================================
-- 1. 用户等级表
-- =============================================
CREATE TABLE IF NOT EXISTS `user_level` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `level_name` VARCHAR(50) NOT NULL COMMENT '等级名称',
    `level_code` VARCHAR(20) NOT NULL COMMENT '等级编码',
    `min_points` INT DEFAULT 0 COMMENT '最小积分',
    `max_points` INT DEFAULT 0 COMMENT '最大积分',
    `discount_rate` DECIMAL(3,2) DEFAULT 1.00 COMMENT '折扣率',
    `privileges` JSON COMMENT '特权列表JSON',
    `icon` VARCHAR(255) COMMENT '等级图标',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_level_code` (`level_code`),
    KEY `idx_min_points` (`min_points`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户等级表';

-- =============================================
-- 2. 用户表
-- =============================================
CREATE TABLE IF NOT EXISTS `user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(100) NOT NULL COMMENT '密码',
    `salt` VARCHAR(32) COMMENT '密码盐值',
    `email` VARCHAR(100) COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '手机号',
    `avatar` VARCHAR(255) COMMENT '头像',
    `nickname` VARCHAR(50) COMMENT '昵称',
    `real_name` VARCHAR(50) COMMENT '真实姓名',
    `gender` TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    `birthday` DATE COMMENT '生日',
    `id_card` VARCHAR(18) COMMENT '身份证号',
    `user_type` TINYINT DEFAULT 1 COMMENT '用户类型：1-普通用户，2-商家，3-代理商，4-管理员',
    `level_id` BIGINT COMMENT '用户等级ID',
    `points` INT DEFAULT 0 COMMENT '积分',
    `balance` DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用，-1-删除',
    `register_source` TINYINT DEFAULT 1 COMMENT '注册来源：1-网站，2-微信，3-QQ，4-微博，5-APP',
    `register_ip` VARCHAR(50) COMMENT '注册IP',
    `last_login_time` DATETIME COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(50) COMMENT '最后登录IP',
    `login_count` INT DEFAULT 0 COMMENT '登录次数',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_user_type` (`user_type`),
    KEY `idx_status` (`status`),
    KEY `idx_level_id` (`level_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- =============================================
-- 3. 用户地址表
-- =============================================
CREATE TABLE IF NOT EXISTS `user_address` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `receiver_name` VARCHAR(50) NOT NULL COMMENT '收货人姓名',
    `receiver_phone` VARCHAR(20) NOT NULL COMMENT '收货人电话',
    `province_code` VARCHAR(10) NOT NULL COMMENT '省份编码',
    `province_name` VARCHAR(50) NOT NULL COMMENT '省份名称',
    `city_code` VARCHAR(10) NOT NULL COMMENT '城市编码',
    `city_name` VARCHAR(50) NOT NULL COMMENT '城市名称',
    `district_code` VARCHAR(10) NOT NULL COMMENT '区县编码',
    `district_name` VARCHAR(50) NOT NULL COMMENT '区县名称',
    `detail_address` VARCHAR(200) NOT NULL COMMENT '详细地址',
    `postal_code` VARCHAR(10) COMMENT '邮政编码',
    `is_default` TINYINT DEFAULT 0 COMMENT '是否默认：1-是，0-否',
    `address_type` TINYINT DEFAULT 1 COMMENT '地址类型：1-家庭，2-公司，3-学校',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户地址表';

-- =============================================
-- 4. 商家表
-- =============================================
CREATE TABLE IF NOT EXISTS `merchant` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `merchant_code` VARCHAR(50) NOT NULL COMMENT '商家编码',
    `merchant_name` VARCHAR(100) NOT NULL COMMENT '商家名称',
    `merchant_type` TINYINT DEFAULT 1 COMMENT '商家类型：1-个人，2-企业',
    `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
    `contact_name` VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    `contact_phone` VARCHAR(20) NOT NULL COMMENT '联系人电话',
    `contact_email` VARCHAR(100) COMMENT '联系人邮箱',
    `business_license` VARCHAR(100) COMMENT '营业执照号',
    `license_image` VARCHAR(255) COMMENT '营业执照图片',
    `id_card_front` VARCHAR(255) COMMENT '身份证正面',
    `id_card_back` VARCHAR(255) COMMENT '身份证背面',
    `bank_account` VARCHAR(50) COMMENT '银行账号',
    `bank_name` VARCHAR(100) COMMENT '开户银行',
    `account_holder` VARCHAR(50) COMMENT '开户人',
    `province_code` VARCHAR(10) COMMENT '省份编码',
    `province_name` VARCHAR(50) COMMENT '省份名称',
    `city_code` VARCHAR(10) COMMENT '城市编码',
    `city_name` VARCHAR(50) COMMENT '城市名称',
    `district_code` VARCHAR(10) COMMENT '区县编码',
    `district_name` VARCHAR(50) COMMENT '区县名称',
    `detail_address` VARCHAR(200) COMMENT '详细地址',
    `logo` VARCHAR(255) COMMENT '商家Logo',
    `description` TEXT COMMENT '商家描述',
    `service_phone` VARCHAR(20) COMMENT '客服电话',
    `qq` VARCHAR(20) COMMENT 'QQ号码',
    `wechat` VARCHAR(50) COMMENT '微信号',
    `status` TINYINT DEFAULT 0 COMMENT '状态：0-待审核，1-正常，2-冻结，-1-拒绝',
    `audit_time` DATETIME COMMENT '审核时间',
    `audit_by` BIGINT COMMENT '审核人',
    `audit_remark` VARCHAR(500) COMMENT '审核备注',
    `commission_rate` DECIMAL(5,4) DEFAULT 0.0500 COMMENT '佣金比例',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_merchant_code` (`merchant_code`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    KEY `idx_merchant_type` (`merchant_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家表';

-- =============================================
-- 5. 商家店铺表
-- =============================================
CREATE TABLE IF NOT EXISTS `merchant_shop` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `shop_name` VARCHAR(100) NOT NULL COMMENT '店铺名称',
    `shop_code` VARCHAR(50) NOT NULL COMMENT '店铺编码',
    `shop_type` TINYINT DEFAULT 1 COMMENT '店铺类型：1-旗舰店，2-专卖店，3-专营店',
    `logo` VARCHAR(255) COMMENT '店铺Logo',
    `banner` VARCHAR(255) COMMENT '店铺横幅',
    `description` TEXT COMMENT '店铺描述',
    `keywords` VARCHAR(200) COMMENT '店铺关键词',
    `service_phone` VARCHAR(20) COMMENT '客服电话',
    `qq` VARCHAR(20) COMMENT 'QQ号码',
    `wechat` VARCHAR(50) COMMENT '微信号',
    `business_hours` VARCHAR(100) COMMENT '营业时间',
    `notice` TEXT COMMENT '店铺公告',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-关闭',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_shop_code` (`shop_code`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_shop_type` (`shop_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家店铺表';

-- =============================================
-- 6. 代理商表
-- =============================================
CREATE TABLE IF NOT EXISTS `agent` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `agent_code` VARCHAR(50) NOT NULL COMMENT '代理商编码',
    `agent_name` VARCHAR(100) NOT NULL COMMENT '代理商名称',
    `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
    `parent_id` BIGINT DEFAULT 0 COMMENT '上级代理商ID，0表示顶级',
    `level` TINYINT DEFAULT 1 COMMENT '代理商层级',
    `agent_type` TINYINT DEFAULT 1 COMMENT '代理商类型：1-个人代理，2-企业代理',
    `contact_name` VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    `contact_phone` VARCHAR(20) NOT NULL COMMENT '联系人电话',
    `contact_email` VARCHAR(100) COMMENT '联系人邮箱',
    `province_code` VARCHAR(10) COMMENT '省份编码',
    `province_name` VARCHAR(50) COMMENT '省份名称',
    `city_code` VARCHAR(10) COMMENT '城市编码',
    `city_name` VARCHAR(50) COMMENT '城市名称',
    `district_code` VARCHAR(10) COMMENT '区县编码',
    `district_name` VARCHAR(50) COMMENT '区县名称',
    `detail_address` VARCHAR(200) COMMENT '详细地址',
    `commission_rate` DECIMAL(5,4) DEFAULT 0.0300 COMMENT '佣金比例',
    `status` TINYINT DEFAULT 0 COMMENT '状态：0-待审核，1-正常，2-冻结，-1-拒绝',
    `audit_time` DATETIME COMMENT '审核时间',
    `audit_by` BIGINT COMMENT '审核人',
    `audit_remark` VARCHAR(500) COMMENT '审核备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_agent_code` (`agent_code`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_level` (`level`),
    KEY `idx_agent_type` (`agent_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商表';

-- =============================================
-- 7. 代理商关系表
-- =============================================
CREATE TABLE IF NOT EXISTS `agent_relation` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `agent_id` BIGINT NOT NULL COMMENT '代理商ID',
    `parent_id` BIGINT NOT NULL COMMENT '上级代理商ID',
    `level` TINYINT NOT NULL COMMENT '层级关系',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_agent_parent_level` (`agent_id`, `parent_id`, `level`),
    KEY `idx_agent_id` (`agent_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商关系表';

-- =============================================
-- 8. 角色表
-- =============================================
CREATE TABLE IF NOT EXISTS `role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
    `role_type` TINYINT DEFAULT 1 COMMENT '角色类型：1-系统角色，2-商家角色，3-代理商角色',
    `description` VARCHAR(200) COMMENT '角色描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code` (`role_code`),
    KEY `idx_role_type` (`role_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- =============================================
-- 9. 权限表
-- =============================================
CREATE TABLE IF NOT EXISTS `permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `permission_name` VARCHAR(100) NOT NULL COMMENT '权限名称',
    `permission_code` VARCHAR(100) NOT NULL COMMENT '权限编码',
    `parent_id` BIGINT DEFAULT 0 COMMENT '父权限ID，0表示顶级',
    `permission_type` TINYINT DEFAULT 1 COMMENT '权限类型：1-菜单，2-按钮，3-接口',
    `path` VARCHAR(200) COMMENT '路径',
    `component` VARCHAR(200) COMMENT '组件',
    `icon` VARCHAR(100) COMMENT '图标',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_permission_type` (`permission_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- =============================================
-- 10. 用户角色关联表
-- =============================================
CREATE TABLE IF NOT EXISTS `user_role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- =============================================
-- 11. 角色权限关联表
-- =============================================
CREATE TABLE IF NOT EXISTS `role_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `permission_id` BIGINT NOT NULL COMMENT '权限ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- =============================================
-- 12. 用户登录日志表
-- =============================================
CREATE TABLE IF NOT EXISTS `user_login_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `login_ip` VARCHAR(50) COMMENT '登录IP',
    `login_location` VARCHAR(200) COMMENT '登录地点',
    `browser` VARCHAR(100) COMMENT '浏览器',
    `os` VARCHAR(100) COMMENT '操作系统',
    `login_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `login_status` TINYINT DEFAULT 1 COMMENT '登录状态：1-成功，0-失败',
    `error_message` VARCHAR(500) COMMENT '错误信息',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_login_time` (`login_time`),
    KEY `idx_login_status` (`login_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录日志表';

-- 用户服务表结构创建完成 - 共12张表

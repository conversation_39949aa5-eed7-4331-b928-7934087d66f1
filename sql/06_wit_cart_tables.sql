-- =============================================
-- WitMall v2.0 购物车服务表结构
-- 数据库: wit_cart
-- 表数量: 1张
-- 功能: 购物车管理
-- =============================================

USE `wit_cart`;

-- =============================================
-- 1. 购物车表
-- =============================================
CREATE TABLE IF NOT EXISTS `shopping_cart` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `shop_id` BIGINT NOT NULL COMMENT '店铺ID',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `product_code` VARCHAR(100) NOT NULL COMMENT '商品编码',
    `sku_code` VARCHAR(100) NOT NULL COMMENT 'SKU编码',
    `sku_name` VARCHAR(200) COMMENT 'SKU名称',
    `product_image` VARCHAR(255) COMMENT '商品图片',
    `product_category_id` BIGINT COMMENT '商品分类ID',
    `product_brand_id` BIGINT COMMENT '商品品牌ID',
    `spec_values` JSON COMMENT '规格值JSON对象',
    `price` DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    `quantity` INT NOT NULL COMMENT '数量',
    `total_amount` DECIMAL(10,2) NOT NULL COMMENT '小计金额',
    `weight` DECIMAL(8,2) DEFAULT 0.00 COMMENT '商品重量(kg)',
    `volume` DECIMAL(8,2) DEFAULT 0.00 COMMENT '商品体积(立方米)',
    `selected` TINYINT DEFAULT 1 COMMENT '是否选中：1-选中，0-未选中',
    `is_gift` TINYINT DEFAULT 0 COMMENT '是否赠品：1-是，0-否',
    `gift_integral` INT DEFAULT 0 COMMENT '赠送积分',
    `activity_id` BIGINT COMMENT '参与的活动ID',
    `activity_type` TINYINT COMMENT '活动类型：1-普通活动，2-秒杀，3-拼团',
    `coupon_id` BIGINT COMMENT '可用优惠券ID',
    `freight_template_id` BIGINT COMMENT '运费模板ID',
    `invalid_reason` VARCHAR(200) COMMENT '失效原因',
    `is_valid` TINYINT DEFAULT 1 COMMENT '是否有效：1-有效，0-失效',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_sku` (`user_id`, `sku_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_selected` (`selected`),
    KEY `idx_is_valid` (`is_valid`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';

-- 购物车服务表结构创建完成 - 共1张表

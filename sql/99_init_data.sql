-- =============================================
-- WitMall v2.0 完整初始化数据脚本
-- 包含所有必要的基础数据和测试数据
-- 涵盖102张表的完整初始化
-- =============================================

-- 设置基本参数
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 用户服务初始化数据 (wit_user)
-- =============================================
USE `wit_user`;

-- 插入用户等级数据
INSERT IGNORE INTO `user_level` (`id`, `level_name`, `level_code`, `min_points`, `max_points`, `discount_rate`, `privileges`, `icon`, `sort_order`, `status`, `create_by`) VALUES
(1, '普通会员', 'NORMAL', 0, 999, 1.00, '[]', '/icons/level-normal.png', 1, 1, 1),
(2, '银牌会员', 'SILVER', 1000, 4999, 0.98, '["free_shipping"]', '/icons/level-silver.png', 2, 1, 1),
(3, '金牌会员', 'GOLD', 5000, 19999, 0.95, '["free_shipping", "priority_service"]', '/icons/level-gold.png', 3, 1, 1),
(4, '钻石会员', 'DIAMOND', 20000, 999999, 0.90, '["free_shipping", "priority_service", "exclusive_discount"]', '/icons/level-diamond.png', 4, 1, 1);

-- 插入角色数据
INSERT IGNORE INTO `role` (`id`, `role_name`, `role_code`, `role_type`, `description`, `status`, `sort_order`, `create_by`) VALUES
(1, '超级管理员', 'SUPER_ADMIN', 1, '系统超级管理员，拥有所有权限', 1, 1, 1),
(2, '系统管理员', 'ADMIN', 1, '系统管理员，拥有大部分权限', 1, 2, 1),
(3, '运营人员', 'OPERATOR', 1, '运营人员，负责日常运营管理', 1, 3, 1),
(4, '客服人员', 'SERVICE', 1, '客服人员，负责客户服务', 1, 4, 1),
(5, '商家管理员', 'MERCHANT_ADMIN', 2, '商家管理员', 1, 5, 1),
(6, '商家员工', 'MERCHANT_STAFF', 2, '商家员工', 1, 6, 1),
(7, '代理商', 'AGENT', 3, '代理商', 1, 7, 1),
(8, '普通用户', 'USER', 1, '普通注册用户', 1, 8, 1);

-- 插入权限数据
INSERT IGNORE INTO `permission` (`id`, `permission_name`, `permission_code`, `parent_id`, `permission_type`, `path`, `icon`, `sort_order`, `status`, `create_by`) VALUES
(1, '系统管理', 'SYSTEM', 0, 1, '/system', 'system', 1, 1, 1),
(2, '用户管理', 'SYSTEM_USER', 1, 1, '/system/user', 'user', 1, 1, 1),
(3, '角色管理', 'SYSTEM_ROLE', 1, 1, '/system/role', 'role', 2, 1, 1),
(4, '权限管理', 'SYSTEM_PERMISSION', 1, 1, '/system/permission', 'permission', 3, 1, 1),
(5, '商品管理', 'PRODUCT', 0, 1, '/product', 'product', 2, 1, 1),
(6, '商品列表', 'PRODUCT_LIST', 5, 1, '/product/list', 'list', 1, 1, 1),
(7, '分类管理', 'PRODUCT_CATEGORY', 5, 1, '/product/category', 'category', 2, 1, 1),
(8, '品牌管理', 'PRODUCT_BRAND', 5, 1, '/product/brand', 'brand', 3, 1, 1),
(9, '订单管理', 'ORDER', 0, 1, '/order', 'order', 3, 1, 1),
(10, '订单列表', 'ORDER_LIST', 9, 1, '/order/list', 'list', 1, 1, 1),
(11, '售后管理', 'ORDER_AFTERSALE', 9, 1, '/order/aftersale', 'aftersale', 2, 1, 1),
(12, '营销管理', 'MARKETING', 0, 1, '/marketing', 'marketing', 4, 1, 1),
(13, '优惠券管理', 'MARKETING_COUPON', 12, 1, '/marketing/coupon', 'coupon', 1, 1, 1),
(14, '活动管理', 'MARKETING_ACTIVITY', 12, 1, '/marketing/activity', 'activity', 2, 1, 1);

-- 插入管理员用户
INSERT IGNORE INTO `user` (`id`, `username`, `password`, `salt`, `email`, `phone`, `nickname`, `user_type`, `level_id`, `status`, `create_by`) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '15991793921', '超级管理员', 4, 1, 1, 1),
(2, 'operator', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '13800138001', '运营人员', 4, 1, 1, 1),
(3, 'service', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '13800138002', '客服人员', 4, 1, 1, 1),
(4, 'testuser', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '13800138888', '测试用户', 1, 1, 1, 1),
(5, 'apple_merchant', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '***********', '苹果商家', 2, 2, 1, 1),
(6, 'huawei_merchant', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '***********', '华为商家', 2, 2, 1, 1),
(7, 'xiaomi_merchant', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '***********', '小米商家', 2, 2, 1, 1);

-- 插入用户角色关联
INSERT IGNORE INTO `user_role` (`user_id`, `role_id`, `create_by`) VALUES
(1, 1, 1), -- admin -> 超级管理员
(2, 3, 1), -- operator -> 运营人员
(3, 4, 1), -- service -> 客服人员
(4, 8, 1); -- testuser -> 普通用户

-- 插入角色权限关联（超级管理员拥有所有权限）
INSERT IGNORE INTO `role_permission` (`role_id`, `permission_id`, `create_by`)
SELECT 1, id, 1 FROM `permission` WHERE `status` = 1;

-- 插入运营人员权限
INSERT IGNORE INTO `role_permission` (`role_id`, `permission_id`, `create_by`) VALUES
(3, 5, 1), (3, 6, 1), (3, 7, 1), (3, 8, 1), -- 商品管理权限
(3, 9, 1), (3, 10, 1), (3, 11, 1), -- 订单管理权限
(3, 12, 1), (3, 13, 1), (3, 14, 1); -- 营销管理权限

-- 插入测试商家
INSERT IGNORE INTO `merchant` (`id`, `merchant_name`, `merchant_code`, `merchant_type`, `user_id`, `contact_name`, `contact_phone`, `contact_email`, `business_license`, `province_code`, `province_name`, `city_code`, `city_name`, `district_code`, `district_name`, `detail_address`, `description`, `status`, `create_by`) VALUES
(1, '苹果官方旗舰店', 'APPLE_OFFICIAL', 2, 5, '库克', '***********', '<EMAIL>', '91110000000000001X', '110000', '北京市', '110100', '东城区', '110101', '东华门街道', '北京市东城区东华门街道苹果大厦', '苹果公司官方授权旗舰店，专业销售iPhone、iPad、Mac等苹果全系列产品', 1, 1),
(2, '华为官方店', 'HUAWEI_OFFICIAL', 2, 6, '任正非', '***********', '<EMAIL>', '91440300000000002X', '440000', '广东省', '440300', '深圳市', '440305', '南山区', '深圳市南山区华为总部', '华为技术有限公司官方直营店，提供华为手机、笔记本、智能穿戴等产品', 1, 1),
(3, '小米专营店', 'XIAOMI_STORE', 2, 7, '雷军', '***********', '<EMAIL>', '91110000000000003X', '110000', '北京市', '110100', '海淀区', '110108', '中关村街道', '北京市海淀区中关村小米科技园', '小米生态链产品专营店，销售小米手机、智能家居、生活用品等', 1, 1);

-- 插入商家店铺
INSERT IGNORE INTO `merchant_shop` (`id`, `merchant_id`, `shop_name`, `shop_code`, `shop_type`, `logo`, `banner`, `description`, `keywords`, `service_phone`, `business_hours`, `notice`, `status`, `create_by`) VALUES
(1, 1, '苹果官方旗舰店', 'APPLE_FLAGSHIP', 1, '/images/shops/apple-logo.png', '/images/shops/apple-banner.jpg', '苹果公司官方授权店铺，正品保证，提供iPhone、iPad、Mac等全系列产品', 'iPhone,iPad,Mac,苹果,Apple', '************', '09:00-22:00', '欢迎来到苹果官方旗舰店！所有商品均为正品，享受官方保修服务。', 1, 1),
(2, 2, '华为官方店', 'HUAWEI_OFFICIAL', 1, '/images/shops/huawei-logo.png', '/images/shops/huawei-banner.jpg', '华为官方直营店，品质保障，提供华为手机、笔记本、智能穿戴等产品', '华为,Huawei,手机,笔记本,智能穿戴', '************', '09:00-22:00', '华为官方直营，品质保证！最新华为产品，尽在华为官方店。', 1, 1),
(3, 3, '小米专营店', 'XIAOMI_STORE', 3, '/images/shops/xiaomi-logo.png', '/images/shops/xiaomi-banner.jpg', '小米生态链产品专营店，提供小米手机、智能家居、生活用品等', '小米,Xiaomi,智能家居,生活用品', '************', '09:00-21:00', '小米生态链产品专营！为发烧而生，让科技更有温度。', 1, 1);

-- 先为代理商创建用户账户
INSERT IGNORE INTO `user` (`id`, `username`, `password`, `salt`, `email`, `phone`, `nickname`, `user_type`, `level_id`, `status`, `create_by`) VALUES
(8, 'agent_north', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '13900001001', '华北区总代理', 3, 1, 1, 1),
(9, 'agent_east', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '13900001002', '华东区总代理', 3, 1, 1, 1),
(10, 'agent_bj', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '13900001003', '北京分代理', 3, 1, 1, 1),
(11, 'agent_sh', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6tI7U1f2lZjWvkjlb8KzVzUtP.', 'salt123', '<EMAIL>', '13900001004', '上海分代理', 3, 1, 1, 1);

-- 插入代理商
INSERT IGNORE INTO `agent` (`id`, `agent_name`, `agent_code`, `user_id`, `parent_id`, `level`, `agent_type`, `contact_name`, `contact_phone`, `contact_email`, `province_code`, `province_name`, `city_code`, `city_name`, `commission_rate`, `status`, `create_by`) VALUES
(1, '华北区总代理', 'AGENT_NORTH', 8, 0, 1, 2, '张代理', '13900001001', '<EMAIL>', '110000', '北京市', '110100', '北京市', 0.0500, 1, 1),
(2, '华东区总代理', 'AGENT_EAST', 9, 0, 1, 2, '李代理', '13900001002', '<EMAIL>', '310000', '上海市', '310100', '上海市', 0.0500, 1, 1),
(3, '北京分代理', 'AGENT_BJ', 10, 1, 2, 1, '王分代', '13900001003', '<EMAIL>', '110000', '北京市', '110100', '北京市', 0.0300, 1, 1),
(4, '上海分代理', 'AGENT_SH', 11, 2, 2, 1, '赵分代', '13900001004', '<EMAIL>', '310000', '上海市', '310100', '上海市', 0.0300, 1, 1);

-- 插入代理商关系
INSERT IGNORE INTO `agent_relation` (`agent_id`, `parent_id`, `level`) VALUES
(3, 1, 1), -- 北京分代理 -> 华北区总代理
(4, 2, 1); -- 上海分代理 -> 华东区总代理

-- 插入用户地址
INSERT IGNORE INTO `user_address` (`id`, `user_id`, `receiver_name`, `receiver_phone`, `province_code`, `province_name`, `city_code`, `city_name`, `district_code`, `district_name`, `detail_address`, `postal_code`, `is_default`, `address_type`) VALUES
(1, 1, '管理员', '13800138000', '110000', '北京市', '110100', '东城区', '110101', '东华门街道', '北京市东城区东华门街道1号', '100001', 1, 1),
(2, 4, '测试用户', '13800138888', '310000', '上海市', '310100', '黄浦区', '310101', '南京东路街道', '上海市黄浦区南京东路100号', '200001', 1, 1),
(3, 5, '苹果商家', '***********', '110000', '北京市', '110100', '东城区', '110101', '东华门街道', '北京市东城区东华门街道苹果大厦', '100001', 1, 2),
(4, 6, '华为商家', '***********', '440000', '广东省', '440300', '深圳市', '440305', '南山区', '深圳市南山区华为总部', '518000', 1, 2),
(5, 7, '小米商家', '***********', '110000', '北京市', '110100', '海淀区', '110108', '中关村街道', '北京市海淀区中关村小米科技园', '100080', 1, 2);

-- =============================================
-- 商品服务初始化数据
-- =============================================
USE `wit_product`;

-- 插入测试分类
INSERT IGNORE INTO `category` (`id`, `category_name`, `category_code`, `parent_id`, `level`, `sort_order`, `status`, `create_by`) VALUES
(1, '电子产品', 'ELECTRONICS', 0, 1, 1, 1, 1),
(2, '服装鞋帽', 'CLOTHING', 0, 1, 2, 1, 1),
(3, '家居用品', 'HOME', 0, 1, 3, 1, 1),
(4, '图书音像', 'BOOKS', 0, 1, 4, 1, 1),
(10, '手机数码', 'MOBILE', 1, 2, 1, 1, 1),
(11, '电脑办公', 'COMPUTER', 1, 2, 2, 1, 1),
(12, '家用电器', 'APPLIANCE', 1, 2, 3, 1, 1),
(20, '男装', 'MEN_CLOTHING', 2, 2, 1, 1, 1),
(21, '女装', 'WOMEN_CLOTHING', 2, 2, 2, 1, 1),
(22, '童装', 'CHILDREN_CLOTHING', 2, 2, 3, 1, 1);

-- 插入测试品牌
INSERT IGNORE INTO `brand` (`id`, `brand_name`, `brand_code`, `brand_name_en`, `country`, `sort_order`, `is_hot`, `status`, `create_by`) VALUES
(1, '苹果', 'APPLE', 'Apple', '美国', 1, 1, 1, 1),
(2, '华为', 'HUAWEI', 'HUAWEI', '中国', 2, 1, 1, 1),
(3, '小米', 'XIAOMI', 'Xiaomi', '中国', 3, 1, 1, 1),
(4, '三星', 'SAMSUNG', 'Samsung', '韩国', 4, 1, 1, 1),
(5, '联想', 'LENOVO', 'Lenovo', '中国', 5, 0, 1, 1);

-- 插入分类品牌关联
INSERT IGNORE INTO `category_brand` (`category_id`, `brand_id`, `create_by`) VALUES
(10, 1, 1), (10, 2, 1), (10, 3, 1), (10, 4, 1), -- 手机数码
(11, 1, 1), (11, 5, 1); -- 电脑办公

-- 插入测试商品
INSERT IGNORE INTO `product` (`id`, `product_name`, `product_code`, `merchant_id`, `shop_id`, `category_id`, `brand_id`, `price`, `original_price`, `cost_price`, `main_image`, `detail`, `keywords`, `is_hot`, `is_new`, `status`, `create_by`) VALUES
(1, 'iPhone 15 Pro', 'IP15PRO001', 1, 1, 10, 1, 7999.00, 8999.00, 6500.00, '/images/iphone15pro.jpg', '<p>Apple iPhone 15 Pro 128GB 深空黑色，搭载A17 Pro芯片，支持5G网络，拍照更清晰，性能更强劲。</p>', 'iPhone,苹果,手机,5G', 1, 1, 1, 1),
(2, 'MacBook Pro 14英寸', 'MBP14001', 1, 1, 11, 1, 12999.00, 14999.00, 10500.00, '/images/macbookpro14.jpg', '<p>Apple MacBook Pro 14英寸 M3芯片，专业级性能，适合开发者和创意工作者使用。</p>', 'MacBook,苹果,笔记本,M3', 1, 1, 1, 1),
(3, '华为Mate 60 Pro', 'HWM60PRO001', 2, 2, 10, 2, 6999.00, 7999.00, 5800.00, '/images/mate60pro.jpg', '<p>华为Mate 60 Pro 512GB 雅川青，麒麟9000S芯片，支持卫星通话，拍照专业。</p>', '华为,Mate,手机,卫星通话', 1, 1, 1, 1);

-- 插入商品SKU
INSERT IGNORE INTO `product_sku` (`id`, `product_id`, `sku_code`, `sku_name`, `spec_values`, `price`, `status`, `create_by`) VALUES
(1, 1, 'IP15PRO001-128G-BLACK', 'iPhone 15 Pro 128GB 深空黑色', '{"颜色":"深空黑色","容量":"128GB"}', 7999.00, 1, 1),
(2, 1, 'IP15PRO001-256G-BLACK', 'iPhone 15 Pro 256GB 深空黑色', '{"颜色":"深空黑色","容量":"256GB"}', 8999.00, 1, 1),
(3, 2, 'MBP14001-512G-GRAY', 'MacBook Pro 14英寸 512GB 深空灰色', '{"颜色":"深空灰色","存储":"512GB"}', 12999.00, 1, 1),
(4, 3, 'HWM60PRO001-512G-GREEN', '华为Mate 60 Pro 512GB 雅川青', '{"颜色":"雅川青","容量":"512GB"}', 6999.00, 1, 1);

-- 插入运费模板
INSERT IGNORE INTO `freight_template` (`id`, `template_name`, `merchant_id`, `charge_type`, `first_amount`, `first_fee`, `continue_amount`, `continue_fee`, `free_amount`, `status`, `create_by`) VALUES
(1, '全国包邮模板', 1, 1, 1.00, 0.00, 1.00, 0.00, 0.00, 1, 1),
(2, '标准快递模板', 2, 1, 1.00, 10.00, 1.00, 5.00, 99.00, 1, 1),
(3, '重货物流模板', 3, 2, 1.00, 15.00, 1.00, 8.00, 199.00, 1, 1);

-- 插入商品服务
INSERT IGNORE INTO `product_service` (`id`, `service_name`, `service_code`, `service_icon`, `description`, `sort_order`, `status`, `create_by`) VALUES
(1, '7天无理由退货', 'RETURN_7DAYS', '/icons/return.png', '支持7天无理由退货', 1, 1, 1),
(2, '正品保证', 'GENUINE', '/icons/genuine.png', '100%正品保证', 2, 1, 1),
(3, '全国联保', 'WARRANTY', '/icons/warranty.png', '全国联保服务', 3, 1, 1),
(4, '免费配送', 'FREE_SHIPPING', '/icons/shipping.png', '免费配送到家', 4, 1, 1),
(5, '24小时发货', 'FAST_DELIVERY', '/icons/fast.png', '24小时内发货', 5, 1, 1);

-- 插入商品服务关联
INSERT IGNORE INTO `product_service_relation` (`product_id`, `service_id`, `create_by`) VALUES
(1, 1, 1), (1, 2, 1), (1, 3, 1), (1, 4, 1), (1, 5, 1), -- iPhone 15 Pro
(2, 1, 1), (2, 2, 1), (2, 3, 1), (2, 4, 1), (2, 5, 1), -- MacBook Pro
(3, 1, 1), (3, 2, 1), (3, 3, 1), (3, 5, 1); -- 华为Mate 60 Pro

-- 插入商品统计
INSERT IGNORE INTO `product_statistics` (`product_id`, `view_count`, `collect_count`, `cart_count`, `order_count`, `sale_count`, `comment_count`, `good_comment_count`) VALUES
(1, 1580, 89, 156, 45, 45, 23, 21),
(2, 890, 34, 67, 12, 12, 8, 7),
(3, 2340, 156, 234, 78, 78, 45, 42);

-- =============================================
-- 库存服务初始化数据
-- =============================================
USE `wit_inventory`;

-- 插入测试仓库
INSERT IGNORE INTO `warehouse` (`id`, `warehouse_name`, `warehouse_code`, `warehouse_type`, `contact_name`, `contact_phone`, `province_code`, `province_name`, `city_code`, `city_name`, `district_code`, `district_name`, `detail_address`, `status`, `create_by`) VALUES
(1, '北京总仓', 'BJ_MAIN', 1, '张三', '13800138000', '110000', '北京市', '110100', '东城区', '110101', '东华门街道', '北京市东城区东华门街道1号', 1, 1),
(2, '上海分仓', 'SH_BRANCH', 1, '李四', '13800138001', '310000', '上海市', '310100', '黄浦区', '310101', '南京东路街道', '上海市黄浦区南京东路100号', 1, 1);

-- 插入库区
INSERT IGNORE INTO `warehouse_area` (`id`, `warehouse_id`, `area_name`, `area_code`, `area_type`, `capacity`, `description`, `status`, `create_by`) VALUES
(1, 1, '普通存储区A', 'BJ_AREA_A', 1, 1000.00, '北京仓普通存储区域A', 1, 1),
(2, 1, '普通存储区B', 'BJ_AREA_B', 1, 1000.00, '北京仓普通存储区域B', 1, 1),
(3, 2, '普通存储区A', 'SH_AREA_A', 1, 800.00, '上海仓普通存储区域A', 1, 1),
(4, 2, '冷藏区', 'SH_COLD', 2, 200.00, '上海仓冷藏区域', 1, 1);

-- 插入测试库存
INSERT IGNORE INTO `product_stock` (`id`, `product_id`, `sku_id`, `warehouse_id`, `area_id`, `stock`, `available_stock`, `warning_stock`, `cost_price`, `create_by`) VALUES
(1, 1, 1, 1, 1, 100, 95, 10, 6000.00, 1),
(2, 1, 2, 1, 1, 50, 48, 10, 6800.00, 1),
(3, 2, 3, 1, 2, 30, 28, 5, 10000.00, 1),
(4, 3, 4, 2, 3, 80, 75, 10, 5500.00, 1);

-- 插入供应商
INSERT IGNORE INTO `supplier` (`id`, `supplier_code`, `supplier_name`, `supplier_type`, `contact_name`, `contact_phone`, `contact_email`, `business_license`, `province_code`, `province_name`, `city_code`, `city_name`, `district_code`, `district_name`, `detail_address`, `credit_level`, `cooperation_status`, `status`, `create_by`) VALUES
(1, 'SUP001', '苹果供应链', 1, '供应经理', '***********', '<EMAIL>', '91110000SUP000001X', '110000', '北京市', '110100', '东城区', '110101', '东华门街道', '北京市东城区供应链大厦', 1, 1, 1, 1),
(2, 'SUP002', '华为供应商', 1, '采购经理', '***********', '<EMAIL>', '91440300SUP000002X', '440000', '广东省', '440300', '深圳市', '440305', '南山区', '深圳市南山区供应基地', 1, 1, 1, 1),
(3, 'SUP003', '小米生态链', 2, '渠道经理', '***********', '<EMAIL>', '91110000SUP000003X', '110000', '北京市', '110100', '海淀区', '110108', '中关村街道', '北京市海淀区生态园', 2, 1, 1, 1);

-- 插入库存批次
INSERT IGNORE INTO `stock_batch` (`id`, `batch_no`, `product_id`, `sku_id`, `warehouse_id`, `area_id`, `supplier_id`, `batch_quantity`, `remain_quantity`, `cost_price`, `production_date`, `expire_date`, `quality_status`, `create_by`) VALUES
(1, 'BATCH202401001', 1, 1, 1, 1, 1, 100, 95, 6000.00, '2024-01-15', '2026-01-15', 1, 1),
(2, 'BATCH202401002', 1, 2, 1, 1, 1, 50, 48, 6800.00, '2024-01-15', '2026-01-15', 1, 1),
(3, 'BATCH202401003', 2, 3, 1, 2, 1, 30, 28, 10000.00, '2024-01-20', '2026-01-20', 1, 1),
(4, 'BATCH202401004', 3, 4, 2, 3, 2, 80, 75, 5500.00, '2024-01-25', '2026-01-25', 1, 1);

-- =============================================
-- 支付服务初始化数据
-- =============================================
USE `wit_payment`;

-- 插入支付方式
INSERT IGNORE INTO `payment_method` (`id`, `method_name`, `method_code`, `method_type`, `channel_code`, `icon`, `description`, `fee_rate`, `min_amount`, `max_amount`, `is_enabled`, `sort_order`, `status`, `create_by`) VALUES
(1, '微信支付', 'WECHAT_PAY', 1, 'WECHAT', '/images/payment/wechat.png', '微信支付，安全便捷', 0.0060, 0.01, 50000.00, 1, 1, 1, 1),
(2, '支付宝', 'ALIPAY', 1, 'ALIPAY', '/images/payment/alipay.png', '支付宝支付，快速到账', 0.0060, 0.01, 50000.00, 1, 2, 1, 1),
(3, '余额支付', 'BALANCE_PAY', 3, 'BALANCE', '/images/payment/balance.png', '账户余额支付，无手续费', 0.0000, 0.01, 999999.99, 1, 3, 1, 1),
(4, '银联支付', 'UNION_PAY', 1, 'UNIONPAY', '/images/payment/unionpay.png', '银联在线支付，银行直连', 0.0080, 0.01, 50000.00, 1, 4, 1, 1);

-- 插入用户账户
INSERT IGNORE INTO `user_account` (`id`, `user_id`, `account_type`, `balance`, `available_amount`, `status`, `create_by`) VALUES
(1, 1, 1, 10000.00, 10000.00, 1, 1), -- admin余额账户
(2, 4, 1, 1000.00, 1000.00, 1, 1),   -- testuser余额账户
(3, 1, 2, 5000, 5000, 1, 1),         -- admin积分账户
(4, 4, 2, 100, 100, 1, 1);           -- testuser积分账户

-- =============================================
-- 订单服务初始化数据 (wit_order)
-- =============================================
USE `wit_order`;

-- 插入物流公司
INSERT IGNORE INTO `logistics_company` (`id`, `company_name`, `company_code`, `company_logo`, `contact_name`, `contact_phone`, `website`, `is_support_query`, `sort_order`, `status`, `create_by`) VALUES
(1, '顺丰速运', 'SF', '/images/logistics/sf.png', '顺丰客服', '95338', 'https://www.sf-express.com', 1, 1, 1, 1),
(2, '中通快递', 'ZTO', '/images/logistics/zto.png', '中通客服', '95311', 'https://www.zto.com', 1, 2, 1, 1),
(3, '圆通速递', 'YTO', '/images/logistics/yto.png', '圆通客服', '95554', 'https://www.yto.net.cn', 1, 3, 1, 1),
(4, '申通快递', 'STO', '/images/logistics/sto.png', '申通客服', '95543', 'https://www.sto.cn', 1, 4, 1, 1),
(5, '韵达速递', 'YUNDA', '/images/logistics/yunda.png', '韵达客服', '95546', 'https://www.yunda.com', 1, 5, 1, 1);

-- 插入测试订单
INSERT IGNORE INTO `order_info` (`id`, `order_no`, `user_id`, `merchant_id`, `shop_id`, `order_type`, `order_source`, `order_status`, `pay_status`, `delivery_status`, `total_amount`, `product_amount`, `freight_amount`, `pay_amount`, `total_quantity`, `buyer_message`, `create_by`) VALUES
(1, 'WM202401150001', 4, 1, 1, 1, 4, 5, 1, 3, 7999.00, 7999.00, 0.00, 7999.00, 1, '请尽快发货，谢谢！', 4),
(2, 'WM202401150002', 4, 2, 2, 1, 4, 2, 1, 0, 6999.00, 6999.00, 0.00, 6999.00, 1, '', 4);

-- 插入订单项
INSERT IGNORE INTO `order_item` (`id`, `order_id`, `order_no`, `product_id`, `sku_id`, `product_name`, `product_code`, `sku_code`, `sku_name`, `product_image`, `spec_values`, `price`, `quantity`, `total_amount`, `real_amount`) VALUES
(1, 1, 'WM202401150001', 1, 1, 'iPhone 15 Pro', 'IP15PRO001', 'IP15PRO001-128G-BLACK', 'iPhone 15 Pro 128GB 深空黑色', '/images/iphone15pro.jpg', '{"颜色":"深空黑色","容量":"128GB"}', 7999.00, 1, 7999.00, 7999.00),
(2, 2, 'WM202401150002', 3, 4, '华为Mate 60 Pro', 'HWM60PRO001', 'HWM60PRO001-512G-GREEN', '华为Mate 60 Pro 512GB 雅川青', '/images/mate60pro.jpg', '{"颜色":"雅川青","容量":"512GB"}', 6999.00, 1, 6999.00, 6999.00);

-- 插入订单收货地址
INSERT IGNORE INTO `order_address` (`id`, `order_id`, `order_no`, `receiver_name`, `receiver_phone`, `province_code`, `province_name`, `city_code`, `city_name`, `district_code`, `district_name`, `detail_address`, `postal_code`) VALUES
(1, 1, 'WM202401150001', '测试用户', '13800138888', '310000', '上海市', '310100', '黄浦区', '310101', '南京东路街道', '上海市黄浦区南京东路100号', '200001'),
(2, 2, 'WM202401150002', '测试用户', '13800138888', '310000', '上海市', '310100', '黄浦区', '310101', '南京东路街道', '上海市黄浦区南京东路100号', '200001');

-- 插入订单物流（已发货的订单）
INSERT IGNORE INTO `order_logistics` (`id`, `order_id`, `order_no`, `logistics_company_id`, `logistics_company_name`, `logistics_no`, `sender_name`, `sender_phone`, `sender_address`, `receiver_name`, `receiver_phone`, `receiver_address`, `logistics_status`, `freight_amount`, `create_by`) VALUES
(1, 1, 'WM202401150001', 1, '顺丰速运', 'SF1234567890123', '苹果仓库', '************', '北京市东城区东华门街道苹果大厦', '测试用户', '13800138888', '上海市黄浦区南京东路100号', 4, 0.00, 1);

-- =============================================
-- 营销服务初始化数据
-- =============================================
USE `wit_marketing`;

-- =============================================
-- 购物车服务初始化数据 (wit_cart)
-- =============================================
USE `wit_cart`;

-- 插入测试购物车数据
INSERT IGNORE INTO `shopping_cart` (`id`, `user_id`, `product_id`, `sku_id`, `merchant_id`, `shop_id`, `product_name`, `product_code`, `sku_code`, `sku_name`, `product_image`, `spec_values`, `price`, `quantity`, `total_amount`, `selected`) VALUES
(1, 4, 2, 3, 1, 1, 'MacBook Pro 14英寸', 'MBP14001', 'MBP14001-512G-GRAY', 'MacBook Pro 14英寸 512GB 深空灰色', '/images/macbookpro14.jpg', '{"颜色":"深空灰色","存储":"512GB"}', 12999.00, 1, 12999.00, 1);

-- =============================================
-- 营销服务初始化数据 (wit_marketing)
-- =============================================
USE `wit_marketing`;

-- 插入测试优惠券
INSERT IGNORE INTO `coupon` (`id`, `coupon_name`, `coupon_code`, `coupon_type`, `discount_type`, `discount_value`, `min_amount`, `total_quantity`, `per_limit`, `start_time`, `end_time`, `description`, `status`, `create_by`) VALUES
(1, '新用户专享券', 'NEW_USER_50', 1, 1, 50.00, 100.00, 1000, 1, '2024-01-01 00:00:00', '2024-12-31 23:59:59', '新用户注册专享50元优惠券', 1, 1),
(2, '满减优惠券', 'FULL_REDUCE_100', 1, 1, 100.00, 500.00, 500, 2, '2024-01-01 00:00:00', '2024-12-31 23:59:59', '满500减100优惠券', 1, 1),
(3, '折扣优惠券', 'DISCOUNT_20', 2, 2, 0.80, 200.00, 200, 1, '2024-01-01 00:00:00', '2024-12-31 23:59:59', '满200享8折优惠', 1, 1);

-- 插入用户优惠券（给测试用户发放优惠券）
INSERT IGNORE INTO `user_coupon` (`id`, `user_id`, `coupon_id`, `coupon_code`, `coupon_name`, `coupon_type`, `discount_type`, `discount_value`, `min_amount`, `use_scope`, `receive_type`, `status`, `start_time`, `end_time`) VALUES
(1, 4, 1, 'NEW_USER_50', '新用户专享券', 1, 1, 50.00, 100.00, 1, 2, 1, '2024-01-01 00:00:00', '2024-12-31 23:59:59'),
(2, 4, 2, 'FULL_REDUCE_100', '满减优惠券', 1, 1, 100.00, 500.00, 1, 2, 1, '2024-01-01 00:00:00', '2024-12-31 23:59:59');

-- 插入营销活动
INSERT IGNORE INTO `marketing_activity` (`id`, `activity_name`, `activity_code`, `activity_type`, `activity_desc`, `start_time`, `end_time`, `total_budget`, `priority`, `status`, `create_by`) VALUES
(1, '春节大促', 'SPRING_FESTIVAL_2024', 1, '春节期间全场满减活动', '2024-02-01 00:00:00', '2024-02-29 23:59:59', 100000.00, 10, 1, 1),
(2, '618购物节', 'SHOPPING_618_2024', 2, '618购物节折扣活动', '2024-06-01 00:00:00', '2024-06-18 23:59:59', 200000.00, 20, 1, 1);

-- 插入秒杀活动
INSERT IGNORE INTO `seckill_activity` (`id`, `activity_name`, `activity_date`, `start_time`, `end_time`, `status`, `description`, `create_by`) VALUES
(1, '每日秒杀10点场', '2024-01-20', '10:00:00', '10:59:59', 2, '每日10点秒杀专场', 1),
(2, '每日秒杀20点场', '2024-01-20', '20:00:00', '20:59:59', 1, '每日20点秒杀专场', 1);

-- 插入秒杀商品
INSERT IGNORE INTO `seckill_product` (`id`, `activity_id`, `product_id`, `sku_id`, `product_name`, `product_image`, `original_price`, `seckill_price`, `seckill_stock`, `per_limit`, `sort_order`, `status`, `create_by`) VALUES
(1, 2, 1, 1, 'iPhone 15 Pro', '/images/iphone15pro.jpg', 7999.00, 7599.00, 10, 1, 1, 1, 1),
(2, 2, 3, 4, '华为Mate 60 Pro', '/images/mate60pro.jpg', 6999.00, 6599.00, 20, 1, 2, 1, 1);

-- =============================================
-- 系统服务初始化数据
-- =============================================
USE `wit_system`;

-- =============================================
-- 评价服务初始化数据 (wit_review)
-- =============================================
USE `wit_review`;

-- 插入商品评价
INSERT IGNORE INTO `product_review` (`id`, `order_id`, `order_no`, `order_item_id`, `user_id`, `product_id`, `sku_id`, `merchant_id`, `product_name`, `product_image`, `spec_values`, `review_score`, `review_content`, `logistics_score`, `service_score`, `status`) VALUES
(1, 1, 'WM202401150001', 1, 4, 1, 1, 1, 'iPhone 15 Pro', '/images/iphone15pro.jpg', '{"颜色":"深空黑色","容量":"128GB"}', 5, '手机很不错，系统流畅，拍照效果很好！', 5, 5, 1);

-- 插入评价回复
INSERT IGNORE INTO `review_reply` (`id`, `review_id`, `parent_id`, `reply_type`, `reply_user_id`, `reply_content`, `status`) VALUES
(1, 1, 0, 1, 1, '感谢您的好评，我们会继续努力提供更好的产品和服务！', 1);

-- =============================================
-- 通知服务初始化数据 (wit_notification)
-- =============================================
USE `wit_notification`;

-- 插入消息模板
INSERT IGNORE INTO `message_template` (`id`, `template_name`, `template_code`, `template_type`, `title`, `content`, `variables`, `status`, `create_by`) VALUES
(1, '订单支付成功通知', 'ORDER_PAY_SUCCESS', 1, '订单支付成功', '您的订单${orderNo}已支付成功，金额${amount}元，我们将尽快为您发货。', '["orderNo", "amount"]', 1, 1),
(2, '订单发货通知', 'ORDER_SHIPPED', 1, '订单已发货', '您的订单${orderNo}已发货，物流公司：${logisticsCompany}，运单号：${logisticsNo}', '["orderNo", "logisticsCompany", "logisticsNo"]', 1, 1),
(3, '验证码短信', 'SMS_VERIFY_CODE', 2, '', '您的验证码是${code}，5分钟内有效，请勿泄露给他人。', '["code"]', 1, 1);

-- =============================================
-- 文件服务初始化数据 (wit_file)
-- =============================================
USE `wit_file`;

-- 插入文件分类
INSERT IGNORE INTO `file_category` (`id`, `category_name`, `parent_id`, `level`, `sort_order`, `description`, `status`, `create_by`) VALUES
(1, '商品图片', 0, 1, 1, '商品相关图片文件', 1, 1),
(2, '用户头像', 0, 1, 2, '用户头像文件', 1, 1),
(3, '系统图标', 0, 1, 3, '系统图标文件', 1, 1),
(4, '营销素材', 0, 1, 4, '营销活动素材', 1, 1);

-- =============================================
-- 系统服务初始化数据 (wit_system)
-- =============================================
USE `wit_system`;

-- 插入系统配置
INSERT IGNORE INTO `system_config` (`id`, `config_key`, `config_value`, `config_type`, `config_group`, `config_name`, `description`, `status`, `create_by`) VALUES
(1, 'site.name', 'WitMall商城', 1, 'site', '网站名称', '网站名称配置', 1, 1),
(2, 'site.logo', '/images/logo.png', 1, 'site', '网站Logo', '网站Logo配置', 1, 1),
(3, 'site.keywords', 'WitMall,电商,商城', 1, 'site', '网站关键词', 'SEO关键词配置', 1, 1),
(4, 'site.description', 'WitMall - 专业的B2B2C电商平台', 1, 'site', '网站描述', 'SEO描述配置', 1, 1),
(5, 'order.auto_cancel_time', '30', 2, 'order', '订单自动取消时间', '未支付订单自动取消时间（分钟）', 1, 1),
(6, 'order.auto_receive_time', '7', 2, 'order', '订单自动收货时间', '发货后自动收货时间（天）', 1, 1),
(7, 'sms.daily_limit', '10', 2, 'sms', '短信日发送限制', '每个手机号每日短信发送限制', 1, 1),
(8, 'upload.max_size', '10485760', 2, 'upload', '文件上传大小限制', '文件上传最大大小（字节）', 1, 1);

-- 插入字典类型
INSERT IGNORE INTO `dict_type` (`id`, `dict_name`, `dict_type`, `description`, `is_system`, `sort_order`, `status`, `create_by`) VALUES
(1, '用户性别', 'user_gender', '用户性别字典', 1, 1, 1, 1),
(2, '订单状态', 'order_status', '订单状态字典', 1, 2, 1, 1),
(3, '支付状态', 'pay_status', '支付状态字典', 1, 3, 1, 1),
(4, '物流状态', 'logistics_status', '物流状态字典', 1, 4, 1, 1);

-- 插入字典数据
INSERT IGNORE INTO `dict_data` (`id`, `dict_type_id`, `dict_type`, `dict_label`, `dict_value`, `dict_color`, `sort_order`, `status`, `create_by`) VALUES
(1, 1, 'user_gender', '未知', '0', '', 1, 1, 1),
(2, 1, 'user_gender', '男', '1', 'blue', 2, 1, 1),
(3, 1, 'user_gender', '女', '2', 'pink', 3, 1, 1),
(4, 2, 'order_status', '待付款', '1', 'orange', 1, 1, 1),
(5, 2, 'order_status', '待发货', '2', 'blue', 2, 1, 1),
(6, 2, 'order_status', '待收货', '3', 'cyan', 3, 1, 1),
(7, 2, 'order_status', '待评价', '4', 'purple', 4, 1, 1),
(8, 2, 'order_status', '已完成', '5', 'green', 5, 1, 1),
(9, 2, 'order_status', '已取消', '6', 'red', 6, 1, 1);

-- =============================================
-- 定时任务服务初始化数据 (wit_schedule)
-- =============================================
USE `wit_schedule`;

-- 插入定时任务
INSERT IGNORE INTO `schedule_job` (`id`, `job_name`, `job_group`, `job_class`, `job_method`, `cron_expression`, `description`, `status`, `create_by`) VALUES
(1, '订单自动取消任务', 'ORDER', 'com.witmall.schedule.OrderCancelJob', 'execute', '0 */5 * * * ?', '每5分钟检查并取消超时未支付订单', 1, 1),
(2, '订单自动收货任务', 'ORDER', 'com.witmall.schedule.OrderReceiveJob', 'execute', '0 0 2 * * ?', '每天凌晨2点自动确认收货', 1, 1),
(3, '数据统计任务', 'ANALYTICS', 'com.witmall.schedule.DataStatisticsJob', 'execute', '0 30 1 * * ?', '每天凌晨1点30分统计数据', 1, 1);

-- =============================================
-- 数据分析服务初始化数据 (wit_analytics)
-- =============================================
USE `wit_analytics`;

-- 插入销售统计数据（示例数据）
INSERT IGNORE INTO `sales_statistics` (`id`, `stat_date`, `stat_type`, `order_count`, `order_amount`, `paid_count`, `paid_amount`, `product_sales`, `new_user_count`, `active_user_count`, `conversion_rate`, `avg_order_amount`) VALUES
(1, '2024-01-15', 1, 25, 89750.00, 23, 85250.00, 25, 5, 18, 0.9200, 3706.52),
(2, '2024-01-16', 1, 32, 125600.00, 30, 118900.00, 32, 8, 24, 0.9375, 3965.63),
(3, '2024-01-17', 1, 28, 98400.00, 26, 91200.00, 28, 3, 21, 0.9286, 3507.69);

-- 插入用户统计数据
INSERT IGNORE INTO `user_statistics` (`id`, `stat_date`, `stat_type`, `total_user_count`, `new_user_count`, `active_user_count`, `login_user_count`, `order_user_count`, `paid_user_count`, `retention_rate_1d`, `avg_session_duration`, `avg_page_views`) VALUES
(1, '2024-01-15', 1, 1005, 5, 18, 45, 12, 11, 0.8000, 1800, 8.50),
(2, '2024-01-16', 1, 1013, 8, 24, 52, 15, 14, 0.7500, 1950, 9.20),
(3, '2024-01-17', 1, 1016, 3, 21, 38, 13, 12, 0.6667, 1720, 7.80);

-- =============================================
-- 搜索服务初始化数据 (wit_search)
-- =============================================
USE `wit_search`;

-- 插入搜索关键词
INSERT IGNORE INTO `search_keyword` (`id`, `keyword`, `keyword_type`, `search_count`, `result_count`, `click_count`, `conversion_count`, `click_rate`, `conversion_rate`, `is_hot`, `sort_order`, `first_search_time`, `last_search_time`) VALUES
(1, 'iPhone', 1, 1580, 25, 456, 89, 0.2886, 0.1951, 1, 1, '2024-01-01 10:00:00', '2024-01-17 15:30:00'),
(2, '华为', 1, 1234, 18, 345, 67, 0.2796, 0.1942, 1, 2, '2024-01-01 11:00:00', '2024-01-17 14:20:00'),
(3, '小米', 1, 987, 22, 234, 45, 0.2371, 0.1923, 1, 3, '2024-01-01 12:00:00', '2024-01-17 13:10:00'),
(4, 'MacBook', 1, 756, 8, 189, 23, 0.2500, 0.1217, 0, 4, '2024-01-02 09:00:00', '2024-01-17 16:45:00');

-- 插入热门搜索
INSERT IGNORE INTO `hot_search` (`id`, `keyword`, `search_count`, `trend_type`, `rank_position`, `last_rank_position`, `rank_change`, `heat_score`, `stat_date`, `status`) VALUES
(1, 'iPhone', 1580, 1, 1, 2, 1, 95.80, '2024-01-17', 1),
(2, '华为', 1234, 2, 2, 1, -1, 87.60, '2024-01-17', 1),
(3, '小米', 987, 3, 3, 3, 0, 76.40, '2024-01-17', 1),
(4, 'MacBook', 756, 1, 4, 5, 1, 68.20, '2024-01-17', 1);

-- =============================================
-- 推荐服务初始化数据 (wit_recommendation)
-- =============================================
USE `wit_recommendation`;

-- 插入用户偏好
INSERT IGNORE INTO `user_preference` (`id`, `user_id`, `preference_type`, `preference_key`, `preference_value`, `preference_score`, `behavior_count`, `last_behavior_time`, `weight`, `status`) VALUES
(1, 4, 1, '10', '手机数码', 0.8500, 15, '2024-01-17 15:30:00', 1.0000, 1),
(2, 4, 2, '1', '苹果', 0.7200, 8, '2024-01-17 14:20:00', 0.9000, 1),
(3, 4, 2, '2', '华为', 0.6800, 7, '2024-01-17 13:10:00', 0.8500, 1),
(4, 4, 3, '5000-10000', '5000-10000元', 0.9000, 12, '2024-01-17 16:00:00', 1.0000, 1);

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示初始化完成信息
SELECT '🎉 WitMall v2.0 完整初始化数据插入完成！' as message;
SELECT '📊 已创建17个微服务数据库，包含102张表' as database_info;
SELECT '👤 管理员账号：admin / 123456' as admin_info;
SELECT '👤 运营账号：operator / 123456' as operator_info;
SELECT '👤 客服账号：service / 123456' as service_info;
SELECT '👤 测试账号：testuser / 123456' as user_info;
SELECT '🏪 测试商家：苹果官方旗舰店、华为官方店、小米专营店' as merchant_info;
SELECT '📦 测试商品：iPhone 15 Pro、MacBook Pro、华为Mate 60 Pro' as product_info;
SELECT '🎫 测试优惠券：新用户专享券、满减优惠券、折扣优惠券' as coupon_info;
SELECT '📊 包含完整的测试数据：用户、商品、订单、库存、营销等' as data_info;
SELECT '🔧 可以开始启动微服务项目了！' as next_step;

-- =============================================
-- WitMall v2.0 完整执行脚本
-- 按顺序执行所有SQL文件，创建完整的102张表
-- =============================================

-- 设置基本参数
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
-- MySQL 8.0兼容设置
SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- =============================================
-- 执行顺序说明：
-- 1. 创建数据库
-- 2. 按服务顺序创建表结构
-- 3. 插入初始化数据
-- =============================================

SELECT '🚀 开始执行WitMall v2.0数据库初始化...' as message;

-- 1. 创建所有数据库
SELECT '📊 第1步：创建17个微服务数据库...' as step;
SOURCE 00_create_databases.sql;

-- 2. 创建用户服务表（12张）
SELECT '👤 第2步：创建用户服务表结构（12张表）...' as step;
SOURCE 01_wit_user_tables.sql;

-- 3. 创建商品服务表（15张）
SELECT '📦 第3步：创建商品服务表结构（15张表）...' as step;
SOURCE 02_wit_product_tables.sql;

-- 4. 创建库存服务表（18张）
SELECT '📋 第4步：创建库存服务表结构（18张表）...' as step;
SOURCE 03_wit_inventory_tables.sql;

-- 5. 创建订单服务表（12张）
SELECT '🛒 第5步：创建订单服务表结构（12张表）...' as step;
SOURCE 04_wit_order_tables.sql;

-- 6. 创建支付服务表（12张）
SELECT '💳 第6步：创建支付服务表结构（12张表）...' as step;
SOURCE 05_wit_payment_tables.sql;

-- 7. 创建购物车服务表（1张）
SELECT '🛍️ 第7步：创建购物车服务表结构（1张表）...' as step;
SOURCE 06_wit_cart_tables.sql;

-- 8. 创建营销服务表（8张）
SELECT '🎯 第8步：创建营销服务表结构（8张表）...' as step;
SOURCE 07_wit_marketing_tables.sql;

-- 9. 创建评价服务表（2张）
SELECT '⭐ 第9步：创建评价服务表结构（2张表）...' as step;
SOURCE 08_wit_review_tables.sql;

-- 10. 创建通知服务表（3张）
SELECT '📢 第10步：创建通知服务表结构（3张表）...' as step;
SOURCE 09_wit_notification_tables.sql;

-- 11. 创建文件服务表（2张）
SELECT '📁 第11步：创建文件服务表结构（2张表）...' as step;
SOURCE 10_wit_file_tables.sql;

-- 12. 创建系统服务表（4张）
SELECT '⚙️ 第12步：创建系统服务表结构（4张表）...' as step;
SOURCE 11_wit_system_tables.sql;

-- 13. 创建定时任务服务表（3张）
SELECT '⏰ 第13步：创建定时任务服务表结构（3张表）...' as step;
SOURCE 12_wit_schedule_tables.sql;

-- 14. 创建数据分析服务表（5张）
SELECT '📈 第14步：创建数据分析服务表结构（5张表）...' as step;
SOURCE 13_wit_analytics_tables.sql;

-- 15. 创建搜索服务表（3张）
SELECT '🔍 第15步：创建搜索服务表结构（3张表）...' as step;
SOURCE 14_wit_search_tables.sql;

-- 16. 创建推荐服务表（2张）
SELECT '🎯 第16步：创建推荐服务表结构（2张表）...' as step;
SOURCE 15_wit_recommendation_tables.sql;

-- 17. 插入初始化数据
SELECT '📝 第17步：插入初始化数据...' as step;
SOURCE 99_init_data.sql;

-- 恢复设置
SET FOREIGN_KEY_CHECKS = 1;

-- 显示完成信息
SELECT '🎉 WitMall v2.0数据库初始化完成！' as message;
SELECT '📊 已创建17个微服务数据库，包含102张表' as database_info;
SELECT '👤 默认管理员账号：admin / 123456' as admin_info;
SELECT '👤 默认测试账号：testuser / 123456' as user_info;
SELECT '🔧 可以开始启动微服务项目了！' as next_step;

-- 验证表数量
SELECT '📋 验证表数量：' as verify_title;
SELECT COUNT(*) as total_tables FROM information_schema.TABLES WHERE TABLE_SCHEMA LIKE 'wit_%';

SELECT '📊 各数据库表数量统计：' as stats_title;
SELECT 
    TABLE_SCHEMA as database_name, 
    COUNT(*) as table_count 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA LIKE 'wit_%' 
GROUP BY TABLE_SCHEMA 
ORDER BY table_count DESC;

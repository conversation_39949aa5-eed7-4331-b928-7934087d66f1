-- =============================================
-- WitMall v2.0 库存服务表结构
-- 数据库: wit_inventory
-- 表数量: 18张
-- 功能: 库存管理、仓库管理、出入库管理、盘点管理
-- =============================================

USE `wit_inventory`;

-- =============================================
-- 1. 仓库表
-- =============================================
CREATE TABLE IF NOT EXISTS `warehouse` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `warehouse_name` VARCHAR(100) NOT NULL COMMENT '仓库名称',
    `warehouse_code` VARCHAR(50) NOT NULL COMMENT '仓库编码',
    `warehouse_type` TINYINT DEFAULT 1 COMMENT '仓库类型：1-自营仓库，2-第三方仓库',
    `merchant_id` BIGINT COMMENT '商家ID，自营仓库为空',
    `contact_name` VARCHAR(50) NOT NULL COMMENT '联系人',
    `contact_phone` VARCHAR(20) NOT NULL COMMENT '联系电话',
    `province_code` VARCHAR(10) NOT NULL COMMENT '省份编码',
    `province_name` VARCHAR(50) NOT NULL COMMENT '省份名称',
    `city_code` VARCHAR(10) NOT NULL COMMENT '城市编码',
    `city_name` VARCHAR(50) NOT NULL COMMENT '城市名称',
    `district_code` VARCHAR(10) NOT NULL COMMENT '区县编码',
    `district_name` VARCHAR(50) NOT NULL COMMENT '区县名称',
    `detail_address` VARCHAR(200) NOT NULL COMMENT '详细地址',
    `postal_code` VARCHAR(10) COMMENT '邮政编码',
    `longitude` DECIMAL(10,7) COMMENT '经度',
    `latitude` DECIMAL(10,7) COMMENT '纬度',
    `area` DECIMAL(10,2) COMMENT '仓库面积(平方米)',
    `capacity` DECIMAL(10,2) COMMENT '仓库容量(立方米)',
    `description` TEXT COMMENT '仓库描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_warehouse_code` (`warehouse_code`),
    KEY `idx_warehouse_type` (`warehouse_type`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='仓库表';

-- =============================================
-- 2. 库区表
-- =============================================
CREATE TABLE IF NOT EXISTS `warehouse_area` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID',
    `area_name` VARCHAR(100) NOT NULL COMMENT '库区名称',
    `area_code` VARCHAR(50) NOT NULL COMMENT '库区编码',
    `area_type` TINYINT DEFAULT 1 COMMENT '库区类型：1-普通区，2-冷藏区，3-冷冻区，4-危险品区',
    `temperature_min` DECIMAL(5,2) COMMENT '最低温度',
    `temperature_max` DECIMAL(5,2) COMMENT '最高温度',
    `humidity_min` DECIMAL(5,2) COMMENT '最低湿度',
    `humidity_max` DECIMAL(5,2) COMMENT '最高湿度',
    `capacity` DECIMAL(10,2) COMMENT '容量(立方米)',
    `description` TEXT COMMENT '库区描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_area_code` (`area_code`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_area_type` (`area_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库区表';

-- =============================================
-- 3. 商品库存表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_stock` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID',
    `area_id` BIGINT COMMENT '库区ID',
    `stock` INT DEFAULT 0 COMMENT '库存数量',
    `available_stock` INT DEFAULT 0 COMMENT '可用库存',
    `locked_stock` INT DEFAULT 0 COMMENT '锁定库存',
    `in_transit_stock` INT DEFAULT 0 COMMENT '在途库存',
    `warning_stock` INT DEFAULT 10 COMMENT '预警库存',
    `max_stock` INT DEFAULT 0 COMMENT '最大库存，0表示不限制',
    `cost_price` DECIMAL(10,2) DEFAULT 0.00 COMMENT '成本价',
    `last_in_time` DATETIME COMMENT '最后入库时间',
    `last_out_time` DATETIME COMMENT '最后出库时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_sku_warehouse` (`sku_id`, `warehouse_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_area_id` (`area_id`),
    KEY `idx_stock` (`stock`),
    KEY `idx_available_stock` (`available_stock`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品库存表';

-- =============================================
-- 4. 库存变动记录表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `record_no` VARCHAR(50) NOT NULL COMMENT '记录编号',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID',
    `area_id` BIGINT COMMENT '库区ID',
    `record_type` TINYINT NOT NULL COMMENT '记录类型：1-入库，2-出库，3-调拨，4-盘点，5-锁定，6-解锁',
    `change_quantity` INT NOT NULL COMMENT '变动数量',
    `before_stock` INT NOT NULL COMMENT '变动前库存',
    `after_stock` INT NOT NULL COMMENT '变动后库存',
    `cost_price` DECIMAL(10,2) COMMENT '成本价',
    `related_no` VARCHAR(50) COMMENT '关联单号',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_record_no` (`record_no`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_record_type` (`record_type`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存变动记录表';

-- =============================================
-- 5. 入库单表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_in` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `in_no` VARCHAR(50) NOT NULL COMMENT '入库单号',
    `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID',
    `supplier_id` BIGINT COMMENT '供应商ID',
    `in_type` TINYINT DEFAULT 1 COMMENT '入库类型：1-采购入库，2-退货入库，3-调拨入库，4-盘盈入库',
    `total_quantity` INT DEFAULT 0 COMMENT '总数量',
    `total_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '总金额',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-待入库，2-部分入库，3-已入库，4-已取消',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_in_no` (`in_no`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_in_type` (`in_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='入库单表';

-- =============================================
-- 6. 入库单明细表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_in_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `in_id` BIGINT NOT NULL COMMENT '入库单ID',
    `in_no` VARCHAR(50) NOT NULL COMMENT '入库单号',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `area_id` BIGINT COMMENT '库区ID',
    `batch_no` VARCHAR(50) COMMENT '批次号',
    `plan_quantity` INT NOT NULL COMMENT '计划数量',
    `actual_quantity` INT DEFAULT 0 COMMENT '实际数量',
    `cost_price` DECIMAL(10,2) NOT NULL COMMENT '成本价',
    `total_amount` DECIMAL(12,2) NOT NULL COMMENT '总金额',
    `production_date` DATE COMMENT '生产日期',
    `expire_date` DATE COMMENT '过期日期',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_in_id` (`in_id`),
    KEY `idx_in_no` (`in_no`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_batch_no` (`batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='入库单明细表';

-- =============================================
-- 7. 出库单表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_out` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `out_no` VARCHAR(50) NOT NULL COMMENT '出库单号',
    `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID',
    `out_type` TINYINT DEFAULT 1 COMMENT '出库类型：1-销售出库，2-调拨出库，3-盘亏出库，4-报损出库',
    `related_no` VARCHAR(50) COMMENT '关联单号（订单号等）',
    `total_quantity` INT DEFAULT 0 COMMENT '总数量',
    `total_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '总金额',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-待出库，2-部分出库，3-已出库，4-已取消',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_out_no` (`out_no`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_out_type` (`out_type`),
    KEY `idx_related_no` (`related_no`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='出库单表';

-- =============================================
-- 8. 出库单明细表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_out_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `out_id` BIGINT NOT NULL COMMENT '出库单ID',
    `out_no` VARCHAR(50) NOT NULL COMMENT '出库单号',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `area_id` BIGINT COMMENT '库区ID',
    `batch_no` VARCHAR(50) COMMENT '批次号',
    `plan_quantity` INT NOT NULL COMMENT '计划数量',
    `actual_quantity` INT DEFAULT 0 COMMENT '实际数量',
    `cost_price` DECIMAL(10,2) NOT NULL COMMENT '成本价',
    `total_amount` DECIMAL(12,2) NOT NULL COMMENT '总金额',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_out_id` (`out_id`),
    KEY `idx_out_no` (`out_no`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_batch_no` (`batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='出库单明细表';

-- =============================================
-- 9. 调拨单表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_transfer` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `transfer_no` VARCHAR(50) NOT NULL COMMENT '调拨单号',
    `from_warehouse_id` BIGINT NOT NULL COMMENT '调出仓库ID',
    `to_warehouse_id` BIGINT NOT NULL COMMENT '调入仓库ID',
    `transfer_type` TINYINT DEFAULT 1 COMMENT '调拨类型：1-仓库间调拨，2-库区间调拨',
    `total_quantity` INT DEFAULT 0 COMMENT '总数量',
    `total_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '总金额',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-待调拨，2-调拨中，3-已完成，4-已取消',
    `transfer_time` DATETIME COMMENT '调拨时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_transfer_no` (`transfer_no`),
    KEY `idx_from_warehouse_id` (`from_warehouse_id`),
    KEY `idx_to_warehouse_id` (`to_warehouse_id`),
    KEY `idx_transfer_type` (`transfer_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='调拨单表';

-- =============================================
-- 10. 调拨单明细表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_transfer_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `transfer_id` BIGINT NOT NULL COMMENT '调拨单ID',
    `transfer_no` VARCHAR(50) NOT NULL COMMENT '调拨单号',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `from_area_id` BIGINT COMMENT '调出库区ID',
    `to_area_id` BIGINT COMMENT '调入库区ID',
    `batch_no` VARCHAR(50) COMMENT '批次号',
    `plan_quantity` INT NOT NULL COMMENT '计划数量',
    `actual_quantity` INT DEFAULT 0 COMMENT '实际数量',
    `cost_price` DECIMAL(10,2) NOT NULL COMMENT '成本价',
    `total_amount` DECIMAL(12,2) NOT NULL COMMENT '总金额',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_transfer_id` (`transfer_id`),
    KEY `idx_transfer_no` (`transfer_no`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_batch_no` (`batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='调拨单明细表';

-- =============================================
-- 11. 盘点单表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_check` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `check_no` VARCHAR(50) NOT NULL COMMENT '盘点单号',
    `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID',
    `area_id` BIGINT COMMENT '库区ID',
    `check_type` TINYINT DEFAULT 1 COMMENT '盘点类型：1-全盘，2-抽盘，3-循环盘点',
    `check_reason` VARCHAR(200) COMMENT '盘点原因',
    `total_quantity` INT DEFAULT 0 COMMENT '盘点总数量',
    `profit_quantity` INT DEFAULT 0 COMMENT '盘盈数量',
    `loss_quantity` INT DEFAULT 0 COMMENT '盘亏数量',
    `profit_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '盘盈金额',
    `loss_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '盘亏金额',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-盘点中，2-已完成，3-已取消',
    `start_time` DATETIME COMMENT '开始时间',
    `end_time` DATETIME COMMENT '结束时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_check_no` (`check_no`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_area_id` (`area_id`),
    KEY `idx_check_type` (`check_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='盘点单表';

-- =============================================
-- 12. 盘点单明细表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_check_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `check_id` BIGINT NOT NULL COMMENT '盘点单ID',
    `check_no` VARCHAR(50) NOT NULL COMMENT '盘点单号',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `area_id` BIGINT COMMENT '库区ID',
    `batch_no` VARCHAR(50) COMMENT '批次号',
    `book_quantity` INT NOT NULL COMMENT '账面数量',
    `actual_quantity` INT NOT NULL COMMENT '实际数量',
    `diff_quantity` INT NOT NULL COMMENT '差异数量',
    `cost_price` DECIMAL(10,2) NOT NULL COMMENT '成本价',
    `diff_amount` DECIMAL(12,2) NOT NULL COMMENT '差异金额',
    `diff_reason` VARCHAR(200) COMMENT '差异原因',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_check_id` (`check_id`),
    KEY `idx_check_no` (`check_no`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_batch_no` (`batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='盘点单明细表';

-- =============================================
-- 13. 库存锁定表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_lock` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `lock_no` VARCHAR(50) NOT NULL COMMENT '锁定单号',
    `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID',
    `lock_type` TINYINT DEFAULT 1 COMMENT '锁定类型：1-订单锁定，2-活动锁定，3-手动锁定',
    `related_no` VARCHAR(50) COMMENT '关联单号',
    `total_quantity` INT DEFAULT 0 COMMENT '锁定总数量',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-已锁定，2-已释放，3-已消费',
    `lock_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '锁定时间',
    `unlock_time` DATETIME COMMENT '解锁时间',
    `expire_time` DATETIME COMMENT '过期时间',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_lock_no` (`lock_no`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_lock_type` (`lock_type`),
    KEY `idx_related_no` (`related_no`),
    KEY `idx_status` (`status`),
    KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存锁定表';

-- =============================================
-- 14. 库存锁定明细表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_lock_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `lock_id` BIGINT NOT NULL COMMENT '锁定单ID',
    `lock_no` VARCHAR(50) NOT NULL COMMENT '锁定单号',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `area_id` BIGINT COMMENT '库区ID',
    `batch_no` VARCHAR(50) COMMENT '批次号',
    `lock_quantity` INT NOT NULL COMMENT '锁定数量',
    `used_quantity` INT DEFAULT 0 COMMENT '已使用数量',
    `remain_quantity` INT NOT NULL COMMENT '剩余数量',
    `cost_price` DECIMAL(10,2) NOT NULL COMMENT '成本价',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_lock_id` (`lock_id`),
    KEY `idx_lock_no` (`lock_no`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_batch_no` (`batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存锁定明细表';

-- =============================================
-- 15. 库存预警表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_warning` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID',
    `area_id` BIGINT COMMENT '库区ID',
    `warning_type` TINYINT NOT NULL COMMENT '预警类型：1-库存不足，2-库存过多，3-即将过期，4-已过期',
    `current_stock` INT NOT NULL COMMENT '当前库存',
    `warning_stock` INT NOT NULL COMMENT '预警库存',
    `warning_message` VARCHAR(500) NOT NULL COMMENT '预警信息',
    `warning_level` TINYINT DEFAULT 1 COMMENT '预警级别：1-低，2-中，3-高',
    `is_handled` TINYINT DEFAULT 0 COMMENT '是否已处理：1-是，0-否',
    `handle_time` DATETIME COMMENT '处理时间',
    `handle_by` BIGINT COMMENT '处理人',
    `handle_remark` VARCHAR(500) COMMENT '处理备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_warning_type` (`warning_type`),
    KEY `idx_warning_level` (`warning_level`),
    KEY `idx_is_handled` (`is_handled`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存预警表';

-- =============================================
-- 16. 库存批次表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_batch` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `batch_no` VARCHAR(50) NOT NULL COMMENT '批次号',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID',
    `area_id` BIGINT COMMENT '库区ID',
    `supplier_id` BIGINT COMMENT '供应商ID',
    `batch_quantity` INT NOT NULL COMMENT '批次数量',
    `remain_quantity` INT NOT NULL COMMENT '剩余数量',
    `cost_price` DECIMAL(10,2) NOT NULL COMMENT '成本价',
    `production_date` DATE COMMENT '生产日期',
    `expire_date` DATE COMMENT '过期日期',
    `quality_status` TINYINT DEFAULT 1 COMMENT '质量状态：1-合格，2-不合格，3-待检',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_batch_no` (`batch_no`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_expire_date` (`expire_date`),
    KEY `idx_quality_status` (`quality_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存批次表';

-- =============================================
-- 17. 库存操作日志表
-- =============================================
CREATE TABLE IF NOT EXISTS `stock_operation_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `operation_no` VARCHAR(50) NOT NULL COMMENT '操作编号',
    `operation_type` TINYINT NOT NULL COMMENT '操作类型：1-入库，2-出库，3-调拨，4-盘点，5-锁定，6-解锁',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `warehouse_id` BIGINT NOT NULL COMMENT '仓库ID',
    `area_id` BIGINT COMMENT '库区ID',
    `batch_no` VARCHAR(50) COMMENT '批次号',
    `operation_quantity` INT NOT NULL COMMENT '操作数量',
    `before_stock` INT NOT NULL COMMENT '操作前库存',
    `after_stock` INT NOT NULL COMMENT '操作后库存',
    `cost_price` DECIMAL(10,2) COMMENT '成本价',
    `related_no` VARCHAR(50) COMMENT '关联单号',
    `operation_reason` VARCHAR(200) COMMENT '操作原因',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `idx_operation_no` (`operation_no`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_create_by` (`create_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存操作日志表';

-- =============================================
-- 18. 供应商表
-- =============================================
CREATE TABLE IF NOT EXISTS `supplier` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `supplier_code` VARCHAR(50) NOT NULL COMMENT '供应商编码',
    `supplier_name` VARCHAR(100) NOT NULL COMMENT '供应商名称',
    `supplier_type` TINYINT DEFAULT 1 COMMENT '供应商类型：1-生产商，2-经销商，3-代理商',
    `contact_name` VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    `contact_phone` VARCHAR(20) NOT NULL COMMENT '联系人电话',
    `contact_email` VARCHAR(100) COMMENT '联系人邮箱',
    `business_license` VARCHAR(100) COMMENT '营业执照号',
    `tax_no` VARCHAR(50) COMMENT '税号',
    `bank_account` VARCHAR(50) COMMENT '银行账号',
    `bank_name` VARCHAR(100) COMMENT '开户银行',
    `province_code` VARCHAR(10) COMMENT '省份编码',
    `province_name` VARCHAR(50) COMMENT '省份名称',
    `city_code` VARCHAR(10) COMMENT '城市编码',
    `city_name` VARCHAR(50) COMMENT '城市名称',
    `district_code` VARCHAR(10) COMMENT '区县编码',
    `district_name` VARCHAR(50) COMMENT '区县名称',
    `detail_address` VARCHAR(200) COMMENT '详细地址',
    `website` VARCHAR(200) COMMENT '官方网站',
    `description` TEXT COMMENT '供应商描述',
    `credit_level` TINYINT DEFAULT 1 COMMENT '信用等级：1-A级，2-B级，3-C级，4-D级',
    `cooperation_status` TINYINT DEFAULT 1 COMMENT '合作状态：1-合作中，2-暂停合作，3-终止合作',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_supplier_code` (`supplier_code`),
    KEY `idx_supplier_type` (`supplier_type`),
    KEY `idx_credit_level` (`credit_level`),
    KEY `idx_cooperation_status` (`cooperation_status`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商表';

-- 库存服务表结构创建完成 - 共18张表

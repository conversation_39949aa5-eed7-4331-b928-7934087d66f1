-- =============================================
-- WitMall v2.0 评价服务表结构
-- 数据库: wit_review
-- 表数量: 2张
-- 功能: 商品评价、评论管理
-- =============================================

USE `wit_review`;

-- =============================================
-- 1. 商品评价表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_review` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `order_item_id` BIGINT NOT NULL COMMENT '订单项ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `product_image` VARCHAR(255) COMMENT '商品图片',
    `spec_values` JSON COMMENT '规格值JSON对象',
    `review_score` TINYINT NOT NULL COMMENT '评分：1-5分',
    `review_content` TEXT COMMENT '评价内容',
    `review_images` JSON COMMENT '评价图片JSON数组',
    `review_videos` JSON COMMENT '评价视频JSON数组',
    `is_anonymous` TINYINT DEFAULT 0 COMMENT '是否匿名：1-是，0-否',
    `logistics_score` TINYINT COMMENT '物流评分：1-5分',
    `service_score` TINYINT COMMENT '服务评分：1-5分',
    `is_additional` TINYINT DEFAULT 0 COMMENT '是否追评：1-是，0-否',
    `additional_content` TEXT COMMENT '追评内容',
    `additional_images` JSON COMMENT '追评图片JSON数组',
    `additional_time` DATETIME COMMENT '追评时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-隐藏',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_item_id` (`order_item_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_review_score` (`review_score`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品评价表';

-- =============================================
-- 2. 评价回复表
-- =============================================
CREATE TABLE IF NOT EXISTS `review_reply` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `review_id` BIGINT NOT NULL COMMENT '评价ID',
    `parent_id` BIGINT DEFAULT 0 COMMENT '父回复ID，0表示直接回复评价',
    `reply_type` TINYINT NOT NULL COMMENT '回复类型：1-商家回复，2-用户回复，3-管理员回复',
    `reply_user_id` BIGINT NOT NULL COMMENT '回复人ID',
    `reply_content` TEXT NOT NULL COMMENT '回复内容',
    `reply_images` JSON COMMENT '回复图片JSON数组',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-隐藏',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_review_id` (`review_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_reply_type` (`reply_type`),
    KEY `idx_reply_user_id` (`reply_user_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价回复表';

-- 评价服务表结构创建完成 - 共2张表

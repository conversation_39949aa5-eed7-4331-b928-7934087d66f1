-- =============================================
-- WitMall v2.0 数据分析服务表结构
-- 数据库: wit_analytics
-- 表数量: 5张
-- 功能: 数据统计、报表分析、用户行为分析
-- =============================================

USE `wit_analytics`;

-- =============================================
-- 1. 用户行为表
-- =============================================
CREATE TABLE IF NOT EXISTS `user_behavior` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT COMMENT '用户ID，未登录用户为空',
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID',
    `behavior_type` TINYINT NOT NULL COMMENT '行为类型：1-浏览，2-搜索，3-加购物车，4-收藏，5-下单，6-支付',
    `page_url` VARCHAR(500) COMMENT '页面URL',
    `page_title` VARCHAR(200) COMMENT '页面标题',
    `referrer_url` VARCHAR(500) COMMENT '来源URL',
    `product_id` BIGINT COMMENT '商品ID',
    `category_id` BIGINT COMMENT '分类ID',
    `search_keyword` VARCHAR(200) COMMENT '搜索关键词',
    `device_type` TINYINT COMMENT '设备类型：1-PC，2-移动端，3-平板',
    `browser` VARCHAR(100) COMMENT '浏览器',
    `os` VARCHAR(100) COMMENT '操作系统',
    `client_ip` VARCHAR(50) COMMENT '客户端IP',
    `location` VARCHAR(200) COMMENT '地理位置',
    `user_agent` VARCHAR(500) COMMENT '用户代理',
    `stay_time` INT DEFAULT 0 COMMENT '停留时间（秒）',
    `behavior_time` DATETIME NOT NULL COMMENT '行为时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_behavior_type` (`behavior_type`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_behavior_time` (`behavior_time`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为表';

-- =============================================
-- 2. 商品浏览表
-- =============================================
CREATE TABLE IF NOT EXISTS `product_view` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT COMMENT '用户ID，未登录用户为空',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT COMMENT 'SKU ID',
    `category_id` BIGINT COMMENT '分类ID',
    `brand_id` BIGINT COMMENT '品牌ID',
    `merchant_id` BIGINT COMMENT '商家ID',
    `view_source` TINYINT DEFAULT 1 COMMENT '浏览来源：1-搜索，2-分类，3-推荐，4-直接访问',
    `view_duration` INT DEFAULT 0 COMMENT '浏览时长（秒）',
    `device_type` TINYINT COMMENT '设备类型：1-PC，2-移动端，3-平板',
    `client_ip` VARCHAR(50) COMMENT '客户端IP',
    `location` VARCHAR(200) COMMENT '地理位置',
    `view_time` DATETIME NOT NULL COMMENT '浏览时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_brand_id` (`brand_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_view_time` (`view_time`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品浏览表';

-- =============================================
-- 3. 销售统计表
-- =============================================
CREATE TABLE IF NOT EXISTS `sales_statistics` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `stat_type` TINYINT NOT NULL COMMENT '统计类型：1-日统计，2-周统计，3-月统计，4-年统计',
    `merchant_id` BIGINT COMMENT '商家ID，为空表示全平台',
    `category_id` BIGINT COMMENT '分类ID，为空表示全分类',
    `product_id` BIGINT COMMENT '商品ID，为空表示全商品',
    `order_count` INT DEFAULT 0 COMMENT '订单数量',
    `order_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '订单金额',
    `paid_count` INT DEFAULT 0 COMMENT '支付订单数',
    `paid_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '支付金额',
    `refund_count` INT DEFAULT 0 COMMENT '退款订单数',
    `refund_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '退款金额',
    `product_sales` INT DEFAULT 0 COMMENT '商品销量',
    `new_user_count` INT DEFAULT 0 COMMENT '新用户数',
    `active_user_count` INT DEFAULT 0 COMMENT '活跃用户数',
    `conversion_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '转化率',
    `avg_order_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '客单价',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stat_date_type_merchant_category_product` (`stat_date`, `stat_type`, `merchant_id`, `category_id`, `product_id`),
    KEY `idx_stat_date` (`stat_date`),
    KEY `idx_stat_type` (`stat_type`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售统计表';

-- =============================================
-- 4. 用户统计表
-- =============================================
CREATE TABLE IF NOT EXISTS `user_statistics` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `stat_type` TINYINT NOT NULL COMMENT '统计类型：1-日统计，2-周统计，3-月统计，4-年统计',
    `total_user_count` INT DEFAULT 0 COMMENT '总用户数',
    `new_user_count` INT DEFAULT 0 COMMENT '新增用户数',
    `active_user_count` INT DEFAULT 0 COMMENT '活跃用户数',
    `login_user_count` INT DEFAULT 0 COMMENT '登录用户数',
    `order_user_count` INT DEFAULT 0 COMMENT '下单用户数',
    `paid_user_count` INT DEFAULT 0 COMMENT '付费用户数',
    `retention_rate_1d` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '1日留存率',
    `retention_rate_7d` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '7日留存率',
    `retention_rate_30d` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '30日留存率',
    `avg_session_duration` INT DEFAULT 0 COMMENT '平均会话时长（秒）',
    `avg_page_views` DECIMAL(8,2) DEFAULT 0.00 COMMENT '平均页面浏览量',
    `bounce_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '跳出率',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stat_date_type` (`stat_date`, `stat_type`),
    KEY `idx_stat_date` (`stat_date`),
    KEY `idx_stat_type` (`stat_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户统计表';

-- =============================================
-- 5. 订单统计表
-- =============================================
CREATE TABLE IF NOT EXISTS `order_statistics` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `stat_type` TINYINT NOT NULL COMMENT '统计类型：1-日统计，2-周统计，3-月统计，4-年统计',
    `merchant_id` BIGINT COMMENT '商家ID，为空表示全平台',
    `total_order_count` INT DEFAULT 0 COMMENT '总订单数',
    `paid_order_count` INT DEFAULT 0 COMMENT '已支付订单数',
    `shipped_order_count` INT DEFAULT 0 COMMENT '已发货订单数',
    `completed_order_count` INT DEFAULT 0 COMMENT '已完成订单数',
    `cancelled_order_count` INT DEFAULT 0 COMMENT '已取消订单数',
    `refund_order_count` INT DEFAULT 0 COMMENT '退款订单数',
    `total_order_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '总订单金额',
    `paid_order_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '已支付订单金额',
    `refund_order_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '退款订单金额',
    `avg_order_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '平均订单金额',
    `payment_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '支付转化率',
    `completion_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '完成率',
    `refund_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '退款率',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stat_date_type_merchant` (`stat_date`, `stat_type`, `merchant_id`),
    KEY `idx_stat_date` (`stat_date`),
    KEY `idx_stat_type` (`stat_type`),
    KEY `idx_merchant_id` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单统计表';

-- 数据分析服务表结构创建完成 - 共5张表

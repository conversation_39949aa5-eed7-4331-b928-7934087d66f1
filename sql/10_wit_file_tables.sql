-- =============================================
-- WitMall v2.0 文件服务表结构
-- 数据库: wit_file
-- 表数量: 2张
-- 功能: 文件上传、存储管理
-- =============================================

USE `wit_file`;

-- =============================================
-- 1. 文件信息表
-- =============================================
CREATE TABLE IF NOT EXISTS `file_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名称',
    `original_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
    `file_path` VARCHAR(500) NOT NULL COMMENT '文件路径',
    `file_url` VARCHAR(500) NOT NULL COMMENT '文件访问URL',
    `file_size` BIGINT NOT NULL COMMENT '文件大小（字节）',
    `file_type` VARCHAR(50) NOT NULL COMMENT '文件类型',
    `file_extension` VARCHAR(10) NOT NULL COMMENT '文件扩展名',
    `mime_type` VARCHAR(100) COMMENT 'MIME类型',
    `md5_hash` VARCHAR(32) COMMENT 'MD5哈希值',
    `storage_type` TINYINT DEFAULT 1 COMMENT '存储类型：1-本地，2-阿里云OSS，3-腾讯云COS，4-七牛云',
    `bucket_name` VARCHAR(100) COMMENT '存储桶名称',
    `category_id` BIGINT COMMENT '文件分类ID',
    `upload_user_id` BIGINT COMMENT '上传用户ID',
    `business_type` VARCHAR(50) COMMENT '业务类型',
    `business_id` BIGINT COMMENT '业务ID',
    `is_public` TINYINT DEFAULT 1 COMMENT '是否公开：1-是，0-否',
    `download_count` INT DEFAULT 0 COMMENT '下载次数',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_file_name` (`file_name`),
    KEY `idx_file_type` (`file_type`),
    KEY `idx_md5_hash` (`md5_hash`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_upload_user_id` (`upload_user_id`),
    KEY `idx_business_type_id` (`business_type`, `business_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件信息表';

-- =============================================
-- 2. 文件分类表
-- =============================================
CREATE TABLE IF NOT EXISTS `file_category` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `category_name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    `parent_id` BIGINT DEFAULT 0 COMMENT '父分类ID，0表示顶级分类',
    `category_path` VARCHAR(500) COMMENT '分类路径',
    `level` TINYINT DEFAULT 1 COMMENT '分类层级',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `description` VARCHAR(500) COMMENT '分类描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_level` (`level`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件分类表';

-- 文件服务表结构创建完成 - 共2张表

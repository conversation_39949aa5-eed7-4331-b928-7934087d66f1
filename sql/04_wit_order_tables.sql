-- =============================================
-- WitMall v2.0 订单服务表结构
-- 数据库: wit_order
-- 表数量: 12张
-- 功能: 订单管理、物流跟踪、售后服务
-- =============================================

USE `wit_order`;

-- =============================================
-- 1. 订单表
-- =============================================
CREATE TABLE IF NOT EXISTS `order_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `parent_order_no` VARCHAR(50) COMMENT '父订单号（拆单时使用）',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `shop_id` BIGINT NOT NULL COMMENT '店铺ID',
    `order_type` TINYINT DEFAULT 1 COMMENT '订单类型：1-普通订单，2-预售订单，3-拼团订单，4-秒杀订单',
    `order_source` TINYINT DEFAULT 1 COMMENT '订单来源：1-PC，2-H5，3-小程序，4-APP，5-API',
    `order_status` TINYINT DEFAULT 1 COMMENT '订单状态：1-待付款，2-待发货，3-待收货，4-待评价，5-已完成，6-已取消，7-已关闭',
    `pay_status` TINYINT DEFAULT 0 COMMENT '支付状态：0-未支付，1-已支付，2-部分支付，3-已退款，4-部分退款',
    `delivery_status` TINYINT DEFAULT 0 COMMENT '发货状态：0-未发货，1-部分发货，2-已发货，3-已收货',
    `total_amount` DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    `product_amount` DECIMAL(10,2) NOT NULL COMMENT '商品总金额',
    `freight_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '运费金额',
    `discount_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    `coupon_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠券金额',
    `point_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '积分抵扣金额',
    `balance_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额支付金额',
    `pay_amount` DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    `currency` VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    `exchange_rate` DECIMAL(10,4) DEFAULT 1.0000 COMMENT '汇率',
    `total_quantity` INT NOT NULL COMMENT '商品总数量',
    `total_weight` DECIMAL(8,2) DEFAULT 0.00 COMMENT '总重量(kg)',
    `buyer_message` VARCHAR(500) COMMENT '买家留言',
    `seller_message` VARCHAR(500) COMMENT '卖家备注',
    `invoice_type` TINYINT DEFAULT 0 COMMENT '发票类型：0-不开票，1-个人，2-企业',
    `invoice_title` VARCHAR(200) COMMENT '发票抬头',
    `invoice_content` VARCHAR(200) COMMENT '发票内容',
    `invoice_tax_no` VARCHAR(50) COMMENT '纳税人识别号',
    `coupon_id` BIGINT COMMENT '使用的优惠券ID',
    `activity_id` BIGINT COMMENT '参与的活动ID',
    `agent_id` BIGINT COMMENT '代理商ID',
    `commission_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '佣金金额',
    `pay_time` DATETIME COMMENT '支付时间',
    `delivery_time` DATETIME COMMENT '发货时间',
    `receive_time` DATETIME COMMENT '收货时间',
    `finish_time` DATETIME COMMENT '完成时间',
    `cancel_time` DATETIME COMMENT '取消时间',
    `cancel_reason` VARCHAR(500) COMMENT '取消原因',
    `auto_receive_time` DATETIME COMMENT '自动收货时间',
    `delete_status` TINYINT DEFAULT 0 COMMENT '删除状态：0-未删除，1-用户删除，2-商家删除，3-管理员删除',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_parent_order_no` (`parent_order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_order_status` (`order_status`),
    KEY `idx_pay_status` (`pay_status`),
    KEY `idx_delivery_status` (`delivery_status`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_pay_time` (`pay_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- =============================================
-- 2. 订单项表
-- =============================================
CREATE TABLE IF NOT EXISTS `order_item` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `product_code` VARCHAR(100) NOT NULL COMMENT '商品编码',
    `sku_code` VARCHAR(100) NOT NULL COMMENT 'SKU编码',
    `sku_name` VARCHAR(200) COMMENT 'SKU名称',
    `product_image` VARCHAR(255) COMMENT '商品图片',
    `product_category_id` BIGINT COMMENT '商品分类ID',
    `product_brand_id` BIGINT COMMENT '商品品牌ID',
    `spec_values` JSON COMMENT '规格值JSON对象',
    `price` DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    `quantity` INT NOT NULL COMMENT '购买数量',
    `total_amount` DECIMAL(10,2) NOT NULL COMMENT '小计金额',
    `discount_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    `real_amount` DECIMAL(10,2) NOT NULL COMMENT '实际金额',
    `weight` DECIMAL(8,2) DEFAULT 0.00 COMMENT '商品重量(kg)',
    `volume` DECIMAL(8,2) DEFAULT 0.00 COMMENT '商品体积(立方米)',
    `gift_integral` INT DEFAULT 0 COMMENT '赠送积分',
    `promotion_name` VARCHAR(200) COMMENT '促销活动名称',
    `promotion_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '促销优惠金额',
    `coupon_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠券优惠金额',
    `commission_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '佣金比例',
    `commission_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '佣金金额',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sku_id` (`sku_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单项表';

-- =============================================
-- 3. 订单收货地址表
-- =============================================
CREATE TABLE IF NOT EXISTS `order_address` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `receiver_name` VARCHAR(50) NOT NULL COMMENT '收货人姓名',
    `receiver_phone` VARCHAR(20) NOT NULL COMMENT '收货人电话',
    `province_code` VARCHAR(10) NOT NULL COMMENT '省份编码',
    `province_name` VARCHAR(50) NOT NULL COMMENT '省份名称',
    `city_code` VARCHAR(10) NOT NULL COMMENT '城市编码',
    `city_name` VARCHAR(50) NOT NULL COMMENT '城市名称',
    `district_code` VARCHAR(10) NOT NULL COMMENT '区县编码',
    `district_name` VARCHAR(50) NOT NULL COMMENT '区县名称',
    `detail_address` VARCHAR(200) NOT NULL COMMENT '详细地址',
    `postal_code` VARCHAR(10) COMMENT '邮政编码',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_id` (`order_id`),
    KEY `idx_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单收货地址表';

-- =============================================
-- 4. 物流公司表
-- =============================================
CREATE TABLE IF NOT EXISTS `logistics_company` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `company_name` VARCHAR(100) NOT NULL COMMENT '物流公司名称',
    `company_code` VARCHAR(50) NOT NULL COMMENT '物流公司编码',
    `company_logo` VARCHAR(255) COMMENT '公司Logo',
    `contact_name` VARCHAR(50) COMMENT '联系人',
    `contact_phone` VARCHAR(20) COMMENT '联系电话',
    `website` VARCHAR(200) COMMENT '官方网站',
    `api_url` VARCHAR(200) COMMENT 'API接口地址',
    `api_key` VARCHAR(100) COMMENT 'API密钥',
    `is_support_query` TINYINT DEFAULT 1 COMMENT '是否支持查询：1-是，0-否',
    `query_url` VARCHAR(200) COMMENT '查询接口地址',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_company_code` (`company_code`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流公司表';

-- =============================================
-- 5. 订单物流表
-- =============================================
CREATE TABLE IF NOT EXISTS `order_logistics` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `logistics_company_id` BIGINT NOT NULL COMMENT '物流公司ID',
    `logistics_company_name` VARCHAR(100) NOT NULL COMMENT '物流公司名称',
    `logistics_no` VARCHAR(50) NOT NULL COMMENT '物流单号',
    `sender_name` VARCHAR(50) COMMENT '发件人姓名',
    `sender_phone` VARCHAR(20) COMMENT '发件人电话',
    `sender_address` VARCHAR(200) COMMENT '发件人地址',
    `receiver_name` VARCHAR(50) NOT NULL COMMENT '收件人姓名',
    `receiver_phone` VARCHAR(20) NOT NULL COMMENT '收件人电话',
    `receiver_address` VARCHAR(200) NOT NULL COMMENT '收件人地址',
    `logistics_status` TINYINT DEFAULT 1 COMMENT '物流状态：1-已发货，2-运输中，3-派送中，4-已签收，5-异常',
    `current_location` VARCHAR(200) COMMENT '当前位置',
    `estimated_time` DATETIME COMMENT '预计送达时间',
    `actual_time` DATETIME COMMENT '实际送达时间',
    `freight_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '运费',
    `weight` DECIMAL(8,2) DEFAULT 0.00 COMMENT '重量(kg)',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_logistics_company_id` (`logistics_company_id`),
    KEY `idx_logistics_no` (`logistics_no`),
    KEY `idx_logistics_status` (`logistics_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单物流表';

-- =============================================
-- 6. 物流轨迹表
-- =============================================
CREATE TABLE IF NOT EXISTS `logistics_track` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `logistics_id` BIGINT NOT NULL COMMENT '物流ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `logistics_no` VARCHAR(50) NOT NULL COMMENT '物流单号',
    `track_time` DATETIME NOT NULL COMMENT '轨迹时间',
    `track_location` VARCHAR(200) COMMENT '轨迹地点',
    `track_info` VARCHAR(500) NOT NULL COMMENT '轨迹信息',
    `track_status` TINYINT COMMENT '轨迹状态：1-已发货，2-运输中，3-派送中，4-已签收，5-异常',
    `operator` VARCHAR(50) COMMENT '操作人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_logistics_id` (`logistics_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_logistics_no` (`logistics_no`),
    KEY `idx_track_time` (`track_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流轨迹表';

-- =============================================
-- 7. 售后申请表
-- =============================================
CREATE TABLE IF NOT EXISTS `after_sale` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `after_sale_no` VARCHAR(50) NOT NULL COMMENT '售后单号',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `order_item_id` BIGINT NOT NULL COMMENT '订单项ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `sku_id` BIGINT NOT NULL COMMENT 'SKU ID',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `sku_name` VARCHAR(200) COMMENT 'SKU名称',
    `product_image` VARCHAR(255) COMMENT '商品图片',
    `after_sale_type` TINYINT NOT NULL COMMENT '售后类型：1-退货退款，2-换货，3-仅退款',
    `after_sale_reason` VARCHAR(200) NOT NULL COMMENT '售后原因',
    `after_sale_quantity` INT NOT NULL COMMENT '售后数量',
    `refund_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款金额',
    `description` TEXT COMMENT '问题描述',
    `evidence_images` JSON COMMENT '凭证图片JSON数组',
    `contact_name` VARCHAR(50) COMMENT '联系人姓名',
    `contact_phone` VARCHAR(20) COMMENT '联系人电话',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-待审核，2-审核通过，3-审核拒绝，4-待退货，5-待收货，6-已完成，7-已取消',
    `audit_time` DATETIME COMMENT '审核时间',
    `audit_by` BIGINT COMMENT '审核人',
    `audit_remark` VARCHAR(500) COMMENT '审核备注',
    `return_logistics_company` VARCHAR(100) COMMENT '退货物流公司',
    `return_logistics_no` VARCHAR(50) COMMENT '退货物流单号',
    `receive_time` DATETIME COMMENT '收货时间',
    `refund_time` DATETIME COMMENT '退款时间',
    `finish_time` DATETIME COMMENT '完成时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_after_sale_no` (`after_sale_no`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_order_item_id` (`order_item_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_after_sale_type` (`after_sale_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='售后申请表';

-- =============================================
-- 8. 售后日志表
-- =============================================
CREATE TABLE IF NOT EXISTS `after_sale_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `after_sale_id` BIGINT NOT NULL COMMENT '售后ID',
    `after_sale_no` VARCHAR(50) NOT NULL COMMENT '售后单号',
    `operation_type` TINYINT NOT NULL COMMENT '操作类型：1-申请，2-审核，3-拒绝，4-退货，5-收货，6-退款，7-完成，8-取消',
    `operation_desc` VARCHAR(500) NOT NULL COMMENT '操作描述',
    `before_status` TINYINT COMMENT '操作前状态',
    `after_status` TINYINT COMMENT '操作后状态',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `idx_after_sale_id` (`after_sale_id`),
    KEY `idx_after_sale_no` (`after_sale_no`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='售后日志表';

-- =============================================
-- 9. 订单状态日志表
-- =============================================
CREATE TABLE IF NOT EXISTS `order_status_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `operation_type` TINYINT NOT NULL COMMENT '操作类型：1-创建，2-支付，3-发货，4-收货，5-完成，6-取消，7-关闭',
    `before_status` TINYINT COMMENT '操作前状态',
    `after_status` TINYINT COMMENT '操作后状态',
    `operation_desc` VARCHAR(500) NOT NULL COMMENT '操作描述',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单状态日志表';

-- =============================================
-- 10. 订单发票表
-- =============================================
CREATE TABLE IF NOT EXISTS `order_invoice` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `invoice_type` TINYINT NOT NULL COMMENT '发票类型：1-个人，2-企业',
    `invoice_title` VARCHAR(200) NOT NULL COMMENT '发票抬头',
    `invoice_content` VARCHAR(200) NOT NULL COMMENT '发票内容',
    `tax_no` VARCHAR(50) COMMENT '纳税人识别号',
    `invoice_amount` DECIMAL(10,2) NOT NULL COMMENT '发票金额',
    `invoice_status` TINYINT DEFAULT 1 COMMENT '发票状态：1-待开票，2-已开票，3-已作废',
    `invoice_no` VARCHAR(50) COMMENT '发票号码',
    `invoice_code` VARCHAR(50) COMMENT '发票代码',
    `invoice_date` DATETIME COMMENT '开票日期',
    `invoice_url` VARCHAR(255) COMMENT '发票文件地址',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_invoice_status` (`invoice_status`),
    KEY `idx_invoice_no` (`invoice_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单发票表';

-- =============================================
-- 11. 订单退款表
-- =============================================
CREATE TABLE IF NOT EXISTS `order_refund` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `refund_no` VARCHAR(50) NOT NULL COMMENT '退款单号',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `after_sale_id` BIGINT COMMENT '售后ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `refund_type` TINYINT NOT NULL COMMENT '退款类型：1-全额退款，2-部分退款',
    `refund_reason` VARCHAR(200) NOT NULL COMMENT '退款原因',
    `refund_amount` DECIMAL(10,2) NOT NULL COMMENT '退款金额',
    `refund_method` TINYINT NOT NULL COMMENT '退款方式：1-原路退回，2-余额，3-银行卡',
    `refund_account` VARCHAR(100) COMMENT '退款账户',
    `refund_status` TINYINT DEFAULT 1 COMMENT '退款状态：1-待退款，2-退款中，3-已退款，4-退款失败',
    `refund_time` DATETIME COMMENT '退款时间',
    `refund_success_time` DATETIME COMMENT '退款成功时间',
    `third_party_no` VARCHAR(100) COMMENT '第三方退款单号',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_no` (`refund_no`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_after_sale_id` (`after_sale_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_refund_status` (`refund_status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单退款表';

-- =============================================
-- 12. 订单取消表
-- =============================================
CREATE TABLE IF NOT EXISTS `order_cancel` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `cancel_type` TINYINT NOT NULL COMMENT '取消类型：1-用户取消，2-商家取消，3-系统取消',
    `cancel_reason` VARCHAR(200) NOT NULL COMMENT '取消原因',
    `cancel_desc` TEXT COMMENT '取消描述',
    `is_refund` TINYINT DEFAULT 0 COMMENT '是否需要退款：1-是，0-否',
    `refund_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款金额',
    `cancel_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '取消时间',
    `create_by` BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_cancel_type` (`cancel_type`),
    KEY `idx_cancel_time` (`cancel_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单取消表';

-- 订单服务表结构创建完成 - 共12张表

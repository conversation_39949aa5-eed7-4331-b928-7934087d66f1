# 🗄️ WitMall 数据库初始化指南

## 📋 版本说明

WitMall v2.0 是完整的企业级电商数据库设计，包含**102张表**，覆盖所有电商业务场景。

### ✨ v2.0 特性

- ✅ **MySQL 8.0 完全兼容**：解决了所有兼容性问题
- ✅ **102张完整业务表**：真正的企业级表结构
- ✅ **17个微服务数据库**：完整的微服务架构
- ✅ **生产就绪**：可直接用于生产环境

### 📁 文件列表

| 文件名 | 说明 | 表数量 | 推荐使用场景 |
|--------|------|--------|-------------|
| `execute_all.sql` | **🌟 完整执行脚本** | 102张表 | **强烈推荐使用** |
| `00_create_databases.sql` | 创建所有数据库 | 17个数据库 | 第一步执行 |
| `01-15_*.sql` | 各服务表结构文件 | 102张表 | 分步执行 |
| `99_init_data.sql` | 初始化数据 | - | 最后执行 |

## 🚀 快速开始

### ⚠️ 重要说明

- ✅ **MySQL 8.0兼容**：已修复`NO_AUTO_CREATE_USER`兼容性问题
- ✅ **完整功能**：包含102张表，覆盖所有业务场景
- ✅ **生产就绪**：企业级表结构设计，支持大规模应用

## 🚀 快速开始

### 🎯 一键初始化（推荐）

```sql
-- 1. 连接到MySQL服务器（MySQL 5.7+ 或 8.0+）
-- 2. 复制 execute_all.sql 的全部内容
-- 3. 粘贴到SQL客户端执行
```

### 📋 分步执行

```sql
-- 1. 创建数据库
SOURCE 00_create_databases.sql;

-- 2. 创建用户服务表（12张）
SOURCE 01_wit_user_tables.sql;

-- 3. 创建商品服务表（15张）
SOURCE 02_wit_product_tables.sql;

-- 4. 创建库存服务表（18张）
SOURCE 03_wit_inventory_tables.sql;

-- 5. 创建订单服务表（12张）
SOURCE 04_wit_order_tables.sql;

-- 6. 创建支付服务表（12张）
SOURCE 05_wit_payment_tables.sql;

-- 7. 创建购物车服务表（1张）
SOURCE 06_wit_cart_tables.sql;

-- 8. 创建营销服务表（8张）
SOURCE 07_wit_marketing_tables.sql;

-- 9. 创建评价服务表（2张）
SOURCE 08_wit_review_tables.sql;

-- 10. 创建通知服务表（3张）
SOURCE 09_wit_notification_tables.sql;

-- 11. 创建文件服务表（2张）
SOURCE 10_wit_file_tables.sql;

-- 12. 创建系统服务表（4张）
SOURCE 11_wit_system_tables.sql;

-- 13. 创建定时任务服务表（3张）
SOURCE 12_wit_schedule_tables.sql;

-- 14. 创建数据分析服务表（5张）
SOURCE 13_wit_analytics_tables.sql;

-- 15. 创建搜索服务表（3张）
SOURCE 14_wit_search_tables.sql;

-- 16. 创建推荐服务表（2张）
SOURCE 15_wit_recommendation_tables.sql;

-- 17. 插入初始化数据
SOURCE 99_init_data.sql;
```

**包含内容：**
- ✅ 16个微服务数据库
- ✅ 102张完整业务表
- ✅ 完整的索引和约束
- ✅ 基础测试数据
- ✅ 完整的权限体系

## 📊 完整表结构统计

### 各服务表数量详情

| 序号 | 服务名称 | 数据库名 | 表数量 | 主要功能 |
|------|----------|----------|--------|----------|
| 1 | 用户服务 | wit_user | 12张 | 用户管理、商家管理、代理商体系、权限控制 |
| 2 | 商品服务 | wit_product | 15张 | 商品管理、分类管理、品牌管理、规格管理 |
| 3 | 库存服务 | wit_inventory | 18张 | 库存管理、仓库管理、出入库管理、盘点管理 |
| 4 | 订单服务 | wit_order | 12张 | 订单管理、物流跟踪、售后服务 |
| 5 | 支付服务 | wit_payment | 12张 | 支付管理、退款管理、账户管理、佣金结算 |
| 6 | 购物车服务 | wit_cart | 1张 | 购物车管理 |
| 7 | 营销服务 | wit_marketing | 8张 | 优惠券、营销活动、秒杀、拼团 |
| 8 | 评价服务 | wit_review | 2张 | 商品评价、评论管理 |
| 9 | 通知服务 | wit_notification | 3张 | 消息通知、模板管理 |
| 10 | 文件服务 | wit_file | 2张 | 文件上传、存储管理 |
| 11 | 系统服务 | wit_system | 4张 | 系统配置、操作日志 |
| 12 | 定时任务服务 | wit_schedule | 3张 | 定时任务管理 |
| 13 | 数据分析服务 | wit_analytics | 5张 | 数据统计、报表分析 |
| 14 | 搜索服务 | wit_search | 3张 | 商品搜索、索引管理 |
| 15 | 推荐服务 | wit_recommendation | 2张 | 商品推荐、用户画像 |

**总计：102张表**

## ✅ 验证方法

执行完成后，验证表数量：

```sql
-- 查看数据库数量（应该是17个）
SELECT COUNT(*) as database_count FROM information_schema.SCHEMATA WHERE SCHEMA_NAME LIKE 'wit_%' OR SCHEMA_NAME = 'nacos_config';

-- 查看总表数量（应该是102张）
SELECT COUNT(*) as table_count FROM information_schema.TABLES WHERE TABLE_SCHEMA LIKE 'wit_%';

-- 查看各数据库的表数量
SELECT TABLE_SCHEMA, COUNT(*) as table_count
FROM information_schema.TABLES
WHERE TABLE_SCHEMA LIKE 'wit_%'
GROUP BY TABLE_SCHEMA
ORDER BY table_count DESC;
```

## 👤 默认账号

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | 123456 | 超级管理员 | 拥有所有权限 |
| operator | 123456 | 运营人员 | 日常运营管理 |
| service | 123456 | 客服人员 | 客服和售后 |
| testuser | 123456 | 普通用户 | 测试用户 |

## 🔧 环境要求

- **MySQL版本**: 5.7+ 或 8.0+
- **字符集**: utf8mb4
- **权限**: 具有创建数据库的权限

## 🎯 使用建议

### 开发环境
- 使用 `execute_all.sql` 快速初始化
- 可以只执行需要的服务表结构

### 生产环境
- 分步执行各个服务的表结构
- 根据业务需要调整表结构
- 建议使用数据库迁移工具管理版本

### 性能优化
- 根据业务量调整索引
- 考虑分库分表策略
- 配置主从复制

---

🎉 **WitMall v1.0 - 企业级102张表完整版！**

# WitMall Docker 环境变量配置

# MySQL 配置
MYSQL_ROOT_PASSWORD=root123456
MYSQL_DATABASE=wit_mall
MYSQL_USER=witmall
MYSQL_PASSWORD=witmall123456

# Redis 配置
REDIS_PASSWORD=

# Nacos 配置
NACOS_AUTH_ENABLE=false
NACOS_AUTH_TOKEN=SecretKey012345678901234567890123456789012345678901234567890123456789
NACOS_AUTH_IDENTITY_KEY=serverIdentity
NACOS_AUTH_IDENTITY_VALUE=security

# RabbitMQ 配置
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin123456

# Elasticsearch 配置
ES_JAVA_OPTS=-Xms512m -Xmx512m
ELASTIC_PASSWORD=

# MinIO 配置
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=admin123456

# XXL-Job 配置
XXL_JOB_ADMIN_PASSWORD=123456

# Seata 配置
SEATA_PORT=8091
STORE_MODE=db

# 网络配置
NETWORK_NAME=wit-network

# 数据目录
DATA_DIR=./data

# 时区设置
TZ=Asia/Shanghai

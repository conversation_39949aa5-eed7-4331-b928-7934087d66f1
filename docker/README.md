# 🐳 WitMall Docker 部署指南

## 📋 服务清单

本Docker Compose配置包含以下服务：

| 服务 | 版本 | 端口 | 用户名/密码 | 说明 |
|------|------|------|-------------|------|
| **MySQL** | 8.0 | 3306 | root/root123456 | 主数据库 |
| **Redis** | 7.0 | 6379 | 无密码 | 缓存存储 |
| **Nacos** | 2.2.3 | 8848 | nacos/nacos | 服务注册中心 |
| **RabbitMQ** | 3.11 | 5672, 15672 | admin/admin123456 | 消息队列 |
| **Elasticsearch** | 7.17 | 9200, 9300 | 无认证 | 搜索引擎 |
| **Kibana** | 7.17 | 5601 | 无认证 | ES可视化 |
| **MinIO** | latest | 9000, 9001 | admin/admin123456 | 对象存储 |
| **Sentinel** | 1.8.0 | 8858 | sentinel/sentinel | 流量控制 |
| **XXL-Job** | 2.4.0 | 8080 | admin/123456 | 任务调度 |
| **Seata** | 1.7.1 | 8091 | - | 分布式事务 |
| **Nginx** | 1.24 | 80, 443 | - | 反向代理 |

## 🚀 快速部署

### 1. 环境准备

确保已安装Docker和Docker Compose：

```bash
# 检查Docker版本
docker --version

# 检查Docker Compose版本
docker-compose --version
```

### 2. 启动服务

```bash
# 进入docker目录
cd docker

# 设置脚本执行权限（Linux/Mac）
chmod +x scripts/deploy.sh

# 启动所有服务
./scripts/deploy.sh start

# 或者直接使用docker-compose
docker-compose up -d
```

### 3. 验证服务

```bash
# 查看服务状态
./scripts/deploy.sh status

# 检查健康状态
./scripts/deploy.sh health

# 查看服务访问信息
./scripts/deploy.sh info
```

## 🔧 本地开发配置

### Spring Boot 配置示例

```yaml
# application-dev.yml
spring:
  datasource:
    url: ********************************************************************************************************
    username: root
    password: root123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  redis:
    host: localhost
    port: 6379
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
  
  rabbitmq:
    host: localhost
    port: 5672
    username: admin
    password: admin123456
    virtual-host: /

  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        file-extension: yml

  elasticsearch:
    rest:
      uris: http://localhost:9200

# MinIO 配置
minio:
  endpoint: http://localhost:9000
  access-key: admin
  secret-key: admin123456
  bucket-name: witmall

# XXL-Job 配置
xxl:
  job:
    admin:
      addresses: http://localhost:8080/xxl-job-admin
    executor:
      appname: witmall-executor
      address: 
      ip: 
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30

# Seata 配置
seata:
  registry:
    type: nacos
    nacos:
      server-addr: localhost:8848
      group: SEATA_GROUP
      namespace: 
  config:
    type: nacos
    nacos:
      server-addr: localhost:8848
      group: SEATA_GROUP
      namespace: 
```

## 🛠️ 常用命令

```bash
# 启动所有服务
./scripts/deploy.sh start

# 停止所有服务
./scripts/deploy.sh stop

# 重启所有服务
./scripts/deploy.sh restart

# 查看服务状态
./scripts/deploy.sh status

# 查看所有服务日志
./scripts/deploy.sh logs

# 查看特定服务日志
./scripts/deploy.sh logs mysql
./scripts/deploy.sh logs redis

# 检查服务健康状态
./scripts/deploy.sh health

# 显示服务访问信息
./scripts/deploy.sh info
```

## 📊 服务访问地址

- **Nacos控制台**: http://localhost:8848/nacos
- **RabbitMQ管理界面**: http://localhost:15672
- **Kibana**: http://localhost:5601
- **MinIO管理界面**: http://localhost:9001
- **Sentinel控制台**: http://localhost:8858
- **XXL-Job管理界面**: http://localhost:8080/xxl-job-admin
- **Elasticsearch**: http://localhost:9200

## 🔍 故障排查

### 1. 服务启动失败

```bash
# 查看具体服务日志
docker-compose logs [service-name]

# 查看容器状态
docker ps -a

# 重启特定服务
docker-compose restart [service-name]
```

### 2. 端口冲突

```bash
# 检查端口占用
netstat -tlnp | grep [port]

# 修改 docker-compose.yml 中的端口映射
```

### 3. 权限问题

```bash
# 设置Elasticsearch数据目录权限
sudo chown -R 1000:1000 elasticsearch/

# 设置MySQL数据目录权限
sudo chown -R 999:999 mysql/
```

### 4. 内存不足

```bash
# 调整Elasticsearch内存
# 在docker-compose.yml中修改ES_JAVA_OPTS

# 调整MySQL缓存大小
# 在mysql/config/my.cnf中修改相关参数
```

## 🔒 安全配置

### 1. 修改默认密码

编辑 `.env` 文件，修改各服务的默认密码：

```bash
# 修改MySQL密码
MYSQL_ROOT_PASSWORD=your_new_password

# 修改RabbitMQ密码
RABBITMQ_DEFAULT_PASS=your_new_password

# 修改MinIO密码
MINIO_ROOT_PASSWORD=your_new_password
```

### 2. 网络安全

```bash
# 配置防火墙规则（仅开放必要端口）
sudo ufw allow 3306  # MySQL
sudo ufw allow 6379  # Redis
sudo ufw allow 8848  # Nacos
# ... 其他端口
```

### 3. 数据备份

```bash
# MySQL备份
docker exec wit-mysql mysqldump -uroot -proot123456 --all-databases > backup.sql

# Redis备份
docker exec wit-redis redis-cli BGSAVE
```

## 📈 性能优化

### 1. MySQL优化
- 调整 `innodb_buffer_pool_size`
- 优化 `max_connections`
- 配置慢查询日志

### 2. Redis优化
- 设置合适的 `maxmemory`
- 配置 `maxmemory-policy`
- 启用AOF持久化

### 3. Elasticsearch优化
- 调整JVM堆内存
- 配置索引分片数
- 设置合适的刷新间隔

---

🎉 **部署完成后，你就可以在本地开发环境中连接这些服务了！**

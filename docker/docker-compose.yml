version: '3.8'

services:
  # MySQL 8.0 - 主数据库
  mysql:
    image: mysql:8.0
    container_name: wit-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/config/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./mysql/logs:/var/log/mysql
      - ../sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - wit-network

  # Redis 7.0 - 缓存和会话存储
  redis:
    image: redis:7.0-alpine
    container_name: wit-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/config/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf
    networks:
      - wit-network

  # RabbitMQ 3.11 - 消息队列
  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    container_name: wit-rabbitmq
    restart: always
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123456
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./rabbitmq/logs:/var/log/rabbitmq
    networks:
      - wit-network

  # Nacos 2.2.3 - 服务注册与配置中心
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: wit-nacos
    restart: always
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos_config
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: root123456
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    volumes:
      - nacos_logs:/home/<USER>/logs
    networks:
      - wit-network

  # Elasticsearch 7.17 - 搜索引擎
  elasticsearch:
    image: elasticsearch:8.11.3
    container_name: wit-elasticsearch
    restart: always
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - es_data:/usr/share/elasticsearch/data
      - ./elasticsearch/logs:/usr/share/elasticsearch/logs
    networks:
      - wit-network

  # Kibana 7.17 - Elasticsearch可视化
  kibana:
    image: kibana:8.11.3
    container_name: wit-kibana
    restart: always
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    volumes:
      - ./kibana/logs:/usr/share/kibana/logs
    depends_on:
      - elasticsearch
    networks:
      - wit-network

  # MinIO - 对象存储
  minio:
    image: minio/minio:latest
    container_name: wit-minio
    restart: always
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: admin123456
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - wit-network

  # Sentinel Dashboard - 流量控制
  sentinel:
    image: bladex/sentinel-dashboard:1.8.6
    container_name: wit-sentinel
    restart: always
    ports:
      - "8858:8858"
    networks:
      - wit-network

  # Seata Server - 分布式事务
  seata:
    image: seataio/seata-server:1.7.1
    container_name: wit-seata
    restart: always
    environment:
      SEATA_PORT: 8091
      STORE_MODE: db
      SEATA_CONFIG_NAME: file:/root/seata-config/registry
    ports:
      - "8091:8091"
    volumes:
      - ./seata:/root/seata-config
    depends_on:
      - mysql
      - nacos
    networks:
      - wit-network

  # XXL-Job Admin - 分布式任务调度
  xxl-job-admin:
    image: xuxueli/xxl-job-admin:2.4.0
    container_name: wit-xxl-job-admin
    restart: always
    environment:
      PARAMS: '--spring.datasource.url=*********************************************************************************************************************** --spring.datasource.username=root --spring.datasource.password=root123456'
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    networks:
      - wit-network

  # Nginx - 反向代理和负载均衡
  nginx:
    image: nginx:1.24-alpine
    container_name: wit-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/config/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/config/conf.d:/etc/nginx/conf.d
      - ./nginx/logs:/var/log/nginx
      - ./nginx/html:/usr/share/nginx/html
    depends_on:
      - nacos
    networks:
      - wit-network

networks:
  wit-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:
  nacos_logs:
  elasticsearch_data:
  minio_data:

# 智能路由配置 - 可选方案
# 如果你想要Nginx直接路由到具体服务，可以使用这个配置

# 定义服务映射
map $service_name $backend_service {
    default         wit-gateway:8080;
    auth           wit-auth:8081;
    user           wit-user:8082;
    product        wit-product:8083;
    order          wit-order:8084;
    cart           wit-cart:8085;
    payment        wit-payment:8086;
    inventory      wit-inventory:8087;
    marketing      wit-marketing:8088;
    search         wit-search:8089;
    recommendation wit-recommendation:8090;
    review         wit-review:8091;
    notification   wit-notification:8092;
    file           wit-file:8093;
    system         wit-system:8094;
    schedule       wit-schedule:8095;
    analytics      wit-analytics:8096;
    aftersales     wit-aftersales:8097;
}

# 智能路由服务器配置（备用方案）
server {
    listen 8081;
    server_name localhost;

    # 解析租户ID和服务名
    location ~ ^/(\d+)/([a-zA-Z-]+)(/.*)?$ {
        set $tenant_id $1;
        set $service_name $2;
        set $service_path $3;
        
        # 如果没有路径，设置默认路径
        if ($service_path = "") {
            set $service_path "/";
        }
        
        # 直接代理到对应的微服务
        proxy_pass http://$backend_service$service_path;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Tenant-Id $tenant_id;
        proxy_set_header X-Service-Name $service_name;
        proxy_set_header X-Original-Path $uri;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 10s;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "smart routing healthy\n";
        add_header Content-Type text/plain;
    }
}

#!/bin/bash

# WitMall Docker 部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|status|logs|health|info]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="witmall"
COMPOSE_FILE="docker-compose.yml"
BASE_DIR=$(dirname $(dirname $(realpath $0)))

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查环境依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "环境检查通过"
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    cd $BASE_DIR
    
    # 创建各服务的数据和配置目录
    mkdir -p mysql/{data,logs}
    mkdir -p redis/data
    mkdir -p nacos/logs
    mkdir -p rabbitmq/{data,logs}
    mkdir -p elasticsearch/{data,logs}
    mkdir -p kibana/logs
    mkdir -p minio/data
    mkdir -p nginx/{logs,html}
    mkdir -p seata
    
    # 设置权限
    chmod -R 755 .
    
    # 设置Elasticsearch数据目录权限
    if [ -d "elasticsearch" ]; then
        sudo chown -R 1000:1000 elasticsearch/ || true
    fi
    
    log_info "目录结构创建完成"
}

# 启动服务
start_services() {
    log_info "启动 $PROJECT_NAME 服务..."
    
    check_requirements
    create_directories
    
    cd $BASE_DIR
    docker-compose -f $COMPOSE_FILE up -d
    
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_services_health
    
    log_info "所有服务启动完成！"
    show_service_info
}

# 停止服务
stop_services() {
    log_info "停止 $PROJECT_NAME 服务..."
    cd $BASE_DIR
    docker-compose -f $COMPOSE_FILE down
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启 $PROJECT_NAME 服务..."
    stop_services
    sleep 5
    start_services
}

# 查看服务状态
show_status() {
    log_info "服务状态："
    cd $BASE_DIR
    docker-compose -f $COMPOSE_FILE ps
}

# 查看日志
show_logs() {
    cd $BASE_DIR
    if [ -n "$2" ]; then
        docker-compose -f $COMPOSE_FILE logs -f $2
    else
        docker-compose -f $COMPOSE_FILE logs -f
    fi
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态..."
    
    # 检查MySQL
    if docker exec wit-mysql mysqladmin ping -h localhost --silent 2>/dev/null; then
        log_info "✓ MySQL 服务正常"
    else
        log_warn "✗ MySQL 服务异常"
    fi
    
    # 检查Redis
    if docker exec wit-redis redis-cli ping 2>/dev/null | grep -q PONG; then
        log_info "✓ Redis 服务正常"
    else
        log_warn "✗ Redis 服务异常"
    fi
    
    # 检查Nacos
    if curl -s http://localhost:8848/nacos/v1/ns/operator/metrics > /dev/null 2>&1; then
        log_info "✓ Nacos 服务正常"
    else
        log_warn "✗ Nacos 服务异常"
    fi
    
    # 检查RabbitMQ
    if curl -s http://localhost:15672 > /dev/null 2>&1; then
        log_info "✓ RabbitMQ 服务正常"
    else
        log_warn "✗ RabbitMQ 服务异常"
    fi
}

# 显示服务信息
show_service_info() {
    log_info "服务访问信息："
    echo -e "${BLUE}MySQL:${NC}        localhost:3306 (root/root123456)"
    echo -e "${BLUE}Redis:${NC}        localhost:6379"
    echo -e "${BLUE}Nacos:${NC}        http://localhost:8848/nacos (nacos/nacos)"
    echo -e "${BLUE}RabbitMQ:${NC}     http://localhost:15672 (admin/admin123456)"
    echo -e "${BLUE}Elasticsearch:${NC} http://localhost:9200"
    echo -e "${BLUE}Kibana:${NC}       http://localhost:5601"
    echo -e "${BLUE}MinIO:${NC}        http://localhost:9001 (admin/admin123456)"
    echo -e "${BLUE}Sentinel:${NC}     http://localhost:8858 (sentinel/sentinel)"
    echo -e "${BLUE}XXL-Job:${NC}      http://localhost:8080/xxl-job-admin (admin/123456)"
    echo -e "${BLUE}Seata:${NC}        localhost:8091"
    echo -e "${BLUE}Nginx:${NC}        http://localhost"
}

# 主函数
main() {
    case "$1" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs $@
            ;;
        health)
            check_services_health
            ;;
        info)
            show_service_info
            ;;
        *)
            echo "使用方法: $0 {start|stop|restart|status|logs|health|info}"
            echo ""
            echo "命令说明："
            echo "  start   - 启动所有服务"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  status  - 查看服务状态"
            echo "  logs    - 查看服务日志 (可指定服务名)"
            echo "  health  - 检查服务健康状态"
            echo "  info    - 显示服务访问信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main $@

@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: WitMall Docker 部署脚本 (Windows版本)
:: 使用方法: deploy.bat [start|stop|restart|status|logs|health|info]

set PROJECT_NAME=witmall
set COMPOSE_FILE=docker-compose.yml
set BASE_DIR=%~dp0..

:: 颜色定义（Windows不支持，使用文本替代）
set INFO_PREFIX=[INFO]
set WARN_PREFIX=[WARN]
set ERROR_PREFIX=[ERROR]

:: 检查参数
if "%1"=="" goto usage
if "%1"=="start" goto start_services
if "%1"=="stop" goto stop_services
if "%1"=="restart" goto restart_services
if "%1"=="status" goto show_status
if "%1"=="logs" goto show_logs
if "%1"=="health" goto check_health
if "%1"=="info" goto show_info
goto usage

:check_requirements
echo %INFO_PREFIX% 检查环境依赖...
docker --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR_PREFIX% Docker 未安装，请先安装 Docker
    exit /b 1
)
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR_PREFIX% Docker Compose 未安装，请先安装 Docker Compose
    exit /b 1
)
echo %INFO_PREFIX% 环境检查通过
goto :eof

:create_directories
echo %INFO_PREFIX% 创建目录结构...
cd /d "%BASE_DIR%"
if not exist "mysql\data" mkdir mysql\data
if not exist "mysql\logs" mkdir mysql\logs
if not exist "redis\data" mkdir redis\data
if not exist "nacos\logs" mkdir nacos\logs
if not exist "rabbitmq\data" mkdir rabbitmq\data
if not exist "rabbitmq\logs" mkdir rabbitmq\logs
if not exist "elasticsearch\data" mkdir elasticsearch\data
if not exist "elasticsearch\logs" mkdir elasticsearch\logs
if not exist "kibana\logs" mkdir kibana\logs
if not exist "minio\data" mkdir minio\data
if not exist "nginx\logs" mkdir nginx\logs
if not exist "nginx\html" mkdir nginx\html
if not exist "seata" mkdir seata
echo %INFO_PREFIX% 目录结构创建完成
goto :eof

:start_services
echo %INFO_PREFIX% 启动 %PROJECT_NAME% 服务...
call :check_requirements
if errorlevel 1 exit /b 1
call :create_directories
cd /d "%BASE_DIR%"
docker-compose -f %COMPOSE_FILE% up -d
echo %INFO_PREFIX% 等待服务启动...
timeout /t 30 /nobreak >nul
call :check_health
echo %INFO_PREFIX% 所有服务启动完成！
call :show_info
goto :eof

:stop_services
echo %INFO_PREFIX% 停止 %PROJECT_NAME% 服务...
cd /d "%BASE_DIR%"
docker-compose -f %COMPOSE_FILE% down
echo %INFO_PREFIX% 服务已停止
goto :eof

:restart_services
echo %INFO_PREFIX% 重启 %PROJECT_NAME% 服务...
call :stop_services
timeout /t 5 /nobreak >nul
call :start_services
goto :eof

:show_status
echo %INFO_PREFIX% 服务状态：
cd /d "%BASE_DIR%"
docker-compose -f %COMPOSE_FILE% ps
goto :eof

:show_logs
cd /d "%BASE_DIR%"
if "%2"=="" (
    docker-compose -f %COMPOSE_FILE% logs -f
) else (
    docker-compose -f %COMPOSE_FILE% logs -f %2
)
goto :eof

:check_health
echo %INFO_PREFIX% 检查服务健康状态...

:: 检查MySQL
docker exec wit-mysql mysqladmin ping -h localhost --silent >nul 2>&1
if errorlevel 1 (
    echo %WARN_PREFIX% ✗ MySQL 服务异常
) else (
    echo %INFO_PREFIX% ✓ MySQL 服务正常
)

:: 检查Redis
docker exec wit-redis redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo %WARN_PREFIX% ✗ Redis 服务异常
) else (
    echo %INFO_PREFIX% ✓ Redis 服务正常
)

:: 检查Nacos
curl -s http://localhost:8848/nacos/v1/ns/operator/metrics >nul 2>&1
if errorlevel 1 (
    echo %WARN_PREFIX% ✗ Nacos 服务异常
) else (
    echo %INFO_PREFIX% ✓ Nacos 服务正常
)

:: 检查RabbitMQ
curl -s http://localhost:15672 >nul 2>&1
if errorlevel 1 (
    echo %WARN_PREFIX% ✗ RabbitMQ 服务异常
) else (
    echo %INFO_PREFIX% ✓ RabbitMQ 服务正常
)
goto :eof

:show_info
echo %INFO_PREFIX% 服务访问信息：
echo MySQL:        localhost:3306 (root/root123456)
echo Redis:        localhost:6379
echo Nacos:        http://localhost:8848/nacos (nacos/nacos)
echo RabbitMQ:     http://localhost:15672 (admin/admin123456)
echo Elasticsearch: http://localhost:9200
echo Kibana:       http://localhost:5601
echo MinIO:        http://localhost:9001 (admin/admin123456)
echo Sentinel:     http://localhost:8858 (sentinel/sentinel)
echo XXL-Job:      http://localhost:8080/xxl-job-admin (admin/123456)
echo Seata:        localhost:8091
echo Nginx:        http://localhost
goto :eof

:usage
echo 使用方法: %0 {start^|stop^|restart^|status^|logs^|health^|info}
echo.
echo 命令说明：
echo   start   - 启动所有服务
echo   stop    - 停止所有服务
echo   restart - 重启所有服务
echo   status  - 查看服务状态
echo   logs    - 查看服务日志 (可指定服务名)
echo   health  - 检查服务健康状态
echo   info    - 显示服务访问信息
exit /b 1

# 🛍️ WitMall - 企业级微服务电商平台

[![Java](https://img.shields.io/badge/Java-17-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2.1-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Spring Cloud](https://img.shields.io/badge/Spring%20Cloud-2023.0.0-blue.svg)](https://spring.io/projects/spring-cloud)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)

## 📋 项目简介

**WitMall** 是一个基于 Spring Cloud Alibaba 的**企业级微服务电商平台**，采用现代化的技术栈和最佳实践构建。项目提供完整的电商业务功能，包括用户管理、商品管理、订单处理、支付系统、库存管理等核心模块。

### ✨ 项目特色
- 🏗️ **微服务架构** - 基于Spring Cloud Alibaba构建的分布式系统
- 🚀 **开箱即用** - 完整的项目模板，一键启动开发环境
- 📚 **文档完善** - 详细的开发、部署、运维文档
- 🛠️ **代码生成** - 集成MyBatis Plus代码生成器，提高开发效率
- 🔧 **统一规范** - 统一的异常处理、返回格式、日志规范
- 📊 **监控完善** - 集成Prometheus、Grafana、ELK等监控方案

## 🛠️ 技术栈

### 核心框架
| 技术 | 版本 | 说明 |
|------|------|------|
| Spring Boot | 3.2.1 | 基础框架 |
| Spring Cloud | 2023.0.0 | 微服务框架 |
| Spring Cloud Alibaba | 2022.0.0.0 | 阿里巴巴微服务组件 |
| JDK | 17 | Java开发工具包 |

### 微服务组件
| 组件 | 版本 | 说明 |
|------|------|------|
| Nacos | 2.3.0 | 服务注册与配置中心 |
| Dubbo | 3.2.8 | 高性能RPC框架 |
| Spring Cloud Gateway | - | API网关 |
| Sentinel | 1.8.6 | 流量控制与熔断降级 |
| Seata | 1.7.1 | 分布式事务 |

### 数据存储
| 技术 | 版本 | 说明 |
|------|------|------|
| MySQL | 8.0 | 关系型数据库 |
| Redis | 7.0 | 缓存数据库 |
| Elasticsearch | 8.11 | 搜索引擎 |
| MinIO | - | 对象存储 |

### 中间件
| 中间件 | 版本 | 说明 |
|--------|------|------|
| RabbitMQ | 3.12 | 消息队列 |
| XXL-Job | 2.4.0 | 分布式任务调度 |
| Druid | 1.2.20 | 数据库连接池 |

### 开发工具
| 工具 | 版本 | 说明 |
|------|------|------|
| MyBatis Plus | 3.5.5 | ORM框架 |
| Knife4j | 4.4.0 | API文档 |
| Hutool | 5.8.24 | Java工具库 |
| Lombok | - | 代码简化 |
| FastJSON2 | 2.0.43 | JSON处理 |

## 🏗️ 项目架构

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用       │───▶│   API网关       │───▶│   微服务集群     │
│  (Web/Mobile)   │    │ (wit-gateway)   │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲                       │
                                │                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   配置中心       │    │   注册中心       │    │   基础设施       │
│    (Nacos)      │    │    (Nacos)      │    │ (MySQL/Redis)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 服务模块
| 模块 | 端口 | 说明 |
|------|------|------|
| wit-gateway | 8080 | API网关服务 |
| wit-auth | 8081 | 认证授权服务 |
| wit-user | 8082 | 用户管理服务 |
| wit-product | 8083 | 商品管理服务 |
| wit-order | 8084 | 订单管理服务 |
| wit-cart | 8085 | 购物车服务 |
| wit-payment | 8086 | 支付服务 |
| wit-inventory | 8087 | 库存管理服务 |
| wit-marketing | 8088 | 营销服务 |
| wit-search | 8089 | 搜索服务 |
| wit-recommendation | 8090 | 推荐服务 |
| wit-review | 8091 | 评价服务 |
| wit-notification | 8092 | 通知服务 |
| wit-file | 8093 | 文件服务 |
| wit-system | 8094 | 系统管理服务 |
| wit-schedule | 8095 | 定时任务服务 |
| wit-analytics | 8096 | 数据分析服务 |
| wit-aftersales | 8097 | 售后服务 |

## 📁 项目结构

```
WitMall/
├── wit-common/                 # 公共模块
│   ├── src/main/java/
│   │   └── com/wit/common/
│   │       ├── config/         # 配置类
│   │       ├── entity/         # 基础实体
│   │       ├── exception/      # 异常处理
│   │       ├── result/         # 统一返回格式
│   │       └── utils/          # 工具类
│   └── src/test/java/
│       └── com/wit/common/generator/  # 代码生成器
├── wit-gateway/                # 网关服务
├── wit-auth/                   # 认证服务
├── wit-user/                   # 用户服务
├── wit-product/                # 商品服务
├── wit-order/                  # 订单服务
├── wit-cart/                   # 购物车服务
├── wit-payment/                # 支付服务
├── wit-inventory/              # 库存服务
├── wit-marketing/              # 营销服务
├── wit-search/                 # 搜索服务
├── wit-recommendation/         # 推荐服务
├── wit-review/                 # 评价服务
├── wit-notification/           # 通知服务
├── wit-file/                   # 文件服务
├── wit-system/                 # 系统服务
├── wit-schedule/               # 定时任务服务
├── wit-analytics/              # 数据分析服务
├── wit-aftersales/             # 售后服务
├── wit-mall-web/               # 前端项目 🆕
│   ├── src/                    # 前端源码
│   │   ├── api/               # API接口
│   │   ├── components/        # 公共组件
│   │   ├── views/             # 页面组件
│   │   ├── router/            # 路由配置
│   │   ├── stores/            # 状态管理
│   │   ├── utils/             # 工具函数
│   │   └── assets/            # 静态资源
│   ├── public/                # 公共资源
│   ├── package.json           # 前端依赖配置
│   ├── vite.config.js         # Vite配置
│   └── README.md              # 前端项目说明
├── config/                     # 配置文件
│   └── nacos/                  # Nacos配置模板
├── docker-compose.yml          # Docker编排文件
├── start-all.bat              # 启动脚本(Windows)
├── stop-all.bat               # 停止脚本(Windows)
├── verify-project.bat         # 项目验证脚本
├── BUSINESS_DEVELOPMENT_GUIDE.md  # 业务开发指南
├── DEPLOYMENT_GUIDE.md           # 部署指南
├── OPERATIONS_GUIDE.md           # 运维监控指南
├── SCRIPTS_GUIDE.md              # 脚本使用指南
├── CODE_GENERATOR_GUIDE.md       # 代码生成器指南
├── PROJECT_OVERVIEW.md           # 项目总览
├── GETTING_STARTED.md            # 快速开始指南
└── FRAMEWORK_SUMMARY.md          # 框架技术总结
```

## 🚀 快速开始

### 环境要求

#### 后端环境
- **JDK 17+**
- **Maven 3.6+**
- **Docker & Docker Compose**
- **MySQL 8.0+**
- **Redis 7.0+**

#### 前端环境
- **Node.js 16+**
- **npm 8+ / yarn 1.22+ / pnpm 7+**

### 1. 克隆项目
```bash
git clone <your-repository-url>
cd wit-mall
```

### 2. 启动基础设施
```bash
# 启动MySQL、Redis、Nacos等基础服务
docker-compose up -d

# 等待服务启动完成（约2-3分钟）
docker-compose ps
```

### 3. 启动后端服务

#### 编译后端项目
```bash
# 清理并编译所有模块
mvn clean compile -DskipTests
```

#### 启动微服务
```bash
# Windows环境
start-all.bat

# Linux/Mac环境
chmod +x start-all.sh
./start-all.sh
```

### 4. 启动前端项目

#### 安装前端依赖
```bash
cd wit-mall-web
npm install
# 或使用 yarn install
# 或使用 pnpm install
```

#### 启动前端开发服务器
```bash
npm run dev
# 或 yarn dev
# 或 pnpm dev
```

### 5. 验证部署
访问以下地址验证服务是否正常：

#### 后端服务
- **Nacos控制台**: http://localhost:8848/nacos (nacos/nacos)
- **API网关**: http://localhost:8080/actuator/health
- **API文档**: http://localhost:8080/doc.html

#### 前端应用
- **前端应用**: http://localhost:3000
- **管理后台**: http://localhost:3000/admin

## 🛠️ 开发指南

### 后端开发

#### 代码生成器
项目集成了MyBatis Plus代码生成器，可快速生成CRUD代码：

```bash
# 运行交互式代码生成器
cd wit-common
mvn test-compile exec:java -Dexec.mainClass="com.wit.common.generator.CodeGenerator"
```

详细使用方法请参考：[代码生成器指南](CODE_GENERATOR_GUIDE.md)

#### 后端开发流程
1. **数据库设计** - 设计业务表结构
2. **代码生成** - 使用代码生成器生成基础代码
3. **业务开发** - 在生成代码基础上实现业务逻辑
4. **接口测试** - 使用Knife4j进行接口测试
5. **单元测试** - 编写并运行单元测试

详细开发指南请参考：[业务开发指南](BUSINESS_DEVELOPMENT_GUIDE.md)

### 前端开发

#### 前端开发流程
1. **页面设计** - 根据原型设计页面结构
2. **组件开发** - 创建可复用的Vue组件
3. **API集成** - 调用后端接口获取数据
4. **状态管理** - 使用Pinia管理应用状态
5. **样式开发** - 使用SCSS编写响应式样式
6. **测试验证** - 在不同设备上测试页面效果

#### 前端技术栈
- **Vue 3** + **Composition API**
- **Vite** - 极速构建工具
- **Element Plus** - UI组件库
- **Vue Router 4** - 路由管理
- **Pinia** - 状态管理
- **Axios** - HTTP客户端
- **SCSS** - CSS预处理器

前端项目详细说明请参考：[前端项目README](wit-mall-web/README.md)

## 📚 文档中心

| 文档 | 说明 |
|------|------|
| [项目总览](PROJECT_OVERVIEW.md) | 项目整体介绍和架构说明 |
| [快速开始](GETTING_STARTED.md) | 环境搭建和快速启动指南 |
| [业务开发指南](BUSINESS_DEVELOPMENT_GUIDE.md) | 详细的业务代码开发流程 |
| [代码生成器指南](CODE_GENERATOR_GUIDE.md) | MyBatis Plus代码生成器使用 |
| [部署指南](DEPLOYMENT_GUIDE.md) | 本地、测试、生产环境部署 |
| [运维监控指南](OPERATIONS_GUIDE.md) | 系统监控和运维管理 |
| [脚本使用指南](SCRIPTS_GUIDE.md) | 项目脚本详细说明 |
| [框架总结](FRAMEWORK_SUMMARY.md) | 技术选型和框架说明 |

## 🔧 核心功能

### 统一基础设施
- ✅ **统一配置管理** - Nacos配置中心
- ✅ **服务注册发现** - Nacos注册中心
- ✅ **API网关** - Spring Cloud Gateway
- ✅ **负载均衡** - Ribbon/LoadBalancer
- ✅ **熔断降级** - Sentinel
- ✅ **分布式事务** - Seata
- ✅ **链路追踪** - Sleuth + Zipkin

### 开发效率工具
- ✅ **代码生成器** - MyBatis Plus Generator
- ✅ **API文档** - Knife4j/Swagger
- ✅ **统一异常处理** - GlobalExceptionHandler
- ✅ **统一返回格式** - Result封装
- ✅ **参数校验** - Validation
- ✅ **日志管理** - Logback + ELK

### 安全认证
- ✅ **JWT认证** - 无状态认证
- ✅ **权限控制** - RBAC权限模型
- ✅ **接口鉴权** - 统一鉴权拦截器
- ✅ **数据脱敏** - 敏感数据处理

## 📊 监控运维

### 系统监控
- **应用监控** - Spring Boot Actuator
- **指标收集** - Prometheus
- **可视化** - Grafana
- **日志分析** - ELK Stack
- **链路追踪** - Jaeger

### 部署方案
- **容器化** - Docker + Docker Compose
- **编排** - Kubernetes
- **CI/CD** - Jenkins/GitHub Actions
- **监控告警** - AlertManager + 钉钉

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目基于 [Apache License 2.0](LICENSE) 许可证开源。

## 📞 联系我们

- **项目地址**: https://github.com/your-username/wit-mall
- **问题反馈**: https://github.com/your-username/wit-mall/issues
- **邮箱**: <EMAIL>

---

⭐ 如果这个项目对您有帮助，请给我们一个Star！

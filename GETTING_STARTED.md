# 🚀 Wit Mall 微服务项目快速上手指南

## 📋 项目简介

Wit Mall 是一个完整的微服务商城学习项目，基于 Spring Cloud Alibaba 构建，包含了18个微服务模块和完整的基础设施支撑。

## 🛠️ 环境要求

### 必需环境
- **JDK 17+** - Java开发环境
- **Maven 3.6+** - 项目构建工具
- **Docker & Docker Compose** - 容器化环境
- **Git** - 版本控制

### 推荐环境
- **IntelliJ IDEA** - 开发IDE
- **Postman** - API测试工具
- **Redis Desktop Manager** - Redis可视化工具

## 🏗️ 项目结构

```
wit-mall/
├── wit-common/                 # 公共模块
├── wit-gateway/               # 网关服务 (8080)
├── wit-auth/                  # 认证授权服务 (8081)
├── wit-user/                  # 用户服务 (8082)
├── wit-product/               # 商品服务 (8083)
├── wit-order/                 # 订单服务 (8084)
├── wit-cart/                  # 购物车服务 (8085)
├── wit-payment/               # 支付服务 (8086)
├── wit-inventory/             # 库存服务 (8087)
├── wit-marketing/             # 营销服务 (8088)
├── wit-search/                # 搜索服务 (8089)
├── wit-recommendation/        # 推荐服务 (8090)
├── wit-review/                # 评价服务 (8091)
├── wit-notification/          # 消息通知服务 (8092)
├── wit-file/                  # 文件服务 (8093)
├── wit-system/                # 系统管理服务 (8094)
├── wit-schedule/              # 定时任务服务 (8095)
├── wit-analytics/             # 统计报表服务 (8096)
├── wit-aftersales/            # 售后服务 (8097)
├── config/                    # 配置文件模板
├── docker/                    # Docker配置
└── docker-compose.yml         # 基础设施编排
```

## 🚀 快速启动

### 第一步：启动基础设施

```bash
# 启动所有基础设施服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

等待所有服务启动完成（大约2-3分钟）

### 第二步：编译项目

```bash
# 清理并编译项目
./mvnw clean compile -DskipTests

# 或者使用Maven（如果已安装）
mvn clean compile -DskipTests
```

### 第三步：启动微服务

#### Windows用户
```bash
# 使用启动脚本
start-all.bat
```

#### Linux/Mac用户
```bash
# 给脚本执行权限
chmod +x start-all.sh

# 启动所有服务
./start-all.sh
```

#### 手动启动（推荐用于开发）
```bash
# 启动网关服务
cd wit-gateway
mvn spring-boot:run

# 启动认证服务（新终端）
cd wit-auth
mvn spring-boot:run

# 启动用户服务（新终端）
cd wit-user
mvn spring-boot:run

# ... 其他服务类似
```

## 🌐 服务访问地址

### 核心服务
| 服务名称 | 访问地址 | 说明 |
|---------|---------|------|
| API网关 | http://localhost:8080 | 统一API入口 |
| 认证服务 | http://localhost:8081 | OAuth2认证 |
| 用户服务 | http://localhost:8082 | 用户管理 |
| 商品服务 | http://localhost:8083 | 商品管理 |

### 基础设施
| 服务名称 | 访问地址 | 用户名/密码 |
|---------|---------|-------------|
| Nacos控制台 | http://localhost:8848/nacos | nacos/nacos |
| Sentinel控制台 | http://localhost:8858 | sentinel/sentinel |
| RabbitMQ控制台 | http://localhost:15672 | admin/admin123 |
| MinIO控制台 | http://localhost:9001 | minioadmin/minioadmin |
| Kibana | http://localhost:5601 | - |
| XXL-Job控制台 | http://localhost:8080/xxl-job-admin | admin/123456 |

## 📝 配置说明

### Nacos配置

项目使用Nacos作为配置中心，配置文件模板位于 `config/nacos/` 目录：

- `common-config.yml` - 公共配置
- `mysql-config.yml` - 数据库配置
- `redis-config.yml` - Redis配置
- `rabbitmq-config.yml` - RabbitMQ配置
- `dubbo-config.yml` - Dubbo配置

### 数据库配置

每个服务使用独立的数据库：
- `wit_auth` - 认证服务数据库
- `wit_user` - 用户服务数据库
- `wit_product` - 商品服务数据库
- ... 其他服务类似

## 🔧 开发指南

### 1. 添加新的API接口

```java
@RestController
@RequestMapping("/api/v1/users")
@Api(tags = "用户管理")
public class UserController {
    
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询用户")
    public Result<User> getUserById(@PathVariable Long id) {
        // 业务逻辑
        return Result.success(user);
    }
}
```

### 2. 使用统一返回格式

```java
// 成功返回
return Result.success(data);

// 失败返回
return Result.error("操作失败");

// 自定义状态码
return Result.error(ResultCode.USER_NOT_FOUND);
```

### 3. 异常处理

```java
// 抛出业务异常
throw new BusinessException("用户不存在");

// 使用预定义异常
throw new BusinessException(ResultCode.USER_NOT_FOUND);
```

### 4. 数据库操作

```java
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    
    public User getUserById(Long id) {
        return this.getById(id);
    }
    
    public Page<User> getUserPage(int current, int size) {
        return this.page(new Page<>(current, size));
    }
}
```

## 🧪 测试指南

### 1. 健康检查

```bash
# 检查网关服务
curl http://localhost:8080/actuator/health

# 检查用户服务
curl http://localhost:8082/actuator/health
```

### 2. API测试

使用Postman或curl测试API：

```bash
# 测试用户服务
curl -X GET http://localhost:8080/user/api/v1/users/1
```

### 3. 查看服务注册

访问Nacos控制台查看服务注册情况：
http://localhost:8848/nacos

## 🐛 常见问题

### 1. 端口冲突
如果遇到端口冲突，可以修改各服务的 `bootstrap.yml` 中的端口配置。

### 2. 依赖下载慢
配置Maven镜像：
```xml
<mirror>
    <id>aliyun</id>
    <mirrorOf>central</mirrorOf>
    <url>https://maven.aliyun.com/repository/public</url>
</mirror>
```

### 3. Docker服务启动失败
```bash
# 查看日志
docker-compose logs [service-name]

# 重启服务
docker-compose restart [service-name]
```

### 4. 服务注册失败
检查Nacos是否正常启动，确保网络连接正常。

## 📚 学习路径

### 初级阶段
1. 熟悉项目结构和配置
2. 理解微服务基本概念
3. 学习Spring Boot基础

### 中级阶段
1. 深入学习Spring Cloud组件
2. 理解服务注册与发现
3. 掌握配置管理

### 高级阶段
1. 学习分布式事务
2. 掌握服务治理
3. 实践DevOps部署

## 🔗 相关文档

- [项目总览](PROJECT_OVERVIEW.md)
- [框架总结](FRAMEWORK_SUMMARY.md)
- [详细说明](README.md)

## 💡 提示

1. **首次启动**：建议先启动基础设施，等待2-3分钟后再启动微服务
2. **开发调试**：推荐使用IDE直接运行单个服务进行调试
3. **日志查看**：各服务日志会输出到控制台，便于调试
4. **配置修改**：修改配置后需要重启对应服务

## 🎯 下一步

1. 尝试启动所有服务
2. 访问各个管理控制台
3. 开始编写第一个API接口
4. 学习微服务间的调用

祝您学习愉快！🚀

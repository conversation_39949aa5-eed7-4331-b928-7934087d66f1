package com.wit.cart;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 购物车服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.wit.cart", "com.wit.common"})
@EnableDiscoveryClient
@EnableDubbo
public class CartApplication {

    public static void main(String[] args) {
        SpringApplication.run(CartApplication.class, args);
    }
}

server:
  port: 8084

spring:
  application:
    name: wit-order
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: wit-mall
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        namespace: wit-mall
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: wit-jwt-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: mysql-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: redis-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: rabbitmq-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: dubbo-config.yml
            group: DEFAULT_GROUP
            refresh: true

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# Redis 配置
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

# 日志配置
logging:
  level:
    com.wit.order: debug
    org.springframework.security: debug
    org.springframework.web: debug
    org.mybatis: debug

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Dubbo 配置
dubbo:
  application:
    name: wit-order
  registry:
    address: nacos://localhost:8848
    parameters:
      namespace: wit-mall
  protocol:
    name: dubbo
    port: 20884
  scan:
    base-packages: com.wit.order.service.impl

# Seata 配置
seata:
  enabled: true
  application-id: wit-order
  tx-service-group: wit-order-group
  registry:
    type: nacos
    nacos:
      server-addr: localhost:8848
      namespace: wit-mall
      group: SEATA_GROUP
  config:
    type: nacos
    nacos:
      server-addr: localhost:8848
      namespace: wit-mall
      group: SEATA_GROUP

# Sentinel 配置
spring:
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8080
        port: 8720
      datasource:
        ds1:
          nacos:
            server-addr: localhost:8848
            dataId: wit-order-sentinel
            groupId: SENTINEL_GROUP
            rule-type: flow

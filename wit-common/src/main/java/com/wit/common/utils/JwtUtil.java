package com.wit.common.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * JWT工具类
 * 统一的JWT token生成和验证工具
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtUtil {

    /**
     * JWT密钥
     */
    @Value("${wit.jwt.secret:wit-mall-default-secret-key-2024-very-long-secret-for-security}")
    private String jwtSecret;

    /**
     * JWT过期时间（秒）
     */
    @Value("${wit.jwt.expiration:7200}")
    private Long jwtExpiration;

    /**
     * JWT刷新token过期时间（秒）
     */
    @Value("${wit.jwt.refresh-expiration:604800}")
    private Long refreshExpiration;

    /**
     * JWT签发者
     */
    @Value("${wit.jwt.issuer:wit-mall}")
    private String jwtIssuer;

    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 生成JWT token
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param tenantId 租户ID
     * @param roles 用户角色
     * @param extraClaims 额外的声明
     * @return JWT token
     */
    public String generateToken(String userId, String username, String tenantId, 
                               List<String> roles, Map<String, Object> extraClaims) {
        try {
            Date now = new Date();
            Date expiryDate = new Date(now.getTime() + jwtExpiration * 1000);

            var builder = Jwts.builder()
                    .subject(userId)
                    .claim("username", username)
                    .claim("tenantId", tenantId)
                    .claim("roles", roles)
                    .issuer(jwtIssuer)
                    .issuedAt(now)
                    .expiration(expiryDate)
                    .signWith(getSigningKey());

            // 添加额外的声明
            if (extraClaims != null && !extraClaims.isEmpty()) {
                extraClaims.forEach(builder::claim);
            }

            return builder.compact();
        } catch (Exception e) {
            log.error("生成JWT token失败", e);
            throw new RuntimeException("生成JWT token失败", e);
        }
    }

    /**
     * 生成刷新token
     * 
     * @param userId 用户ID
     * @return 刷新token
     */
    public String generateRefreshToken(String userId) {
        try {
            Date now = new Date();
            Date expiryDate = new Date(now.getTime() + refreshExpiration * 1000);

            return Jwts.builder()
                    .subject(userId)
                    .claim("type", "refresh")
                    .issuer(jwtIssuer)
                    .issuedAt(now)
                    .expiration(expiryDate)
                    .signWith(getSigningKey())
                    .compact();
        } catch (Exception e) {
            log.error("生成刷新token失败", e);
            throw new RuntimeException("生成刷新token失败", e);
        }
    }

    /**
     * 解析JWT token
     *
     * @param token JWT token
     * @return Claims
     */
    public Claims parseToken(String token) {
        try {
            return Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (Exception e) {
            log.error("解析JWT token失败: {}", e.getMessage());
            throw new RuntimeException("解析JWT token失败", e);
        }
    }

    /**
     * 验证JWT token
     * 
     * @param token JWT token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = parseToken(token);
            return !isTokenExpired(claims);
        } catch (Exception e) {
            log.debug("JWT token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查token是否过期
     * 
     * @param claims JWT声明
     * @return 是否过期
     */
    private boolean isTokenExpired(Claims claims) {
        Date expiration = claims.getExpiration();
        return expiration.before(new Date());
    }

    /**
     * 从token中获取用户ID
     * 
     * @param token JWT token
     * @return 用户ID
     */
    public String getUserIdFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.getSubject();
    }

    /**
     * 从token中获取用户名
     * 
     * @param token JWT token
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.get("username", String.class);
    }

    /**
     * 从token中获取租户ID
     * 
     * @param token JWT token
     * @return 租户ID
     */
    public String getTenantIdFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.get("tenantId", String.class);
    }

    /**
     * 从token中获取用户角色
     * 
     * @param token JWT token
     * @return 用户角色列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getRolesFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.get("roles", List.class);
    }

    /**
     * 获取token剩余有效时间（秒）
     * 
     * @param token JWT token
     * @return 剩余有效时间
     */
    public long getTokenRemainingTime(String token) {
        try {
            Claims claims = parseToken(token);
            Date expiration = claims.getExpiration();
            long remainingTime = (expiration.getTime() - System.currentTimeMillis()) / 1000;
            return Math.max(0, remainingTime);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 检查token是否即将过期（默认30分钟内）
     * 
     * @param token JWT token
     * @param thresholdSeconds 阈值时间（秒）
     * @return 是否即将过期
     */
    public boolean isTokenExpiringSoon(String token, long thresholdSeconds) {
        long remainingTime = getTokenRemainingTime(token);
        return remainingTime > 0 && remainingTime <= thresholdSeconds;
    }

    /**
     * 检查token是否即将过期（默认30分钟内）
     * 
     * @param token JWT token
     * @return 是否即将过期
     */
    public boolean isTokenExpiringSoon(String token) {
        return isTokenExpiringSoon(token, 1800); // 30分钟
    }
}

package com.wit.common.interceptor;

import com.wit.common.utils.JwtUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * JWT认证拦截器
 * 统一的JWT token验证拦截器，供各个微服务使用
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 白名单路径（不需要认证）
     */
    private static final List<String> WHITE_LIST_PATHS = Arrays.asList(
        "/auth/login",
        "/auth/register", 
        "/auth/refresh",
        "/auth/captcha",
        "/product/list",
        "/product/detail",
        "/search",
        "/health",
        "/actuator",
        "/doc.html",
        "/swagger",
        "/v3/api-docs",
        "/favicon.ico"
    );

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        log.debug("JWT拦截器处理请求: {} {}", method, requestURI);

        // 检查是否在白名单中
        if (isWhiteListPath(requestURI)) {
            log.debug("白名单路径，跳过认证: {}", requestURI);
            return true;
        }

        // OPTIONS请求直接放行
        if ("OPTIONS".equals(method)) {
            return true;
        }

        // 获取token
        String token = extractToken(request);
        if (!StringUtils.hasText(token)) {
            log.warn("缺少认证token: {}", requestURI);
            handleAuthError(response, "缺少认证token", 401);
            return false;
        }

        try {
            // 验证token
            if (!jwtUtil.validateToken(token)) {
                log.warn("token验证失败: {}", requestURI);
                handleAuthError(response, "token无效或已过期", 401);
                return false;
            }

            // 解析token并设置用户信息到请求属性
            Claims claims = jwtUtil.parseToken(token);
            String userId = claims.getSubject();
            String username = claims.get("username", String.class);
            String tenantId = claims.get("tenantId", String.class);
            @SuppressWarnings("unchecked")
            List<String> roles = claims.get("roles", List.class);

            // 设置用户信息到请求属性
            request.setAttribute("userId", userId);
            request.setAttribute("username", username);
            request.setAttribute("tenantId", tenantId);
            request.setAttribute("roles", roles);

            // 设置用户信息到请求头（供下游服务使用）
            response.setHeader("X-User-Id", userId);
            response.setHeader("X-Username", username);
            response.setHeader("X-Tenant-Id", tenantId);
            if (roles != null && !roles.isEmpty()) {
                response.setHeader("X-User-Roles", String.join(",", roles));
            }

            log.debug("token验证成功: userId={}, tenantId={}", userId, tenantId);
            return true;

        } catch (Exception e) {
            log.error("token验证过程中发生错误: {}", e.getMessage());
            handleAuthError(response, "认证失败", 401);
            return false;
        }
    }

    /**
     * 检查是否为白名单路径
     */
    private boolean isWhiteListPath(String path) {
        return WHITE_LIST_PATHS.stream().anyMatch(whitePath -> {
            if (whitePath.endsWith("/**")) {
                String prefix = whitePath.substring(0, whitePath.length() - 3);
                return path.startsWith(prefix);
            } else if (whitePath.endsWith("/*")) {
                String prefix = whitePath.substring(0, whitePath.length() - 2);
                return path.startsWith(prefix) && path.indexOf('/', prefix.length()) == -1;
            } else {
                return path.equals(whitePath) || path.contains(whitePath);
            }
        });
    }

    /**
     * 从请求中提取token
     */
    private String extractToken(HttpServletRequest request) {
        // 1. 从Authorization头获取
        String authHeader = request.getHeader("Authorization");
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }

        // 2. 从请求参数获取
        String tokenParam = request.getParameter("token");
        if (StringUtils.hasText(tokenParam)) {
            return tokenParam;
        }

        // 3. 从X-Token头获取
        String xToken = request.getHeader("X-Token");
        if (StringUtils.hasText(xToken)) {
            return xToken;
        }

        return null;
    }

    /**
     * 处理认证错误
     */
    private void handleAuthError(HttpServletResponse response, String message, int status) throws Exception {
        response.setStatus(status);
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "*");
        response.setHeader("Access-Control-Allow-Headers", "*");
        
        String errorResponse = String.format(
            "{\"code\":%d,\"message\":\"%s\",\"timestamp\":%d}",
            status, message, System.currentTimeMillis()
        );
        
        response.getWriter().write(errorResponse);
        response.getWriter().flush();
    }
}

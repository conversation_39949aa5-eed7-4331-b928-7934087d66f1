package com.wit.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * JWT配置属性
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "wit.jwt")
public class JwtProperties {

    /**
     * JWT密钥
     */
    private String secret = "wit-mall-default-secret-key-2024-very-long-secret-for-security";

    /**
     * JWT过期时间（秒）
     */
    private Long expiration = 7200L; // 2小时

    /**
     * 刷新token过期时间（秒）
     */
    private Long refreshExpiration = 604800L; // 7天

    /**
     * JWT签发者
     */
    private String issuer = "wit-mall";

    /**
     * JWT算法
     */
    private String algorithm = "HS256";

    /**
     * 是否在响应头中返回token
     */
    private Boolean responseHeader = true;

    /**
     * token请求头名称
     */
    private String headerName = "Authorization";

    /**
     * token前缀
     */
    private String tokenPrefix = "Bearer ";

    /**
     * 租户头名称
     */
    private String tenantHeaderName = "X-Tenant-Id";
}

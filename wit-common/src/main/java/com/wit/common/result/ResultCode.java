package com.wit.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返回状态码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 业务状态码
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    PASSWORD_ERROR(1003, "密码错误"),
    TOKEN_EXPIRED(1004, "Token已过期"),
    TOKEN_INVALID(1005, "Token无效"),
    
    PRODUCT_NOT_FOUND(2001, "商品不存在"),
    PRODUCT_STOCK_INSUFFICIENT(2002, "商品库存不足"),
    
    ORDER_NOT_FOUND(3001, "订单不存在"),
    ORDER_STATUS_ERROR(3002, "订单状态错误"),
    ORDER_CANNOT_CANCEL(3003, "订单无法取消"),
    
    PAYMENT_FAILED(4001, "支付失败"),
    PAYMENT_TIMEOUT(4002, "支付超时"),
    
    CART_ITEM_NOT_FOUND(5001, "购物车商品不存在"),
    
    INVENTORY_LOCK_FAILED(6001, "库存锁定失败"),
    INVENTORY_UNLOCK_FAILED(6002, "库存解锁失败"),
    
    FILE_UPLOAD_FAILED(7001, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(7002, "文件类型不支持"),
    
    SYSTEM_BUSY(9001, "系统繁忙，请稍后重试"),
    SYSTEM_MAINTENANCE(9002, "系统维护中");

    private final Integer code;
    private final String message;
}

package com.wit.common.generator;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;

import java.util.Collections;

/**
 * 简化版代码生成器 - 预配置常用参数
 * 
 * <AUTHOR>
 */
public class SimpleCodeGenerator {

    // 数据库配置
    private static final String DB_URL = "*********************************************************************************************************************";
    private static final String DB_USERNAME = "root";
    private static final String DB_PASSWORD = "123456";
    
    // 生成配置
    private static final String AUTHOR = "Wit";
    private static final String SERVICE_NAME = "user"; // 修改这里的服务名
    private static final String TABLE_NAMES = "user,user_role"; // 修改这里的表名，多个用逗号分隔
    private static final String TABLE_PREFIX = ""; // 表前缀，如 t_
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("   Wit Mall 简化版代码生成器");
        System.out.println("========================================");
        System.out.println("服务名称: " + SERVICE_NAME);
        System.out.println("表名称: " + TABLE_NAMES);
        System.out.println("作者: " + AUTHOR);
        System.out.println("========================================");
        
        generateCode();
        
        System.out.println("代码生成完成！");
        System.out.println("生成路径: wit-" + SERVICE_NAME + "/src/main/java/com/wit/" + SERVICE_NAME);
    }
    
    /**
     * 生成代码
     */
    private static void generateCode() {
        // 获取项目根路径
        String projectPath = System.getProperty("user.dir");
        String modulePath = projectPath + "/wit-" + SERVICE_NAME;
        
        FastAutoGenerator.create(DB_URL, DB_USERNAME, DB_PASSWORD)
                
                // 全局配置
                .globalConfig(builder -> {
                    builder.author(AUTHOR) // 设置作者
                            .enableSwagger() // 开启 swagger 模式
                            .outputDir(modulePath + "/src/main/java") // 指定输出目录
                            .dateType(DateType.TIME_PACK) // 时间策略
                            .commentDate("yyyy-MM-dd HH:mm:ss") // 注释日期
                            .disableOpenDir(); // 禁止打开输出目录
                })
                
                // 包配置
                .packageConfig(builder -> {
                    builder.parent("com.wit." + SERVICE_NAME) // 设置父包名
                            .entity("entity") // 设置实体类包名
                            .mapper("mapper") // 设置 Mapper 接口包名
                            .service("service") // 设置 Service 接口包名
                            .serviceImpl("service.impl") // 设置 Service 实现类包名
                            .controller("controller") // 设置 Controller 包名
                            .pathInfo(Collections.singletonMap(OutputFile.xml, 
                                    modulePath + "/src/main/resources/mapper")); // 设置mapperXml生成路径
                })
                
                // 策略配置
                .strategyConfig(builder -> {
                    builder.addInclude(TABLE_NAMES.split(",")) // 设置需要生成的表名
                            .addTablePrefix(TABLE_PREFIX.isEmpty() ? new String[]{} : TABLE_PREFIX.split(",")) // 设置过滤表前缀
                            
                            // Entity 策略配置
                            .entityBuilder()
                            .enableLombok() // 开启 lombok 模型
                            .enableTableFieldAnnotation() // 开启生成实体时生成字段注解
                            .enableActiveRecord() // 开启 ActiveRecord 模式
                            .versionColumnName("version") // 乐观锁字段名(数据库)
                            .logicDeleteColumnName("deleted") // 逻辑删除字段名(数据库)
                            .naming(NamingStrategy.underline_to_camel) // 数据库表映射到实体的命名策略
                            .columnNaming(NamingStrategy.underline_to_camel) // 数据库表字段映射到实体的命名策略
                            .addSuperEntityColumns("id", "create_time", "update_time", "create_by", "update_by", "deleted", "version") // 添加父类公共字段
                            .addTableFills(new Column("create_time", FieldFill.INSERT)) // 基于数据库字段填充
                            .addTableFills(new Column("update_time", FieldFill.INSERT_UPDATE))
                            .addTableFills(new Column("create_by", FieldFill.INSERT))
                            .addTableFills(new Column("update_by", FieldFill.INSERT_UPDATE))
                            .idType(IdType.ASSIGN_ID) // 全局主键类型
                            .formatFileName("%s") // 格式化文件名称
                            
                            // Mapper 策略配置
                            .mapperBuilder()
                            .enableMapperAnnotation() // 开启 @Mapper 注解
                            .enableBaseResultMap() // 启用 BaseResultMap 生成
                            .enableBaseColumnList() // 启用 BaseColumnList
                            .formatMapperFileName("%sMapper") // 格式化 mapper 文件名称
                            .formatXmlFileName("%sMapper") // 格式化 xml 实现类文件名称
                            
                            // Service 策略配置
                            .serviceBuilder()
                            .formatServiceFileName("%sService") // 格式化 service 接口文件名称
                            .formatServiceImplFileName("%sServiceImpl") // 格式化 service 实现类文件名称
                            
                            // Controller 策略配置
                            .controllerBuilder()
                            .enableHyphenStyle() // 开启驼峰转连字符
                            .enableRestStyle() // 开启生成@RestController 控制器
                            .formatFileName("%sController"); // 格式化文件名称
                })
                
                // 模板引擎配置
                .templateEngine(new FreemarkerTemplateEngine())
                
                // 执行生成
                .execute();
    }
}

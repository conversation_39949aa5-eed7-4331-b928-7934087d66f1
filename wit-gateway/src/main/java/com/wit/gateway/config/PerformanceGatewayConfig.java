package com.wit.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * 高性能网关配置
 * 针对多租户场景优化的路由配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@Profile("performance") // 只在performance环境下启用
public class PerformanceGatewayConfig {

    /**
     * 高性能多租户路由配置
     * 使用更简洁的路由规则，减少处理开销
     */
    @Bean
    public RouteLocator performanceRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            // 使用通用路由规则，减少路由数量
            .route("tenant-services", r -> r
                .path("/{tenantId}/{service}/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    // 快速正则匹配，避免复杂的字符串操作
                    return path.matches("^/\\d+/[a-zA-Z-]+/.*$");
                })
                .filters(f -> f
                    // 自定义过滤器，直接处理路由转换
                    .filter((exchange, chain) -> {
                        String path = exchange.getRequest().getURI().getPath();
                        String[] parts = path.split("/", 4);
                        
                        if (parts.length >= 3) {
                            String tenantId = parts[1];
                            String service = parts[2];
                            String servicePath = parts.length > 3 ? "/" + parts[3] : "/";
                            
                            // 构建目标URI
                            String targetUri = "lb://wit-" + service;
                            
                            // 修改请求
                            var modifiedRequest = exchange.getRequest().mutate()
                                .path(servicePath)
                                .header("X-Tenant-Id", tenantId)
                                .header("X-Service-Name", service)
                                .build();
                            
                            var modifiedExchange = exchange.mutate()
                                .request(modifiedRequest)
                                .build();
                            
                            log.debug("高性能路由: {} -> {} (租户: {})", path, servicePath, tenantId);
                            
                            return chain.filter(modifiedExchange);
                        }
                        
                        return chain.filter(exchange);
                    })
                )
                // 动态URI，根据服务名路由
                .uri("lb://wit-user") // 这里会被过滤器动态替换
            )
            .build();
    }
}

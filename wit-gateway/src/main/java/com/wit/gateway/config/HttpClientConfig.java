package com.wit.gateway.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.gateway.config.HttpClientCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * HTTP客户端性能配置
 * 优化网关的HTTP客户端连接池和超时设置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class HttpClientConfig {

    /**
     * 自定义HTTP客户端配置
     */
    @Bean
    public HttpClientCustomizer httpClientCustomizer() {
        return httpClient -> {
            // 配置连接池
            ConnectionProvider connectionProvider = ConnectionProvider.builder("wit-gateway-pool")
                .maxConnections(1000)                    // 最大连接数
                .maxIdleTime(Duration.ofSeconds(30))     // 最大空闲时间
                .maxLifeTime(Duration.ofMinutes(10))     // 连接最大生存时间
                .pendingAcquireTimeout(Duration.ofSeconds(60)) // 获取连接超时时间
                .evictInBackground(Duration.ofSeconds(120))    // 后台清理间隔
                .build();

            return httpClient
                .connectionProvider(connectionProvider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000) // 连接超时10秒
                .option(ChannelOption.SO_KEEPALIVE, true)            // 启用TCP keepalive
                .option(ChannelOption.TCP_NODELAY, true)             // 禁用Nagle算法
                .option(ChannelOption.SO_REUSEADDR, true)            // 启用地址重用
                .doOnConnected(conn -> {
                    conn.addHandlerLast(new ReadTimeoutHandler(30, TimeUnit.SECONDS))   // 读超时30秒
                        .addHandlerLast(new WriteTimeoutHandler(30, TimeUnit.SECONDS));  // 写超时30秒
                })
                .compress(true)  // 启用压缩
                .keepAlive(true) // 启用HTTP keep-alive
                .responseTimeout(Duration.ofSeconds(30)) // 响应超时30秒
                .followRedirect(false); // 禁用自动重定向
        };
    }

    /**
     * 自定义Reactor客户端HTTP连接器
     */
    @Bean
    public ReactorClientHttpConnector reactorClientHttpConnector(HttpClientCustomizer customizer) {
        HttpClient httpClient = HttpClient.create();
        httpClient = customizer.customize(httpClient);
        
        log.info("🚀 网关HTTP客户端配置完成:");
        log.info("   - 连接池大小: 1000");
        log.info("   - 连接超时: 10s");
        log.info("   - 响应超时: 30s");
        log.info("   - 启用压缩: true");
        log.info("   - 启用Keep-Alive: true");
        
        return new ReactorClientHttpConnector(httpClient);
    }
    
    /**
     * 网关性能配置属性
     */
    @ConfigurationProperties(prefix = "wit.gateway.performance")
    public static class GatewayPerformanceProperties {
        private int maxConnections = 1000;
        private int connectTimeoutMs = 10000;
        private int responseTimeoutMs = 30000;
        private int readTimeoutMs = 30000;
        private int writeTimeoutMs = 30000;
        private boolean compressionEnabled = true;
        private boolean keepAliveEnabled = true;
        
        // getters and setters
        public int getMaxConnections() { return maxConnections; }
        public void setMaxConnections(int maxConnections) { this.maxConnections = maxConnections; }
        
        public int getConnectTimeoutMs() { return connectTimeoutMs; }
        public void setConnectTimeoutMs(int connectTimeoutMs) { this.connectTimeoutMs = connectTimeoutMs; }
        
        public int getResponseTimeoutMs() { return responseTimeoutMs; }
        public void setResponseTimeoutMs(int responseTimeoutMs) { this.responseTimeoutMs = responseTimeoutMs; }
        
        public int getReadTimeoutMs() { return readTimeoutMs; }
        public void setReadTimeoutMs(int readTimeoutMs) { this.readTimeoutMs = readTimeoutMs; }
        
        public int getWriteTimeoutMs() { return writeTimeoutMs; }
        public void setWriteTimeoutMs(int writeTimeoutMs) { this.writeTimeoutMs = writeTimeoutMs; }
        
        public boolean isCompressionEnabled() { return compressionEnabled; }
        public void setCompressionEnabled(boolean compressionEnabled) { this.compressionEnabled = compressionEnabled; }
        
        public boolean isKeepAliveEnabled() { return keepAliveEnabled; }
        public void setKeepAliveEnabled(boolean keepAliveEnabled) { this.keepAliveEnabled = keepAliveEnabled; }
    }
}

package com.wit.gateway.controller;

import com.wit.gateway.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 网关管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/gateway")
public class GatewayController {

    @Autowired
    private TenantService tenantService;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Mono<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "wit-gateway");
        result.put("timestamp", LocalDateTime.now());
        result.put("version", "1.0.0");
        return Mono.just(result);
    }

    /**
     * 获取网关信息
     */
    @GetMapping("/info")
    public Mono<Map<String, Object>> info() {
        Map<String, Object> result = new HashMap<>();
        result.put("name", "Wit Mall Gateway");
        result.put("description", "多租户微服务网关");
        result.put("version", "1.0.0");
        result.put("features", new String[]{
            "多租户路由",
            "负载均衡", 
            "熔断降级",
            "限流控制",
            "链路追踪"
        });
        result.put("timestamp", LocalDateTime.now());
        return Mono.just(result);
    }

    /**
     * 获取活跃租户列表
     */
    @GetMapping("/tenants")
    public Mono<Map<String, Object>> getActiveTenants() {
        try {
            Set<String> tenants = tenantService.getActiveTenants();
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "success");
            result.put("data", tenants);
            result.put("count", tenants.size());
            return Mono.just(result);
        } catch (Exception e) {
            log.error("获取租户列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "获取租户列表失败: " + e.getMessage());
            return Mono.just(result);
        }
    }

    /**
     * 获取租户配置
     */
    @GetMapping("/tenants/{tenantId}/config")
    public Mono<Map<String, Object>> getTenantConfig(@PathVariable String tenantId) {
        try {
            TenantService.TenantConfig config = tenantService.getTenantConfig(tenantId);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "success");
            result.put("data", config);
            return Mono.just(result);
        } catch (Exception e) {
            log.error("获取租户配置失败: {}", tenantId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "获取租户配置失败: " + e.getMessage());
            return Mono.just(result);
        }
    }

    /**
     * 验证租户
     */
    @GetMapping("/tenants/{tenantId}/validate")
    public Mono<Map<String, Object>> validateTenant(@PathVariable String tenantId) {
        try {
            boolean valid = tenantService.validateTenant(tenantId);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "success");
            result.put("data", Map.of(
                "tenantId", tenantId,
                "valid", valid
            ));
            return Mono.just(result);
        } catch (Exception e) {
            log.error("验证租户失败: {}", tenantId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "验证租户失败: " + e.getMessage());
            return Mono.just(result);
        }
    }

    /**
     * 路由测试
     */
    @GetMapping("/test/route")
    public Mono<Map<String, Object>> testRoute(
            @RequestParam(required = false) String tenantId,
            @RequestParam(required = false) String service) {

        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "路由测试成功");
        result.put("data", Map.of(
            "tenantId", tenantId != null ? tenantId : "未指定",
            "service", service != null ? service : "未指定",
            "timestamp", LocalDateTime.now(),
            "routePattern", "/{tenantId}/{service}/**"
        ));

        return Mono.just(result);
    }

    /**
     * 获取网关性能指标
     */
    @GetMapping("/metrics")
    public Mono<Map<String, Object>> getMetrics(
            @RequestParam(required = false) String tenantId,
            @RequestParam(required = false) String service,
            @RequestParam(defaultValue = "1") int hours) {

        // 这里应该从Redis中获取实际的指标数据
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "success");
        result.put("data", Map.of(
            "totalRequests", 1000,
            "successRequests", 950,
            "errorRequests", 50,
            "avgResponseTime", 150,
            "maxResponseTime", 2000,
            "minResponseTime", 10,
            "timeRange", hours + "小时"
        ));

        return Mono.just(result);
    }

    /**
     * 获取熔断器状态
     */
    @GetMapping("/circuit-breaker/status")
    public Mono<Map<String, Object>> getCircuitBreakerStatus() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "success");
        result.put("data", Map.of(
            "circuitBreakers", Map.of(
                "wit-user", "CLOSED",
                "wit-product", "CLOSED",
                "wit-order", "HALF_OPEN"
            ),
            "timestamp", LocalDateTime.now()
        ));

        return Mono.just(result);
    }

    /**
     * 获取限流状态
     */
    @GetMapping("/rate-limit/status")
    public Mono<Map<String, Object>> getRateLimitStatus(
            @RequestParam(required = false) String tenantId) {

        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "success");
        result.put("data", Map.of(
            "tenantId", tenantId != null ? tenantId : "all",
            "currentRequests", 45,
            "limitPerMinute", 100,
            "remainingRequests", 55,
            "resetTime", LocalDateTime.now().plusMinutes(1)
        ));

        return Mono.just(result);
    }
}

package com.wit.gateway.predicate;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.handler.predicate.AbstractRoutePredicateFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;

import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;
import java.util.regex.Pattern;

/**
 * 租户路由断言工厂
 * 用于匹配租户路由格式: /{tenantId}/{service}/**
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class TenantRoutePredicateFactory extends AbstractRoutePredicateFactory<TenantRoutePredicateFactory.Config> {

    /**
     * 租户路径匹配模式
     */
    private static final Pattern TENANT_PATH_PATTERN = Pattern.compile("^/(\\d+)/([a-zA-Z-]+)(/.*)?$");

    public TenantRoutePredicateFactory() {
        super(Config.class);
    }

    @Override
    public Predicate<ServerWebExchange> apply(Config config) {
        return exchange -> {
            String path = exchange.getRequest().getURI().getPath();
            
            // 检查是否匹配租户路由模式
            boolean matches = TENANT_PATH_PATTERN.matcher(path).matches();
            
            if (matches) {
                // 如果指定了服务列表，检查服务是否在列表中
                if (config.getServices() != null && !config.getServices().isEmpty()) {
                    String service = extractServiceFromPath(path);
                    matches = config.getServices().contains(service);
                }
                
                log.debug("租户路由断言 - 路径: {}, 匹配: {}", path, matches);
            }
            
            return matches;
        };
    }

    /**
     * 从路径中提取服务名
     */
    private String extractServiceFromPath(String path) {
        if (!StringUtils.hasText(path)) {
            return null;
        }
        
        java.util.regex.Matcher matcher = TENANT_PATH_PATTERN.matcher(path);
        if (matcher.matches()) {
            return matcher.group(2);
        }
        
        return null;
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList("services");
    }

    /**
     * 配置类
     */
    @Data
    public static class Config {
        /**
         * 支持的服务列表，为空表示支持所有服务
         */
        private List<String> services;
    }
}

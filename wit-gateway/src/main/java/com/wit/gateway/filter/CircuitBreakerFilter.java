package com.wit.gateway.filter;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.concurrent.TimeUnit;

/**
 * 熔断器过滤器
 * 实现服务熔断和降级功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CircuitBreakerFilter extends AbstractGatewayFilterFactory<CircuitBreakerFilter.Config> {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public CircuitBreakerFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            String serviceName = getServiceName(exchange);
            String tenantId = getTenantId(exchange);
            String circuitKey = buildCircuitKey(serviceName, tenantId);
            
            // 检查熔断状态
            return checkCircuitState(circuitKey, config)
                .flatMap(state -> {
                    if (state == CircuitState.OPEN) {
                        // 熔断器打开，直接返回降级响应
                        return handleCircuitOpen(exchange, config);
                    } else if (state == CircuitState.HALF_OPEN) {
                        // 半开状态，允许少量请求通过
                        return executeWithHalfOpen(exchange, chain, circuitKey, config);
                    } else {
                        // 熔断器关闭，正常执行
                        return executeWithMonitoring(exchange, chain, circuitKey, config);
                    }
                });
        };
    }

    /**
     * 检查熔断器状态
     */
    private Mono<CircuitState> checkCircuitState(String circuitKey, Config config) {
        return Mono.fromCallable(() -> {
            try {
                // 检查是否处于熔断状态
                String openKey = circuitKey + ":open";
                Boolean isOpen = redisTemplate.hasKey(openKey);
                
                if (isOpen != null && isOpen) {
                    // 检查是否可以进入半开状态
                    Long openTime = (Long) redisTemplate.opsForValue().get(openKey);
                    if (openTime != null && 
                        Instant.now().toEpochMilli() - openTime > config.getSleepWindow() * 1000) {
                        return CircuitState.HALF_OPEN;
                    }
                    return CircuitState.OPEN;
                }
                
                return CircuitState.CLOSED;
            } catch (Exception e) {
                log.error("检查熔断状态失败: {}", circuitKey, e);
                return CircuitState.CLOSED;
            }
        });
    }

    /**
     * 正常执行并监控
     */
    private Mono<Void> executeWithMonitoring(ServerWebExchange exchange, 
                                           org.springframework.cloud.gateway.filter.GatewayFilterChain chain,
                                           String circuitKey, Config config) {
        long startTime = System.currentTimeMillis();
        
        return chain.filter(exchange)
            .doOnSuccess(aVoid -> {
                // 请求成功
                recordSuccess(circuitKey, config);
            })
            .doOnError(throwable -> {
                // 请求失败
                recordFailure(circuitKey, config);
                checkAndOpenCircuit(circuitKey, config);
            })
            .timeout(java.time.Duration.ofMillis(config.getTimeout()))
            .doOnError(java.util.concurrent.TimeoutException.class, ex -> {
                // 超时也算失败
                recordFailure(circuitKey, config);
                checkAndOpenCircuit(circuitKey, config);
            });
    }

    /**
     * 半开状态执行
     */
    private Mono<Void> executeWithHalfOpen(ServerWebExchange exchange,
                                         org.springframework.cloud.gateway.filter.GatewayFilterChain chain,
                                         String circuitKey, Config config) {
        return chain.filter(exchange)
            .doOnSuccess(aVoid -> {
                // 半开状态下请求成功，关闭熔断器
                closeCircuit(circuitKey);
                log.info("熔断器关闭: {}", circuitKey);
            })
            .doOnError(throwable -> {
                // 半开状态下请求失败，重新打开熔断器
                openCircuit(circuitKey);
                log.warn("熔断器重新打开: {}", circuitKey);
            })
            .timeout(java.time.Duration.ofMillis(config.getTimeout()))
            .doOnError(java.util.concurrent.TimeoutException.class, ex -> {
                openCircuit(circuitKey);
            });
    }

    /**
     * 处理熔断器打开状态
     */
    private Mono<Void> handleCircuitOpen(ServerWebExchange exchange, Config config) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.SERVICE_UNAVAILABLE);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        response.getHeaders().add("X-Circuit-Breaker", "OPEN");
        
        String fallbackResponse = config.getFallbackResponse();
        if (fallbackResponse == null || fallbackResponse.isEmpty()) {
            fallbackResponse = String.format(
                "{\"code\":503,\"message\":\"服务暂时不可用，请稍后重试\",\"timestamp\":%d}",
                System.currentTimeMillis()
            );
        }
        
        log.warn("熔断器打开，返回降级响应: {}", exchange.getRequest().getURI().getPath());
        
        return response.writeWith(
            Mono.just(response.bufferFactory().wrap(fallbackResponse.getBytes()))
        );
    }

    /**
     * 记录成功
     */
    private void recordSuccess(String circuitKey, Config config) {
        try {
            String successKey = circuitKey + ":success";
            redisTemplate.opsForValue().increment(successKey);
            redisTemplate.expire(successKey, config.getStatisticsWindow(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("记录成功失败: {}", circuitKey, e);
        }
    }

    /**
     * 记录失败
     */
    private void recordFailure(String circuitKey, Config config) {
        try {
            String failureKey = circuitKey + ":failure";
            redisTemplate.opsForValue().increment(failureKey);
            redisTemplate.expire(failureKey, config.getStatisticsWindow(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("记录失败失败: {}", circuitKey, e);
        }
    }

    /**
     * 检查并打开熔断器
     */
    private void checkAndOpenCircuit(String circuitKey, Config config) {
        try {
            String successKey = circuitKey + ":success";
            String failureKey = circuitKey + ":failure";
            
            Long successCount = (Long) redisTemplate.opsForValue().get(successKey);
            Long failureCount = (Long) redisTemplate.opsForValue().get(failureKey);
            
            if (successCount == null) successCount = 0L;
            if (failureCount == null) failureCount = 0L;
            
            long totalCount = successCount + failureCount;
            
            if (totalCount >= config.getMinimumRequests()) {
                double failureRate = (double) failureCount / totalCount;
                if (failureRate >= config.getFailureThreshold()) {
                    openCircuit(circuitKey);
                    log.warn("熔断器打开: {}, 失败率: {:.2f}%, 总请求: {}", 
                        circuitKey, failureRate * 100, totalCount);
                }
            }
        } catch (Exception e) {
            log.error("检查熔断状态失败: {}", circuitKey, e);
        }
    }

    /**
     * 打开熔断器
     */
    private void openCircuit(String circuitKey) {
        try {
            String openKey = circuitKey + ":open";
            redisTemplate.opsForValue().set(openKey, Instant.now().toEpochMilli(), 
                300, TimeUnit.SECONDS); // 5分钟后自动清理
        } catch (Exception e) {
            log.error("打开熔断器失败: {}", circuitKey, e);
        }
    }

    /**
     * 关闭熔断器
     */
    private void closeCircuit(String circuitKey) {
        try {
            String openKey = circuitKey + ":open";
            String successKey = circuitKey + ":success";
            String failureKey = circuitKey + ":failure";
            
            redisTemplate.delete(openKey);
            redisTemplate.delete(successKey);
            redisTemplate.delete(failureKey);
        } catch (Exception e) {
            log.error("关闭熔断器失败: {}", circuitKey, e);
        }
    }

    /**
     * 构建熔断器key
     */
    private String buildCircuitKey(String serviceName, String tenantId) {
        return String.format("circuit_breaker:%s:%s", serviceName, tenantId);
    }

    /**
     * 获取服务名
     */
    private String getServiceName(ServerWebExchange exchange) {
        String serviceName = exchange.getRequest().getHeaders().getFirst("X-Service-Name");
        if (serviceName != null) {
            return serviceName;
        }
        
        String path = exchange.getRequest().getURI().getPath();
        if (path.matches("^/\\d+/([a-zA-Z-]+)/.*")) {
            return path.split("/")[2];
        }
        
        return "unknown";
    }

    /**
     * 获取租户ID
     */
    private String getTenantId(ServerWebExchange exchange) {
        String tenantId = exchange.getRequest().getHeaders().getFirst("X-Tenant-Id");
        if (tenantId != null) {
            return tenantId;
        }
        
        String path = exchange.getRequest().getURI().getPath();
        if (path.matches("^/(\\d+)/.*")) {
            return path.split("/")[1];
        }
        
        return "unknown";
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 200;
    }

    /**
     * 熔断器状态枚举
     */
    private enum CircuitState {
        CLOSED,    // 关闭
        OPEN,      // 打开
        HALF_OPEN  // 半开
    }

    /**
     * 配置类
     */
    @Data
    public static class Config {
        /**
         * 失败阈值（0.0-1.0）
         */
        private double failureThreshold = 0.5;
        
        /**
         * 最小请求数
         */
        private int minimumRequests = 10;
        
        /**
         * 统计窗口时间（秒）
         */
        private int statisticsWindow = 60;
        
        /**
         * 熔断器打开后的等待时间（秒）
         */
        private int sleepWindow = 30;
        
        /**
         * 请求超时时间（毫秒）
         */
        private long timeout = 5000;
        
        /**
         * 降级响应内容
         */
        private String fallbackResponse;
        
        /**
         * 是否启用
         */
        private boolean enabled = true;
    }
}

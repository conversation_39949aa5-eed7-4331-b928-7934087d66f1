package com.wit.gateway.filter;

import com.wit.gateway.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 多租户路由过滤器
 * 支持路径格式: /{tenantId}/{service}/**
 * 例如: /3921/user/api/v1/users -> 转发到 wit-user 服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class TenantRouteFilter extends AbstractGatewayFilterFactory<TenantRouteFilter.Config> {

    @Autowired
    private TenantService tenantService;

    /**
     * 租户路径匹配模式: /{tenantId}/{service}/**
     */
    private static final Pattern TENANT_PATH_PATTERN = Pattern.compile("^/(\\d+)/([a-zA-Z-]+)(/.*)?$");
    
    /**
     * 支持的服务列表
     */
    private static final List<String> SUPPORTED_SERVICES = Arrays.asList(
        "auth", "user", "product", "order", "cart", "payment", 
        "inventory", "marketing", "search", "recommendation", 
        "review", "notification", "file", "system", "schedule", 
        "analytics", "aftersales"
    );

    public TenantRouteFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            String path = request.getURI().getPath();
            
            log.debug("处理请求路径: {}", path);
            
            // 解析租户ID和服务名
            TenantRouteInfo routeInfo = parseTenantRoute(path);
            if (routeInfo == null) {
                log.warn("无效的租户路由路径: {}", path);
                return handleError(exchange, HttpStatus.BAD_REQUEST, "无效的路由路径");
            }
            
            // 验证租户是否有效
            if (!tenantService.validateTenant(routeInfo.getTenantId())) {
                log.warn("无效的租户: {}", routeInfo.getTenantId());
                return handleError(exchange, HttpStatus.FORBIDDEN, "租户无效或已禁用");
            }

            // 验证服务是否支持
            if (!SUPPORTED_SERVICES.contains(routeInfo.getService())) {
                log.warn("不支持的服务: {}", routeInfo.getService());
                return handleError(exchange, HttpStatus.NOT_FOUND, "服务不存在");
            }
            
            // 构建新的请求路径（移除租户ID）
            String newPath = routeInfo.getServicePath();
            if (!StringUtils.hasText(newPath)) {
                newPath = "/";
            }
            
            // 添加租户信息到请求头
            ServerHttpRequest modifiedRequest = request.mutate()
                .path(newPath)
                .header("X-Tenant-Id", routeInfo.getTenantId())
                .header("X-Service-Name", routeInfo.getService())
                .header("X-Original-Path", path)
                .build();
            
            // 修改请求URI
            ServerWebExchange modifiedExchange = exchange.mutate()
                .request(modifiedRequest)
                .build();
            
            log.info("租户路由转换: {} -> {} (租户: {}, 服务: {})", 
                path, newPath, routeInfo.getTenantId(), routeInfo.getService());
            
            return chain.filter(modifiedExchange);
        };
    }

    /**
     * 解析租户路由信息
     */
    private TenantRouteInfo parseTenantRoute(String path) {
        if (!StringUtils.hasText(path)) {
            return null;
        }
        
        Matcher matcher = TENANT_PATH_PATTERN.matcher(path);
        if (!matcher.matches()) {
            return null;
        }
        
        String tenantId = matcher.group(1);
        String service = matcher.group(2);
        String servicePath = matcher.group(3);
        
        return new TenantRouteInfo(tenantId, service, servicePath);
    }

    /**
     * 处理错误响应
     */
    private Mono<Void> handleError(ServerWebExchange exchange, HttpStatus status, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        
        String errorBody = String.format(
            "{\"code\":%d,\"message\":\"%s\",\"timestamp\":\"%s\"}", 
            status.value(), message, System.currentTimeMillis()
        );
        
        return response.writeWith(
            Mono.just(response.bufferFactory().wrap(errorBody.getBytes()))
        );
    }



    /**
     * 配置类
     */
    public static class Config {
        // 可以添加配置参数
    }

    /**
     * 租户路由信息
     */
    private static class TenantRouteInfo {
        private final String tenantId;
        private final String service;
        private final String servicePath;

        public TenantRouteInfo(String tenantId, String service, String servicePath) {
            this.tenantId = tenantId;
            this.service = service;
            this.servicePath = servicePath;
        }

        public String getTenantId() {
            return tenantId;
        }

        public String getService() {
            return service;
        }

        public String getServicePath() {
            return servicePath;
        }
    }
}

package com.wit.gateway.filter;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;

/**
 * 限流过滤器
 * 支持基于租户、IP、用户的多维度限流
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RateLimitFilter extends AbstractGatewayFilterFactory<RateLimitFilter.Config> {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * Lua脚本实现滑动窗口限流
     */
    private static final String RATE_LIMIT_SCRIPT = 
        "local key = KEYS[1]\n" +
        "local window = tonumber(ARGV[1])\n" +
        "local limit = tonumber(ARGV[2])\n" +
        "local current = tonumber(ARGV[3])\n" +
        "\n" +
        "-- 清理过期的记录\n" +
        "redis.call('zremrangebyscore', key, 0, current - window * 1000)\n" +
        "\n" +
        "-- 获取当前窗口内的请求数\n" +
        "local currentCount = redis.call('zcard', key)\n" +
        "\n" +
        "if currentCount < limit then\n" +
        "    -- 添加当前请求\n" +
        "    redis.call('zadd', key, current, current)\n" +
        "    redis.call('expire', key, window + 1)\n" +
        "    return {1, limit - currentCount - 1}\n" +
        "else\n" +
        "    return {0, 0}\n" +
        "end";

    private final DefaultRedisScript<List> rateLimitScript;

    public RateLimitFilter() {
        super(Config.class);
        this.rateLimitScript = new DefaultRedisScript<>();
        this.rateLimitScript.setScriptText(RATE_LIMIT_SCRIPT);
        this.rateLimitScript.setResultType(List.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            String tenantId = getTenantId(exchange);
            String clientIp = getClientIp(exchange);
            
            // 构建限流key
            String rateLimitKey = buildRateLimitKey(config.getKeyType(), tenantId, clientIp);
            
            // 执行限流检查
            return checkRateLimit(rateLimitKey, config)
                .flatMap(allowed -> {
                    if (allowed) {
                        return chain.filter(exchange);
                    } else {
                        return handleRateLimitExceeded(exchange, config);
                    }
                });
        };
    }

    /**
     * 检查限流
     */
    private Mono<Boolean> checkRateLimit(String key, Config config) {
        return Mono.fromCallable(() -> {
            try {
                long current = Instant.now().toEpochMilli();
                List<Long> result = (List<Long>) redisTemplate.execute(
                    rateLimitScript,
                    Arrays.asList(key),
                    config.getWindowSize(),
                    config.getLimit(),
                    current
                );
                
                boolean allowed = result.get(0) == 1L;
                long remaining = result.get(1);
                
                log.debug("限流检查 - Key: {}, 允许: {}, 剩余: {}", key, allowed, remaining);
                
                return allowed;
            } catch (Exception e) {
                log.error("限流检查失败: {}", key, e);
                // 限流检查失败时，默认允许通过
                return true;
            }
        });
    }

    /**
     * 构建限流key
     */
    private String buildRateLimitKey(String keyType, String tenantId, String clientIp) {
        String baseKey = "rate_limit:";
        
        switch (keyType.toLowerCase()) {
            case "tenant":
                return baseKey + "tenant:" + tenantId;
            case "ip":
                return baseKey + "ip:" + clientIp;
            case "tenant_ip":
                return baseKey + "tenant_ip:" + tenantId + ":" + clientIp;
            default:
                return baseKey + "global";
        }
    }

    /**
     * 获取租户ID
     */
    private String getTenantId(ServerWebExchange exchange) {
        // 从请求头获取
        String tenantId = exchange.getRequest().getHeaders().getFirst("X-Tenant-Id");
        if (tenantId != null) {
            return tenantId;
        }
        
        // 从路径解析
        String path = exchange.getRequest().getURI().getPath();
        if (path.matches("^/\\d+/.*")) {
            return path.split("/")[1];
        }
        
        return "unknown";
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(ServerWebExchange exchange) {
        String xForwardedFor = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return exchange.getRequest().getRemoteAddress() != null ? 
            exchange.getRequest().getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }

    /**
     * 处理限流超出
     */
    private Mono<Void> handleRateLimitExceeded(ServerWebExchange exchange, Config config) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        response.getHeaders().add("X-RateLimit-Limit", String.valueOf(config.getLimit()));
        response.getHeaders().add("X-RateLimit-Window", String.valueOf(config.getWindowSize()));
        
        String errorBody = String.format(
            "{\"code\":429,\"message\":\"请求过于频繁，请稍后再试\",\"limit\":%d,\"window\":%d,\"timestamp\":%d}",
            config.getLimit(), config.getWindowSize(), System.currentTimeMillis()
        );
        
        return response.writeWith(
            Mono.just(response.bufferFactory().wrap(errorBody.getBytes()))
        );
    }



    /**
     * 配置类
     */
    @Data
    public static class Config {
        /**
         * 限流类型: tenant(租户), ip(IP), tenant_ip(租户+IP), global(全局)
         */
        private String keyType = "tenant";
        
        /**
         * 时间窗口大小（秒）
         */
        private int windowSize = 60;
        
        /**
         * 限制数量
         */
        private int limit = 1000;
        
        /**
         * 是否启用
         */
        private boolean enabled = true;
    }
}

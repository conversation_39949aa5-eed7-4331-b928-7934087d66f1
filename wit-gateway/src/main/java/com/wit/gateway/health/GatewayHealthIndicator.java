package com.wit.gateway.health;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网关健康检查指示器
 * 检查网关及其依赖服务的健康状态
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class GatewayHealthIndicator implements HealthIndicator {

    @Autowired
    private DiscoveryClient discoveryClient;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Health health() {
        try {
            Map<String, Object> details = new HashMap<>();
            boolean isHealthy = true;

            // 检查服务发现
            boolean nacosHealthy = checkNacosHealth(details);
            if (!nacosHealthy) {
                isHealthy = false;
            }

            // 检查Redis连接
            boolean redisHealthy = checkRedisHealth(details);
            if (!redisHealthy) {
                isHealthy = false;
            }

            // 检查下游服务
            boolean servicesHealthy = checkDownstreamServices(details);
            if (!servicesHealthy) {
                isHealthy = false;
            }

            // 添加网关统计信息
            addGatewayStats(details);

            if (isHealthy) {
                return Health.up()
                    .withDetails(details)
                    .build();
            } else {
                return Health.down()
                    .withDetails(details)
                    .build();
            }

        } catch (Exception e) {
            log.error("网关健康检查失败", e);
            return Health.down()
                .withException(e)
                .build();
        }
    }

    /**
     * 检查Nacos服务发现健康状态
     */
    private boolean checkNacosHealth(Map<String, Object> details) {
        try {
            List<String> services = discoveryClient.getServices();
            details.put("nacos.status", "UP");
            details.put("nacos.services.count", services.size());
            details.put("nacos.services.list", services);
            return true;
        } catch (Exception e) {
            details.put("nacos.status", "DOWN");
            details.put("nacos.error", e.getMessage());
            log.warn("Nacos健康检查失败", e);
            return false;
        }
    }

    /**
     * 检查Redis连接健康状态
     */
    private boolean checkRedisHealth(Map<String, Object> details) {
        try {
            String pong = redisTemplate.getConnectionFactory()
                .getConnection()
                .ping();
            
            details.put("redis.status", "UP");
            details.put("redis.ping", pong);
            
            // 检查Redis中的关键数据
            long tenantCount = redisTemplate.keys("tenant:*").size();
            long sessionCount = redisTemplate.keys("user:session:*").size();
            
            details.put("redis.tenants.count", tenantCount);
            details.put("redis.sessions.count", sessionCount);
            
            return true;
        } catch (Exception e) {
            details.put("redis.status", "DOWN");
            details.put("redis.error", e.getMessage());
            log.warn("Redis健康检查失败", e);
            return false;
        }
    }

    /**
     * 检查下游服务健康状态
     */
    private boolean checkDownstreamServices(Map<String, Object> details) {
        try {
            Map<String, Object> servicesStatus = new HashMap<>();
            boolean allHealthy = true;

            // 检查关键服务
            String[] criticalServices = {"wit-auth", "wit-user", "wit-product", "wit-order"};
            
            for (String serviceName : criticalServices) {
                try {
                    List<org.springframework.cloud.client.ServiceInstance> instances = 
                        discoveryClient.getInstances(serviceName);
                    
                    if (instances.isEmpty()) {
                        servicesStatus.put(serviceName, "DOWN - No instances");
                        allHealthy = false;
                    } else {
                        servicesStatus.put(serviceName, "UP - " + instances.size() + " instances");
                    }
                } catch (Exception e) {
                    servicesStatus.put(serviceName, "ERROR - " + e.getMessage());
                    allHealthy = false;
                }
            }

            details.put("downstream.services", servicesStatus);
            details.put("downstream.status", allHealthy ? "UP" : "DEGRADED");
            
            return allHealthy;
        } catch (Exception e) {
            details.put("downstream.status", "DOWN");
            details.put("downstream.error", e.getMessage());
            log.warn("下游服务健康检查失败", e);
            return false;
        }
    }

    /**
     * 添加网关统计信息
     */
    private void addGatewayStats(Map<String, Object> details) {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // JVM信息
            Runtime runtime = Runtime.getRuntime();
            stats.put("jvm.memory.total", runtime.totalMemory());
            stats.put("jvm.memory.free", runtime.freeMemory());
            stats.put("jvm.memory.used", runtime.totalMemory() - runtime.freeMemory());
            stats.put("jvm.memory.max", runtime.maxMemory());
            stats.put("jvm.processors", runtime.availableProcessors());
            
            // 系统信息
            stats.put("system.uptime", System.currentTimeMillis());
            stats.put("system.timestamp", System.currentTimeMillis());
            
            details.put("gateway.stats", stats);
            
        } catch (Exception e) {
            log.warn("获取网关统计信息失败", e);
            details.put("gateway.stats.error", e.getMessage());
        }
    }
}

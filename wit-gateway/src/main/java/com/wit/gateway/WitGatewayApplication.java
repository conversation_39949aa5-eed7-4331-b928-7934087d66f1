package com.wit.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 网关服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
public class WitGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(WitGatewayApplication.class, args);
        System.out.println("🚀 Wit Mall Gateway 启动成功！");
        System.out.println("📖 API文档地址: http://localhost:8080/doc.html");
        System.out.println("🏢 多租户网关路由已启用");
    }
}

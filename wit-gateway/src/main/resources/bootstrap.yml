spring:
  application:
    name: wit-gateway
  profiles:
    active: prod
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d  # wit 命名空间ID
        group: DEFAULT_GROUP
        cluster-name: wit-cluster
        metadata:
          version: 1.0.0
          zone: prod
          service-type: gateway
      config:
        server-addr: localhost:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
        # 引用Nacos中的配置文件
        extension-configs:
          - data-id: wit-jwt-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: wit-gateway-core.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: wit-gateway-routes.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: wit-gateway-security.yml
            group: DEFAULT_GROUP
            refresh: true
        # 引用公共配置
        shared-configs:
          - data-id: common-logging.yml
            group: COMMON_GROUP
            refresh: true
# 注意：网关的具体配置（路由、安全等）现在都在 Nacos 中管理
# 本地只保留最基本的配置，其他配置从 Nacos 动态加载

# 本地开发配置 - 不依赖Nacos
server:
  port: 8080

spring:
  application:
    name: wit-gateway
  profiles:
    active: local
  
  # 禁用Nacos配置
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
    
    # 网关配置
    gateway:
      discovery:
        locator:
          enabled: false
      
      # 路由配置
      routes:
        # 健康检查路由
        - id: health-check
          uri: no://op
          predicates:
            - Path=/gateway/health,/actuator/health
        
        # 网关信息路由
        - id: gateway-info
          uri: no://op
          predicates:
            - Path=/gateway/info,/gateway/tenants/**,/gateway/test/**
        
        # 认证服务路由
        - id: auth-service
          uri: http://localhost:8081
          predicates:
            - Path=/*/auth/**
          filters:
            - StripPrefix=1
        
        # 用户服务路由
        - id: user-service
          uri: http://localhost:8082
          predicates:
            - Path=/*/user/**
          filters:
            - StripPrefix=1
        
        # 商品服务路由
        - id: product-service
          uri: http://localhost:8083
          predicates:
            - Path=/*/product/**
          filters:
            - StripPrefix=1
        
        # 订单服务路由
        - id: order-service
          uri: http://localhost:8084
          predicates:
            - Path=/*/order/**
          filters:
            - StripPrefix=1
      
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: false
            max-age: 3600
  data:
    redis:
          host: localhost
          port: 6379
          password:
          database: 0
          timeout: 10000ms
          lettuce:
            pool:
              max-active: 8
              max-wait: -1ms
              max-idle: 8
              min-idle: 0

# Redis配置（本地）

# JWT配置（本地）
wit:
  jwt:
    secret: wit-mall-local-secret-key-2024-very-long-secret-for-development
    expiration: 7200
    refresh-expiration: 604800
    issuer: wit-mall-local
    algorithm: HS256
    response-header: true
    header-name: Authorization
    token-prefix: "Bearer "
    tenant-header-name: X-Tenant-Id
  gateway: # 多租户配置
      tenant:
        validation-enabled: false  # 本地开发关闭租户验证
        cache-ttl: 3600
        header-name: X-Tenant-Id

      # 限流配置
      rate-limit:
        enabled: false  # 本地开发关闭限流
        default-limit: 1000
        default-window: 60

      # 熔断器配置
      circuit-breaker:
        enabled: false  # 本地开发关闭熔断器
        failure-threshold: 0.6
        minimum-requests: 20
        sleep-window: 30
        timeout: 5000

      # 认证配置
      auth:
        enabled: false  # 本地开发关闭认证
        exclude-paths:
          - /**  # 本地开发允许所有路径

      # 监控配置
      monitoring:
        enabled: true
        slow-request-threshold: 3000
        metrics-retention-hours: 24
        alert-enabled: false

# 网关配置

# 日志配置
logging:
  level:
    root: INFO
    com.wit: DEBUG
    org.springframework.cloud.gateway: DEBUG
    reactor.netty: INFO
    io.netty: WARN

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true

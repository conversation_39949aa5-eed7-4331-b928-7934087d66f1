# 注意：网关的主要配置在 bootstrap.yml 和 Nacos 中管理
# 这里只保留一些本地覆盖配置

# 基本日志配置（详细配置在 Nacos 中）
logging:
  level:
    root: INFO
    com.wit: INFO
    org.springframework.cloud.gateway: INFO
    reactor.netty: INFO
    io.netty: WARN

# 基本监控配置（详细配置在 Nacos 中）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    web:
      server:
        request:
          autotime:
            enabled: true
  prometheus:
    metrics:
      export:
        enabled: true

# 网关性能配置
wit:
  gateway:
    performance:
      max-connections: 1000
      connect-timeout-ms: 10000
      response-timeout-ms: 30000
      read-timeout-ms: 30000
      write-timeout-ms: 30000
      compression-enabled: true
      keep-alive-enabled: true

# Spring Cloud Gateway 配置
spring:
  cloud:
    gateway:
      # 全局过滤器配置
      default-filters:
        - name: RequestRateLimiter
          args:
            redis-rate-limiter.replenish-rate: 100
            redis-rate-limiter.burst-capacity: 200
            redis-rate-limiter.requested-tokens: 1
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: false
            max-age: 3600
      # HTTP客户端配置
      httpclient:
        connect-timeout: 10000
        response-timeout: 30s
        pool:
          type: elastic
          max-connections: 1000
          max-idle-time: 30s
          max-life-time: 10m
          acquire-timeout: 60s



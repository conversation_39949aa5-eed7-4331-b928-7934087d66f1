package com.wit.aftersales;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * AfterSales Service 启动类
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.wit.aftersales", "com.wit.common"})
@EnableDiscoveryClient
@EnableDubbo
public class AftersalesApplication {

    public static void main(String[] args) {
        SpringApplication.run(AftersalesApplication.class, args);
    }
}

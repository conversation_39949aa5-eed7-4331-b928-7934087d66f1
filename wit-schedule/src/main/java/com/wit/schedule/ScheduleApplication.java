package com.wit.schedule;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 定时任务服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.wit.schedule", "com.wit.common"})
@EnableDiscoveryClient
@EnableDubbo
public class ScheduleApplication {

    public static void main(String[] args) {
        SpringApplication.run(ScheduleApplication.class, args);
    }
}

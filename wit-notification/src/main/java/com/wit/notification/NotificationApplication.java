package com.wit.notification;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Notification Service 启动类
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.wit.notification", "com.wit.common"})
@EnableDiscoveryClient
@EnableDubbo
public class NotificationApplication {

    public static void main(String[] args) {
        SpringApplication.run(NotificationApplication.class, args);
    }
}

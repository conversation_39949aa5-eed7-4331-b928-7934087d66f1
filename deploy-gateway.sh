#!/bin/bash

# Wit Mall 多租户网关部署脚本
# 作者: Wit
# 版本: 1.0.0

set -e

echo "🚀 开始部署 Wit Mall 多租户网关..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查环境
check_environment() {
    log_step "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装，请先安装 JDK 17+"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装，请先安装 Maven 3.6+"
        exit 1
    fi
    
    log_info "环境检查通过"
}

# 启动基础设施
start_infrastructure() {
    log_step "启动基础设施服务..."
    
    cd docker
    
    # 启动基础服务
    docker-compose up -d mysql redis nacos
    
    log_info "等待基础服务启动..."
    sleep 30
    
    # 检查服务状态
    if ! docker-compose ps | grep -q "Up"; then
        log_error "基础服务启动失败"
        docker-compose logs
        exit 1
    fi
    
    log_info "基础设施服务启动成功"
    cd ..
}

# 编译项目
build_project() {
    log_step "编译项目..."
    
    # 清理并编译
    mvn clean compile -DskipTests -q
    
    if [ $? -ne 0 ]; then
        log_error "项目编译失败"
        exit 1
    fi
    
    log_info "项目编译成功"
}

# 启动网关服务
start_gateway() {
    log_step "启动网关服务..."
    
    cd wit-gateway
    
    # 后台启动网关
    nohup mvn spring-boot:run > ../logs/gateway.log 2>&1 &
    GATEWAY_PID=$!
    
    echo $GATEWAY_PID > ../logs/gateway.pid
    
    log_info "网关服务启动中，PID: $GATEWAY_PID"
    
    # 等待服务启动
    sleep 20
    
    # 检查服务是否启动成功
    if ! curl -s http://localhost:8080/gateway/health > /dev/null; then
        log_error "网关服务启动失败"
        cat ../logs/gateway.log
        exit 1
    fi
    
    log_info "网关服务启动成功"
    cd ..
}

# 启动Nginx
start_nginx() {
    log_step "启动Nginx..."
    
    cd docker
    docker-compose up -d nginx
    
    if [ $? -ne 0 ]; then
        log_error "Nginx启动失败"
        exit 1
    fi
    
    log_info "Nginx启动成功"
    cd ..
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 测试网关健康检查
    log_info "测试网关健康检查..."
    if curl -s http://localhost:8080/gateway/health | grep -q "UP"; then
        log_info "✅ 网关健康检查通过"
    else
        log_error "❌ 网关健康检查失败"
        exit 1
    fi
    
    # 测试多租户路由
    log_info "测试多租户路由..."
    if curl -s "http://localhost:8080/gateway/test/route?tenantId=3921&service=user" | grep -q "路由测试成功"; then
        log_info "✅ 多租户路由测试通过"
    else
        log_error "❌ 多租户路由测试失败"
        exit 1
    fi
    
    # 测试Nginx代理
    log_info "测试Nginx代理..."
    if curl -s http://localhost/gateway/health | grep -q "UP"; then
        log_info "✅ Nginx代理测试通过"
    else
        log_warn "⚠️  Nginx代理测试失败，请检查配置"
    fi
    
    log_info "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    log_step "部署信息"
    
    echo ""
    echo "🎉 Wit Mall 多租户网关部署成功！"
    echo ""
    echo "📋 服务信息:"
    echo "  - 网关服务: http://localhost:8080"
    echo "  - 健康检查: http://localhost:8080/gateway/health"
    echo "  - 网关信息: http://localhost:8080/gateway/info"
    echo "  - Nginx代理: http://localhost"
    echo ""
    echo "🏢 多租户路由格式:"
    echo "  - 直接访问: http://localhost:8080/{tenantId}/{service}/**"
    echo "  - Nginx代理: http://localhost/{tenantId}/{service}/**"
    echo "  - 示例: http://localhost/3921/user/api/v1/users"
    echo ""
    echo "🔧 管理接口:"
    echo "  - 租户列表: http://localhost:8080/gateway/tenants"
    echo "  - 租户验证: http://localhost:8080/gateway/tenants/{tenantId}/validate"
    echo "  - 路由测试: http://localhost:8080/gateway/test/route"
    echo ""
    echo "📊 监控地址:"
    echo "  - Nacos控制台: http://localhost:8848/nacos (nacos/nacos)"
    echo "  - 网关日志: tail -f logs/gateway.log"
    echo ""
}

# 主函数
main() {
    echo "🚀 Wit Mall 多租户网关部署脚本"
    echo "=================================="
    
    check_environment
    start_infrastructure
    build_project
    start_gateway
    start_nginx
    verify_deployment
    show_deployment_info
    
    log_info "部署完成！"
}

# 执行主函数
main "$@"

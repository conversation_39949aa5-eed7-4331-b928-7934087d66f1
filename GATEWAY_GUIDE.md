# 🚀 Wit Mall 多租户网关使用指南

## 📋 概述

Wit Mall 多租户网关是一个基于 Spring Cloud Gateway 的企业级API网关，支持多租户路由、负载均衡、熔断降级等功能。

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用       │───▶│     Nginx       │───▶│   API网关       │
│  (Web/Mobile)   │    │  (负载均衡)      │    │ (wit-gateway)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲                       │
                                │                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   租户管理       │    │   路由规则       │    │   微服务集群     │
│   (Redis)       │    │   (动态配置)     │    │  (17个服务)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 核心功能

### 1. 多租户路由
- **路由格式**: `/{tenantId}/{service}/**`
- **示例**: `/3921/user/api/v1/users`
- **支持服务**: auth, user, product, order, cart, payment 等17个服务

### 2. 智能路由
- 自动解析租户ID和服务名
- 动态路由到对应的微服务
- 支持负载均衡和故障转移

### 3. 租户隔离
- 请求头注入租户信息
- 租户配置缓存
- 租户级别的限流控制

## 🚀 快速开始

### 1. 环境要求
- JDK 17+
- Maven 3.6+
- Docker & Docker Compose
- Redis (用于租户配置缓存)

### 2. 部署网关

#### Linux/Mac 部署
```bash
# 给脚本执行权限
chmod +x deploy-gateway.sh

# 执行部署
./deploy-gateway.sh
```

#### Windows 部署
```cmd
# 执行部署脚本
deploy-gateway.bat
```

### 3. 验证部署
```bash
# 执行测试脚本
chmod +x test-gateway.sh
./test-gateway.sh
```

## 📡 API 接口

### 1. 健康检查
```http
GET /gateway/health
```

**响应示例**:
```json
{
  "status": "UP",
  "service": "wit-gateway",
  "timestamp": "2024-01-20T10:30:00",
  "version": "1.0.0"
}
```

### 2. 网关信息
```http
GET /gateway/info
```

### 3. 租户管理

#### 获取活跃租户列表
```http
GET /gateway/tenants
```

#### 验证租户
```http
GET /gateway/tenants/{tenantId}/validate
```

#### 获取租户配置
```http
GET /gateway/tenants/{tenantId}/config
```

### 4. 路由测试
```http
GET /gateway/test/route?tenantId=3921&service=user
```

## 🌐 路由规则

### 1. 多租户路由
```
请求: http://localhost/{tenantId}/{service}/**
转发: lb://wit-{service}/**

示例:
http://localhost/3921/user/api/v1/users
↓
lb://wit-user/api/v1/users
```

### 2. 传统API路由（向后兼容）
```
请求: http://localhost/api/{service}/**
转发: lb://wit-{service}/**

示例:
http://localhost/api/user/v1/users
↓
lb://wit-user/v1/users
```

## 🔧 配置说明

### 1. 网关配置 (application.yml)
```yaml
spring:
  cloud:
    gateway:
      routes:
        # 多租户路由自动配置
        - id: tenant-user
          uri: lb://wit-user
          predicates:
            - Path=/{tenantId}/user/**
          filters:
            - TenantRoute
            - StripPrefix=2
```

### 2. Nginx 配置
```nginx
# 多租户路由代理
location ~ ^/(\d+)/([a-zA-Z-]+)(/.*)?$ {
    proxy_pass http://wit-gateway/$1/$2$3;
    proxy_set_header X-Tenant-Id $1;
    proxy_set_header X-Service-Name $2;
}
```

## 🏢 多租户使用示例

### 1. 用户服务调用
```bash
# 租户3921的用户列表
curl http://localhost/3921/user/api/v1/users

# 租户5678的用户列表  
curl http://localhost/5678/user/api/v1/users
```

### 2. 商品服务调用
```bash
# 租户3921的商品列表
curl http://localhost/3921/product/api/v1/products

# 租户3921的商品详情
curl http://localhost/3921/product/api/v1/products/123
```

### 3. 订单服务调用
```bash
# 租户3921创建订单
curl -X POST http://localhost/3921/order/api/v1/orders \
  -H "Content-Type: application/json" \
  -d '{"productId": 123, "quantity": 2}'
```

## 🔍 监控和日志

### 1. 服务监控
- **Nacos控制台**: http://localhost:8848/nacos
- **网关健康检查**: http://localhost:8080/gateway/health
- **服务注册状态**: 在Nacos中查看各服务状态

### 2. 日志查看
```bash
# 网关日志
tail -f logs/gateway.log

# Nginx访问日志
tail -f docker/nginx/logs/access.log

# Docker服务日志
docker-compose logs -f wit-gateway
```

## 🛠️ 故障排除

### 1. 网关启动失败
```bash
# 检查端口占用
netstat -tlnp | grep 8080

# 检查Nacos连接
curl http://localhost:8848/nacos/v1/ns/operator/metrics
```

### 2. 路由不生效
```bash
# 检查路由配置
curl http://localhost:8080/actuator/gateway/routes

# 测试路由规则
curl "http://localhost:8080/gateway/test/route?tenantId=3921&service=user"
```

### 3. 服务发现问题
```bash
# 检查服务注册
curl http://localhost:8848/nacos/v1/ns/instance/list?serviceName=wit-user
```

## 📈 性能优化

### 1. 连接池配置
```yaml
spring:
  cloud:
    gateway:
      httpclient:
        pool:
          max-connections: 1000
          max-idle-time: 30s
```

### 2. 缓存配置
```yaml
# Redis缓存配置
spring:
  redis:
    host: localhost
    port: 6379
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 200
        max-idle: 20
```

## 🔐 安全配置

### 1. CORS配置
网关已自动配置CORS支持，允许跨域访问。

### 2. 租户隔离
每个请求都会自动注入租户信息到请求头：
- `X-Tenant-Id`: 租户ID
- `X-Service-Name`: 目标服务名
- `X-Original-Path`: 原始请求路径

## 📞 技术支持

如有问题，请查看：
1. 项目日志文件
2. Nacos控制台服务状态
3. Docker容器运行状态
4. 网络连接和端口占用情况

---

🎉 **恭喜！你已经成功部署了 Wit Mall 多租户网关！**
